package main

import (
	"context"
	"time"

	"github.com/labstack/echo/v4"
	skyhubAdminPb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	skyhubBackendPb "gitlab.deepgate.io/apps/api/gen/go/skyhub/backend"
	skyhubWebPartnershipPb "gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/adapter/elasticsearch"
	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/adapter/rabbitmq"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/constants"
	httpEchoServer "gitlab.deepgate.io/apps/common/http_echo/server"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/logger"
	"gitlab.deepgate.io/apps/common/server"
	"gitlab.deepgate.io/apps/common/tracing"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"

	hotels "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

var kacp = keepalive.ClientParameters{
	Time:                15 * time.Second, // send pings every 10 seconds if there is no activity
	Timeout:             time.Second,      // wait 1 second for ping ack before considering the connection dead
	PermitWithoutStream: true,             // send pings even without active streams
}

func main() {
	// Load config.
	cfg, err := config.New()
	if err != nil {
		panic(err)
	}
	// Init DB connection.
	db, err := commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: cfg.WriteURL,
		ReadURL:  cfg.ReadURL,
		Database: cfg.MongoDB,
	})
	if err != nil {
		panic(err)
	}

	// Init CommonDB connection.
	commonDB, err := commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: cfg.CommonReadURL,
		ReadURL:  cfg.CommonReadURL,
		Database: cfg.CommonMongoDB,
	})
	if err != nil {
		panic(err)
	}

	var redisClient redis.IRedis
	if cfg.RedisSentinel {
		redisClient, err = redis.NewSentinel(
			redis.WithSentinelMasterName(cfg.RedisSentinelMasterName),
			redis.WithSentinelAddress(cfg.RedisAddress),
			redis.WithSentinelPassword(cfg.RedisPassword),
			redis.WithSentinelDatabase(cfg.RedisDatabase),
		)
	} else {
		redisClient, err = redis.New(
			redis.WithAddress(cfg.RedisAddress),
			redis.WithPassword(cfg.RedisPassword),
			redis.WithDatabase(cfg.RedisDatabase),
		)
	}

	if err != nil {
		logger.Fatal("Failed to connect to redis")
		panic(err)
	}

	var redisCommonClient redis.IRedis
	if cfg.RedisSentinel {
		redisCommonClient, err = redis.NewSentinel(
			redis.WithSentinelMasterName(cfg.RedisSentinelMasterName),
			redis.WithSentinelAddress(cfg.RedisAddress),
			redis.WithSentinelPassword(cfg.RedisPassword),
			redis.WithSentinelDatabase(cfg.RedisCommonDatabase),
		)
	} else {
		redisCommonClient, err = redis.New(
			redis.WithAddress(cfg.RedisAddress),
			redis.WithPassword(cfg.RedisPassword),
			redis.WithDatabase(cfg.RedisCommonDatabase),
		)
	}

	if err != nil {
		logger.Fatal("Failed to connect to redis")
		panic(err)
	}

	// mirgtations indexes db
	err = commonMongoDB.Migrate(context.Background(), cfg.WriteURL, cfg.MongoDB, cfg.Env)
	if err != nil {
		panic(err)
	}

	// Init elasticsearch
	es := elasticsearch.NewElasticsearch(&elasticsearch.Config{
		Address:  cfg.ElasticsearchAddress,
		Username: cfg.ElasticsearchUsername,
		Password: cfg.ElasticsearchPassword,
	},
		cfg.ElasticsearchIndexSuffix,
	)

	es.Init([]string{config.PlaceIndexName})

	// Init logger.
	log.Setup(cfg.Env, cfg.LogLevel)

	// Init tracer
	_, err = tracing.New(tracing.WithCommandAttributeDisabled(false), tracing.WithTraceFunctionArgs())
	if err != nil {
		log.Error("tracing.New: ", log.Any("error", err))
	}

	rabbit, err := rabbitmq.New(cfg.RabbitmqURL, cfg.RabbitmqSuffix, cfg.RabbitmqQueueType, constants.RabbitMQDeclarationFile)
	if err != nil {
		log.Fatal("Failed to connect rabbitMQ: ", log.Any("error", err))
	}

	conn, err := grpc.Dial(cfg.PartnerServiceEndpoint, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithKeepaliveParams(kacp))
	if err != nil {
		log.Fatal("connect error", log.Any("error", err), log.String("url", cfg.PartnerServiceEndpoint))
	}
	defer conn.Close()

	partnerClient := partner.NewPartnerClient(cfg, conn)
	requestRepo := repositories.NewRequestRepository(db, cfg)

	hotelsHTTPServer, bgJob, hotelsGRPCServer, consumer := hotels.New(cfg, db, commonDB, redisClient, partnerClient, es, rabbit)

	consumerClient := rabbitmq.NewConsumerClient(rabbit)
	consumerClient.RegisterHandler(consumer.GetHandlers())
	consumerClient.Consume()

	if !cfg.SkipJob {
		go func() {
			bgJob.Run()
		}()
	}

	closeSignal := make(chan struct{})
	go func() {
		// Start HTTP server.
		opts := []httpEchoServer.Option{
			httpEchoServer.ListenPort(cfg.HTTPPort),
			httpEchoServer.AuthKey(cfg.AuthSigningKey),
			httpEchoServer.PublicPaths(cfg.PublicPaths),
			httpEchoServer.WithEnv(cfg.Env),
			httpEchoServer.WithLogReq(),
			httpEchoServer.WithLogRes(),
			httpEchoServer.WithLogLevel(cfg.LogLevel),
			httpEchoServer.WithIgnoreLogResURLs([]string{"/search-ids", "/search-hotels", "/check-availability"}),
			httpEchoServer.WithLogSkipSensitiveData(cfg.Env != constants.ProductionEnvName),
			httpEchoServer.AllowedOrigins([]string{"*"}),
			httpEchoServer.WithSwaggerAPIPath("/skyhub-hotels"),
		}
		httpEchoServer.RunHTTPServer(opts, func(e *echo.Echo) {
			hotelsHTTPServer.Register(e, cfg, partnerClient, redisClient, requestRepo)
		}, redisClient)
		close(closeSignal)
	}()

	go func() {
		opts := []server.Option{
			server.ListenPort(cfg.Port),
			server.AuthKey(cfg.AuthSigningKey),
			server.InternalToken(cfg.InternalSecretToken),
			server.PublicMethods(cfg.PublicMethods),
			server.InternalMethods(cfg.InternalMethods),
			server.WithEnv(cfg.Env),
			server.WithLogReq(),
			server.WithLogRes(),
			server.WithLogLevel(cfg.LogLevel),
		}

		server.RunGRPCServer(opts, func(server *grpc.Server) {
			skyhubAdminPb.RegisterHotelAdminServiceServer(server, hotelsGRPCServer.AdminServer)
			skyhubAdminPb.RegisterAdminServiceServer(server, hotelsGRPCServer.AdminServer)
			skyhubBackendPb.RegisterHotelServiceServer(server, hotelsGRPCServer.BackendServer)
			skyhubWebPartnershipPb.RegisterHiddenFeeServiceServer(server, hotelsGRPCServer.WebPartnershipServer)
			skyhubWebPartnershipPb.RegisterHotelServiceServer(server, hotelsGRPCServer.WebPartnershipServer)
			skyhubWebPartnershipPb.RegisterCurrencyExchangeServiceServer(server, hotelsGRPCServer.WebPartnershipServer)
		}, redisCommonClient)
	}()

	<-closeSignal
}
