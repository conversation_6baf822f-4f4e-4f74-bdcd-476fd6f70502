# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

pkg/config/firebase-service-account.json
__debug*
# Ignore DevSpace cache and log folder
.devspace/
aggregate/output/
aggregate/output_vi/
aggregate/output_rh_review/
logs/
.vscode/launch.json
.env.prod
.env.test
.env


.air.toml
dist
modd.conf
/tmp

/**/.DS_Store
.DS_Store
/vna_client/**/docs/*

.golangci.yml

local.*
.golangci.yml