package helpers

import (
	"fmt"
	"time"
)

func ToUTCDateTime(unixMl int64) string {
	return time.UnixMilli(unixMl).UTC().Format(time.DateTime)
}

func ConvertToTime(seconds int) string {
	anHour := 3600
	aMin := 60
	hours := seconds / anHour
	minutes := (seconds % anHour) / aMin
	timeFormat := fmt.Sprintf("%02d:%02d", hours, minutes)

	return timeFormat
}

func ParseRealUTCToFakeTimeUTCLocal(in time.Time) time.Time {
	_, offset := in.Zone()
	return in.Add(time.Second * time.Duration(offset))
}

func ConvertToISO8601(date string, zone string) string {
	layout := "2006-01-02"
	var t time.Time

	if zone == "" {
		t, _ = time.Parse(layout, date)
	} else {
		loc, _ := time.LoadLocation(zone)
		t, _ = time.ParseInLocation(layout, date, loc)
	}

	// Bring to end of day
	t = t.Add(time.Hour*23 + time.Minute*59 + time.Second*59)

	return t.Format(time.RFC3339)
}

func ConvertToRFC3339(dateString string) string {
	_, err := time.Parse("2006-01-02T15:04:05", dateString)
	if err == nil {
		return dateString + "Z"
	}

	return dateString
}

func ConvertRFC3339Zone(dateString string, zone string) (string, error) {
	if dateString == "" {
		return "", nil
	}

	// Parse the input string in RFC3339 format
	t, err := time.Parse(time.RFC3339, dateString)
	if err != nil {
		return "", err
	}

	loc, err := time.LoadLocation(zone)
	if err != nil {
		return "", err
	}
	t = t.In(loc)

	// Format the time in the desired format
	return t.Format(time.RFC3339), nil
}
