package helpers

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"regexp"
	"strings"

	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
)

func GenerateBasicAuthHeader(username, password string) string {
	auth := fmt.Sprintf("%s:%s", username, password)
	encodedAuth := base64.StdEncoding.EncodeToString([]byte(auth))

	return fmt.Sprintf("Basic %s", encodedAuth)
}

func RemoveAccents(input string) string {
	newName, err := commonHelpers.RemoveAccents(input)
	if err != nil {
		log.Error("commonHelpers.RemoveAccents err", log.Any("err", err))
		return input
	}

	return newName
}

func Md5Hex(s string) string {
	hash := md5.Sum([]byte(s))         // Tính MD5 hash
	return hex.EncodeToString(hash[:]) // <PERSON><PERSON>ển thành dạng hex string
}

func SplitHTMLContentByTag(html string, tag string) []string {
	var result []string

	// Split the string by <li> tags
	parts := strings.Split(html, "<"+tag+">")

	// Process each part
	for _, part := range parts[1:] { // Skip the first part as it's before the first <li>
		// Find the end of the li content
		endIndex := strings.Index(part, "</"+tag+">")
		if endIndex != -1 {
			// Extract the content between <li> and </li>
			content := strings.TrimSpace(part[:endIndex])
			result = append(result, content)
		}
	}

	return result
}

func SplitIntoSentences(text string) []string {
	re := regexp.MustCompile(`(?i)([^.!?]+[.!?])`)
	matches := re.FindAllString(text, -1)

	var sentences []string

	for _, sentence := range matches {
		trimmed := strings.TrimSpace(sentence)
		if trimmed != "" {
			sentences = append(sentences, trimmed)
		}
	}

	return sentences
}

const (
	TagLi = "li"
)
