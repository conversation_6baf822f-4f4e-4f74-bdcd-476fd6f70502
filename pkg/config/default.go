package config

var defaultConfig = []byte(`
env: local
log_level: info
port: 3014
http_port: 8000
skyhub_hotels_service_endpoint: localhost:3014

write_url: *******************************************
read_url: *******************************************
common_read_url: *******************************************
rabbitmq_url: amqp://myuser:mypassword@localhost:5672
rabbitmq_suffix:
rabbitmq_queue_type: classic
auth_signing_key: xxxxxx
internal_secret_token: xxxxxx
mongo_db: peahoki-user
common_mongo_db: peahoki-user
common_mongo_db_des: xxx
common_des_read_url: xxx 
travel_agent_endpoint: xxx

job_scan_confirmation_id_schedule: 8,19

hnh_travel_agent_office_id: xxx
hnh_travel_agent_api_key: xxx
hnh_travel_agent_active_time: 0900:2400
hnh_travel_agent_partnership_id: xxx
hnh_travel_agent_skip_day_of_week: 6,0

bzt_travel_agent_office_id: xxx
bzt_travel_agent_api_key: xxx
bzt_travel_agent_active_time: 0900:2400
bzt_travel_agent_partnership_id: xxx
bzt_travel_agent_skip_day_of_week: 6,0

may_travel_agent_office_id: foiawjfoiew
may_travel_agent_api_key: fjowaijfpeoiwajfoepwa
may_travel_agent_active_time: 0900:2400
may_travel_agent_partnership_id: 663b329480f097962b1b50aa

public_paths:
- /

public_methods:
- /skyhub.admin.HotelAdminService/MigrateHotelNameContent
- /skyhub.admin.AdminService/AggregateHotelTransaction

internal_methods:
- /skyhub.backend.HotelService/UpdateOrderStatus

redis_address: localhost:6379,
redis_password: xxx,
redis_database: 0
redis_common_database: 0
redis_sentinel: false
redis_sentinel_master_name: mymaster

search_hotel_ctx_timeout: 90

partner_service_endpoint: partner-service:3000
airplane_service_endpoint: airplane-service:3000
partnership_service_endpoint: partnership-service:3000
price_service_endpoint: price-service:3000

decrypt_key: xxx

hub_partnership_id: 652618f6f511734dec28d649
order_service_endpoint: localhost:3007
wallet_service_endpoint: localhost:3012
payment_service_endpoint: localhost:3002
notification_service_endpoint: localhost:3010

telegram_url: https://api.telegram.org
telegram_bot_token: **********************************************

expedia_url: https://api.bsp.onlineairticket.vn
expedia_api_key: xx
expedia_shared_secret: xx

tour_mind_agent_code: xx
tour_mind_username: xx
tour_mind_password: xx
tour_mind_base_url: http://**************:7080
use_expedia_fake: false

telegram_manual_booking_chat_id: xxx
telegram_manual_booking_bot_token: xxx
telegram_booking_canceling_chat_id: -1002488020998
cron_job_pending_booking: 0 * * * *

rate_hawk_base_url: https://api.worldota.net
rate_hawk_username: xx
rate_hawk_password: xx

elasticsearch_username: xxx
elasticsearch_password: xxx
elasticsearch_address: http://127.0.0.1:9200
elasticsearch_index_suffix: xxx
webhook_transaction: http://hotel-service.tixlabs-dev.svc.cluster.local:8000/webhook/skyhub/transaction
content_version: '1'
enable_provider: rate-hawk
btm_token_url: https://staging-id.maysols.com
btm_webhook_url: https://staging-booking-api.maysols.com
btm_client_id: expedia-notification
btm_secret_key: dmZi3zHhlM
btm_scope: IdentityServerApi

ta_token_url: https://staging-id.maysols.com
ta_hotel_url: https://staging-sale-api.maysols.com
ta_booking_url: https://staging-booking-api.maysols.com
ta_client_id: deeptech-api
ta_secret_key: pvgWXHFVg8
ta_scope: IdentityServerApi
ta_webhook_key: ZY3PWXQBNbONLrGt

company_email: <EMAIL> 
hub_app_name: hub

rate_limit_max_requests: 1000
rate_limit_expire: 3600

ta_issuing_active_time: 0900:1600
skip_job: false

should_log_request_place: true

dida_basic_auth_username: xxx
dida_basic_auth_password: xxx
dida_api_url: https://apiint.didatravel.com/api
dida_res_format: json
contact_first_name: xxx
contact_last_name: xxx

sub_hub_mappings:
  travel-agency-001:
    hub_office_id: main-hub-office-123
    hub_api_key: main-hub-api-key-abc123
    sub_hub_partnership_id: 652618f6f511734dec28d649
  booking-platform-002:
    hub_office_id: main-hub-office-456
    hub_api_key: main-hub-api-key-def456
    sub_hub_partnership_id: 652618f6f511734dec28d650

agoda_booking_url: xxx
agoda_price_check_url: xxx
agoda_search_url: xxx
agoda_site_id: xxx
agoda_api_key: xxx
agoda_currency: USD
bzt_card_number: xxx
bzt_card_expiry_date: xxx
bzt_card_cvc: xxx
bzt_card_holder_name: xxx
agoda_payment_json: xxx
agoda_customer_detail: xxx
`)
