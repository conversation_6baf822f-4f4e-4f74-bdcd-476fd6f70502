package config

import (
	"errors"
	"fmt"
	"time"
)

// SubHubMapping định nghĩa mapping giữa SUB Hub key và Hub credentials.
type SubHubMapping struct {
	// Hub credentials (thêm vào request để forward đến MAIN Hub)
	HubOfficeID         string `json:"hub_office_id" validate:"required"`
	HubAPIKey           string `json:"hub_api_key" validate:"required"`
	SubHubPartnershipID string `json:"sub_hub_partnership_id" validate:"required"`
}

// SubHubContext contains SUB Hub request context information.
type SubHubContext struct {
	HubKey        string
	SubHubMapping *SubHubMapping
	CorrelationID string
	// Client headers to forward to main hub
	ClientHeaders *ClientHeaders
}

// ClientHeaders contains client information to forward.
type ClientHeaders struct {
	UserAgent string `json:"user_agent"`
	IP        string `json:"ip"`
	Country   string `json:"country"`
}

// SubHubMetrics contains metrics for monitoring.
type SubHubMetrics struct {
	RequestCount        int64     `json:"request_count"`
	ErrorCount          int64     `json:"error_count"`
	AverageResponseTime float64   `json:"average_response_time"`
	LastRequestTime     time.Time `json:"last_request_time"`
	LastErrorTime       time.Time `json:"last_error_time"`
}

// Validate validates SubHubMapping.
func (s *SubHubMapping) Validate() error {
	if s.HubOfficeID == "" {
		return ErrInvalidHubOfficeID
	}

	if s.HubAPIKey == "" {
		return ErrInvalidHubAPIKey
	}

	if s.SubHubPartnershipID == "" {
		return ErrInvalidHubPartnershipID
	}

	return nil
}

// GetCacheKey creates cache key for SUB Hub.
func (s *SubHubMapping) GetCacheKey(suffix string) string {
	return fmt.Sprintf("sub_hub:%s:%s", s.SubHubPartnershipID, suffix)
}

// SubHubConfig errors.
var (
	ErrInvalidHubOfficeID      = errors.New("invalid hub office ID")
	ErrInvalidHubAPIKey        = errors.New("invalid hub API key")
	ErrInvalidHubPartnershipID = errors.New("invalid hub partnership ID")
	ErrInvalidClientOfficeID   = errors.New("invalid client office ID")
	ErrInvalidClientAPIKey     = errors.New("invalid client API key")
	ErrSubHubNotFound          = errors.New("sub hub not found")
	ErrSubHubInactive          = errors.New("sub hub is inactive")
	ErrSubHubExpired           = errors.New("sub hub has expired")
	ErrIPNotAllowed            = errors.New("client IP not allowed")
	ErrRateLimitExceeded       = errors.New("rate limit exceeded")
)
