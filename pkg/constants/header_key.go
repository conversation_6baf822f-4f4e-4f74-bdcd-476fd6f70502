package constants

const (
	// Client headers.
	HeaderKeyClientBrowserAgent = "x-client-user-agent"
	HeaderKeyClientIP           = "x-client-ip"
	HeaderKeyClientCountryCode  = "x-client-country"

	// SUB Hub headers.
	HeaderHubKey                 = "x-hub-key"
	HeaderKeySubHubPartnershipID = "x-sub-hub-partnership-id"

	// Rate limit headers.
	HeaderKeyRateLimitLimit     = "x-RateLimit-Limit"
	HeaderKeyRateLimitRemaining = "x-RateLimit-Remaining"
	HeaderKeyRateLimitReset     = "x-RateLimit-Reset"

	// Correlation headers.
	HeaderKeyCorrelationID = "x-correlation-id"

	// Content type.
	HeaderKeyContentType = "Content-Type"

	// Context keys (cho middleware).
	SubHubContextKey   = "sub_hub_context"
	IsSubHubRequestKey = "is_sub_hub_request"
	CorrelationIDKey   = "correlation_id"
)
