package constants

import (
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

const UUIDLength = 36
const HotelIDSearchLimit = 250

const (
	ContextEnabledProvidersKey  = "EnabledProviders"
	ContextDefaultLanguage      = "DefaultLanguage"
	ContextPriceConditionConfig = "PriceConditionConfig"
	VNDCurrency                 = "VND"
	ContextWebhookKey           = "WebhookKey"
)

const ContentVersion = "240725"

// Request timeout
const (
	SessionExpireTime            = time.Minute * 25
	GoroutineContextTimeout      = time.Second * 5
	ClientRequestTimeout         = time.Second * 5
	ThirdPartyRequestTimeout     = time.Second * 30
	LongThirdPartyRequestTimeout = time.Minute

	CacheSearchResultTimeoutSecs = 120
	CachedSearchResultTimeout    = time.Second * CacheSearchResultTimeoutSecs
	DefaultCtxTimeout            = time.Second * 30
	RequestRepoCtxTimeout        = time.Second * 30
	SearchHotelCacheTime         = 2 * time.Hour
)

const (
	HCMTimezone                      = "Asia/Ho_Chi_Minh"
	HubSearchDateFormat              = "2006-01-02"
	HubDateFormat                    = "2006-01-02T15:04:05Z07:00" // ISO 8601
	HubDateFormatWithoutTz           = "2006-01-02T15:04:05"
	MaxRoomSearchAllowed             = 8
	HotelStayDayRange                = 29 * 24 * time.Hour  // 29 days
	HotelLimitDay                    = 365 * 24 * time.Hour // 365 days
	MaxChildrenAge                   = 17
	PendingHotelConfirmationDeadline = 72*time.Hour + 30*time.Minute
	TicketPendingDeadlineTime        = time.Minute * 19
)

const DefaultSearchDistance = 5000 // 5km

var HighLevelPlaceType = []enum.PlaceType{
	enum.PlaceTypeProvinceState,

	enum.PlaceTypeContinent,

	enum.PlaceTypeCity,

	enum.PlaceTypeCountry,

	enum.PlaceTypeMultiCityVicinity,

	enum.PlaceTypeHighLevelRegion,
}

var HotelCountryCodes = []string{
	"AB",
	"AD",
	"AE",
	"AF",
	"AG",
	"AI",
	"AL",
	"AM",
	"AO",
	"AR",
	"AS",
	"AT",
	"AU",
	"AW",
	"AZ",
	"BA",
	"BB",
	"BD",
	"BE",
	"BF",
	"BG",
	"BH",
	"BI",
	"BJ",
	"BL",
	"BM",
	"BN",
	"BO",
	"BQ",
	"BR",
	"BS",
	"BT",
	"BW",
	"BY",
	"BZ",
	"CA",
	"CD",
	"CF",
	"CG",
	"CH",
	"CI",
	"CK",
	"CL",
	"CM",
	"CN",
	"CO",
	"CR",
	"CU",
	"CV",
	"CW",
	"CY",
	"CZ",
	"DE",
	"DJ",
	"DK",
	"DM",
	"DO",
	"DZ",
	"EC",
	"EE",
	"EG",
	"ER",
	"ES",
	"ET",
	"FI",
	"FJ",
	"FK",
	"FM",
	"FO",
	"FR",
	"GA",
	"GB",
	"GD",
	"GE",
	"GH",
	"GI",
	"GL",
	"GM",
	"GN",
	"GQ",
	"GR",
	"GT",
	"GU",
	"GW",
	"GY",
	"HN",
	"HR",
	"HT",
	"HU",
	"ID",
	"IE",
	"IL",
	"IM",
	"IN",
	"IQ",
	"IR",
	"IS",
	"IT",
	"JM",
	"JO",
	"JP",
	"KE",
	"KG",
	"KH",
	"KI",
	"KM",
	"KN",
	"KR",
	"KW",
	"KY",
	"KZ",
	"LA",
	"LB",
	"LC",
	"LI",
	"LK",
	"LR",
	"LS",
	"LT",
	"LU",
	"LV",
	"LY",
	"MA",
	"MC",
	"MD",
	"ME",
	"MF",
	"MG",
	"MH",
	"MK",
	"ML",
	"MM",
	"MN",
	"MO",
	"MP",
	"MR",
	"MS",
	"MT",
	"MU",
	"MV",
	"MW",
	"MX",
	"MY",
	"MZ",
	"NA",
	"NC",
	"NE",
	"NG",
	"NI",
	"NL",
	"NO",
	"NP",
	"NR",
	"NU",
	"NZ",
	"OM",
	"OS",
	"PA",
	"PE",
	"PF",
	"PG",
	"PH",
	"PK",
	"PL",
	"PM",
	"PR",
	"PS",
	"PT",
	"PW",
	"PY",
	"QA",
	"RO",
	"RS",
	"RU",
	"RW",
	"SA",
	"SB",
	"SC",
	"SD",
	"SE",
	"SG",
	"SH",
	"SI",
	"SJ",
	"SK",
	"SL",
	"SM",
	"SN",
	"SO",
	"SR",
	"SS",
	"ST",
	"SV",
	"SX",
	"SY",
	"SZ",
	"TC",
	"TD",
	"TG",
	"TH",
	"TJ",
	"TL",
	"TM",
	"TN",
	"TO",
	"TR",
	"TT",
	"TW",
	"TZ",
	"UA",
	"UG",
	"US",
	"UY",
	"UZ",
	"VA",
	"VC",
	"VE",
	"VG",
	"VI",
	"VN",
	"VU",
	"WF",
	"WS",
	"XK",
	"YE",
	"ZA",
	"ZM",
	"ZW",

	"HK",
	"AX",
	"CC",
	"CX",
	"GF",
	"GG",
	"GP",
	"JE",
	"MQ",
	"NF",
	"RE",
	"YT",
}

var RegionCountryCodes = []string{
	"AF",
	"AL",
	"DZ",
	"AS",
	"AD",
	"AO",
	"AI",
	"AQ",
	"AG",
	"AR",
	"AM",
	"AW",
	"AU",
	"AT",
	"AZ",

	"BS",
	"BH",
	"BD",
	"BB",
	"BY",
	"BE",
	"BZ",
	"BJ",
	"BM",
	"BT",
	"BO",
	"BQ",
	"BA",
	"BW",
	"BV",

	"BR",
	"IO",
	"BN",
	"BG",
	"BF",
	"BI",
	"CV",
	"KH",
	"CM",
	"CA",
	"KY",
	"CF",
	"TD",
	"CL",
	"CN",

	"CX",
	"CC",
	"CO",
	"KM",
	"CD",
	"CG",
	"CK",
	"CR",
	"HR",
	"CU",
	"CW",
	"CY",
	"CZ",
	"CI",
	"DK",

	"DJ",
	"DM",
	"DO",
	"EC",
	"EG",
	"SV",
	"GQ",
	"ER",
	"EE",
	"SZ",
	"ET",
	"FK",
	"FO",
	"FJ",
	"FI",

	"FR",
	"GF",
	"PF",
	"TF",
	"GA",
	"GM",
	"GE",
	"DE",
	"GH",
	"GI",
	"GR",
	"GL",
	"GD",
	"GP",
	"GU",

	"GT",
	"GG",
	"GN",
	"GW",
	"GY",
	"HT",
	"HM",
	"VA",
	"HN",
	"HK",
	"HU",
	"IS",
	"IN",
	"ID",
	"IR",

	"IQ",
	"IE",
	"IM",
	"IL",
	"IT",
	"JM",
	"JP",
	"JE",
	"JO",
	"KZ",
	"KE",
	"KI",
	"KP",
	"KR",
	"KW",

	"KG",
	"LA",
	"LV",
	"LB",
	"LS",
	"LR",
	"LY",
	"LI",
	"LT",
	"LU",
	"MO",
	"MG",
	"MW",
	"MY",
	"MV",

	"ML",
	"MT",
	"MH",
	"MQ",
	"MR",
	"MU",
	"YT",
	"MX",
	"FM",
	"MD",
	"MC",
	"MN",
	"ME",
	"MS",
	"MA",

	"MZ",
	"MM",
	"NA",
	"NR",
	"NP",
	"NL",
	"NC",
	"NZ",
	"NI",
	"NE",
	"NG",
	"NU",
	"NF",
	"MP",
	"NO",

	"OM",
	"PK",
	"PW",
	"PS",
	"PA",
	"PG",
	"PY",
	"PE",
	"PH",
	"PN",
	"PL",
	"PT",
	"PR",
	"QA",
	"MK",

	"RO",
	"RU",
	"RW",
	"RE",
	"BL",
	"SH",
	"KN",
	"LC",
	"MF",
	"PM",
	"VC",
	"WS",
	"SM",
	"ST",
	"SA",

	"SN",
	"RS",
	"SC",
	"SL",
	"SG",
	"SX",
	"SK",
	"SI",
	"SB",
	"SO",
	"ZA",
	"GS",
	"SS",
	"ES",
	"LK",

	"SD",
	"SR",
	"SJ",
	"SE",
	"CH",
	"SY",
	"TW",
	"TJ",
	"TZ",
	"TH",
	"TL",
	"TG",
	"TK",
	"TO",
	"TT",

	"TN",
	"TR",
	"TM",
	"TC",
	"TV",
	"UG",
	"UA",
	"AE",
	"GB",
	"US",
	"UM",
	"UY",
	"UZ",
	"VU",
	"VE",

	"VG",
	"VI",
	"WF",
	"EH",
	"YE",
	"ZM",
	"ZW",
	"AX",
	"VN",
}

const (
	MongoRetryCount = 3
	MongoRetryDelay = 3 * time.Second
)
