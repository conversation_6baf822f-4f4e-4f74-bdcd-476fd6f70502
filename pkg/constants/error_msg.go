package constants

// Common error messages.
const (
	ErrMsgInvalidPayload          = "Invalid payload request."
	ErrMsgSearchKeyNotFound       = "Search key not found."
	ErrMsgHotelIDNotFound         = "Hotel id not found."
	ErrMsgMultiTripIsNotSupported = "Multi trip is not supported."
	ErrMsgInvalidSessionOrExpired = "Session is invalid or expired."
	ErrMsgAnotherRequestInprocess = "Another request is in processing."
)

// Itinerary errors.
const (
	ErrMsgINFMatchAdt  = "Number of infant must match adult."
	ErrMsgHotelSoldOut = "Hotel sold out."
)

// PNR.
const (
	ErrMsgPassengerQuantityNotMatch = "Quantity of passengers does not match."
	ErrMsgInvalidPassportExpiryDate = "Invalid passport expiry date."
	ErrMsgPNREmpty                  = "PNR info is empty."
	ErrMsgBookingConfirmed          = "Booking is comfirmed."
	ErrMsgPaxNameExistedInPNR       = "Duplicate names must not be entered for passengers."
)

// Booking.
const (
	ErrMsgBookingNotFound              = "Booking not found."
	ErrMsgIssueTicketError             = "Ticket issuance has failed, please contact support."
	ErrMsgTicketFareChanged            = "Ticket fare has been changed."
	ErrMsgTotalFareNotConfirmed        = "Booking total fare is not confirmed yet."
	ErrMsgBookingExisted               = "Booking is already created."
	ErrMsgBookingExpired               = "Booking has expired."
	ErrMsgTicketIssued                 = "Ticket is already issued."
	ErrMsgBookingCancelled             = "Booking was cancelled."
	ErrMsgPaxNameExistedInOtherBooking = "Pax name is existed in another booking."
	ErrMsgSeatNotAvailable             = "Seat not available"
	ErrMsgInsufficientBalance          = "Account has insufficient balance for requested action."
	ErrMsgTicketPending                = "Ticket is pending issuance."
)

const (
	WarnCodePriceChanged = "THE_PRICE_HAS_CHANGED"
	WarnMsgPriceChanged  = "The price has changed"

	WarnCodeCancelPolicyChanged = "THE_CANCELLATION_POLICY_HAS_BEEN_CHANGED"
	WarnMsgCancelPolicyChanged  = "The cancellation policy has been changed."

	WarnCodeRateInfoChanged = "RATE_INFO_HAS_BEEN_CHANGED"
	WarnMsgRateInfoChanged  = "The rate information has been changed."
)

var AppErrMsg = map[string]string{
	"Email email": "Invalid email format.",
}
