package constants

// DefaultCurrencyPrecision is the default precision used for currencies not listed in the map.
const DefaultCurrencyPrecision = 3

// CurrencyPrecisions defines the rounding precision for various currencies.
// A positive number `n` means rounding to `n` decimal places.
// A negative number `n` means rounding to the nearest 10^|n|.
// For example, -3 means rounding to the nearest 1000.
// 0 means rounding to the nearest integer.
var CurrencyPrecisions = map[string]int{
	"VND": -3,
	"KRW": 0,
}

// GetCurrencyPrecision returns the rounding precision for a given currency.
// It returns a default value if the currency is not found in the map.
func GetCurrencyPrecision(currency string) int {
	if precision, ok := CurrencyPrecisions[currency]; ok {
		return precision
	}

	return DefaultCurrencyPrecision
}
