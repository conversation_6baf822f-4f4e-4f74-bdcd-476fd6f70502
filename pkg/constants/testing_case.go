package constants

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

const (
	ActionOrderBookingForm         = "order_booking_form"
	ActionOrderBookingFinishStatus = "order_booking_finish_status"
	ActionCreateBooking            = "create_booking"
	ActionPriceCheck               = "price_check"
)

const (
	PendingStatus = "PENDING"
)

var TestCaseMap = map[commonEnum.HotelProvider]map[string]map[string]string{
	commonEnum.HotelProviderRateHawk: {
		ActionOrderBookingForm: {
			"EJYGzDap": "order_not_found",
			"LF7ExE7P": "sandbox_restriction",
			"WsymLpUL": "double_booking_form",
			"1PoGNOAb": "duplicate_reservation",
			"E3LKAuln": "hotel_not_found",
			"Kpkdju1W": "reservation_is_not_allowed",
			"aHciN0Pp": "rate_not_found",
			"GuFXNnpY": "contract_mismatch",
			"eihsieYG": "NOT DEFINED",
		},
		ActionOrderBookingFinishStatus: {
			"fvOPZLOs": "3ds",
			"7OshRCKk": "block",
			"uIVexwVR": "soldout",
			"he6XewcA": "book_limit",
			"HLyul0Dq": "not_allowed",
			"hOgwD0Hr": "provider",
			"gqWopXKO": "order_not_found",
			"hccx3rzU": "booking_finish_did_not_succeed",
			"kRcfW5S5": "decoding_json",
			"HOVLA4oQ": "endpoint_exceeded_limit",
			"qinVdrWS": "endpoint_not_active",
			"3jKFnaoE": "endpoint_not_found",
			"VYnch2LV": "incorrect_credentials",
			"g7ol3qlg": "invalid_auth_header",
			"m3Gvl2GQ": "invalid_params",
			"e7BzqoiP": "lock",
			"mr7eWpMv": "no_auth_header",
			"nzK56mp0": "overdue_debt",
			"8r0uIYql": "unexpected_method",
			"OoDUJvYb": "charge",
			"HeuFd8mG": "NOT DEFINED",
		},
	},
	commonEnum.HotelProviderExpedia: {
		ActionCreateBooking: {
			"FiL3rkgP": "400",
			"DXkfjQzP": "401",
			"goedwgxY": "403",
			"If6twPy6": "409",
			"6xfagsKX": "410",
			"iU8FXeel": "426",
			"HeuFd8mK": "NOT DEFINED",
		},
	},
	commonEnum.HotelProviderDida: {
		ActionCreateBooking: {
			"bkngUnxp9zA1":  "3000",
			"incBkngTgk42":  "3001",
			"refErrKslp93":  "3002",
			"bkngID9xWq12":  "3003",
			"cnclID4Lmns8":  "3004",
			"occpInfKq982":  "3005",
			"bkngExpLmqp0":  "3006",
			"cnclExpTrb61":  "3007",
			"roomErrWq349":  "3008",
			"ctyErrQa1923":  "3009",
			"updStatLwq92":  "3010",
			"noCntctXz842":  "3011",
			"dbErr91pqLd7":  "3012",
			"statErrYt390":  "3013",
			"noCrdtQwlk20":  "3014",
			"invldAvlkz34":  "3015",
			"cnfrmFailr02":  "3016",
			"bkngConfWpl8":  "3017",
			"bkngCnclXz19":  "3018",
			"dupRefYx8431":  "3019",
			"pendBkngLs49":  "3020",
			"stopSellWqp0":  "3021",
			"invChkInQa87":  "3022",
			"eanNullPz102":  "3023",
			"refLngLp9031":  "3025",
			"noRefNw1930a":  "3026",
			"valAddMism88":  "3027",
			"updRefFail9s":  "3028",
			"aftChkCncl12":  "3031",
			"preConfBkpg2":  "3033",
			"cpnMisMatch":   "3034",
			"invNamePkz13":  "3035",
			"payExpTqlz98":  "3036",
			"noNameAlwd34":  "3038",
			"dupBkngAqp11":  "3039",
			"prcTolWq8494":  "3040",
			"mealMisKpw23":  "3041",
			"noRtPlan92wL":  "3050",
			"reqTmtXp8492":  "3060",
			"reqFailRx218":  "3070",
			"blkRndTrWqpl":  "3080",
			"payFailTlo31":  "3090",
			"cnclFailzPt02": "4000",
			"highRiskWplq8": "4010",
			"snctGuestx9Z3": "4030",
			"nonCnclNA49qs": "4031",
			"appIDExist33z": "8000",
			"invAppNA01qp":  "8001",
			"ordrNotExstl2": "8002",
		},
		ActionOrderBookingFinishStatus: {
			"Xn7aJdHa": "3001", // Incorrect Booking Information
			"Kp9bTrWq": "3002", // Incorrect ReferenceNo
			"Lm3cYuIo": "3003", // Incorrect BookingID
			"Zx5dPsEr": "3004", // Incorrect CancelConfirmID
			"Vb2eQtFy": "3005", // Incorrect OccupancyInfo
			"Nc8fRuGt": "3006", // Booking Expired
			"Jh4gSvHu": "3007", // Cancel Expired
			"Gd6hTwIv": "3008", // Incorrect Num Of Rooms
			"Fs9jUxJw": "3009", // Incorrect CityCode
			"Dp1kVyKx": "3010", // Failed To Update Status
			"Bm3lWzLy": "3011", // No Contact Info
			"An5mXaMz": "3012", // DB Error
			"Yo7nYbNa": "3016", // Failed To Confirm Booking
			"Xp9oZcOb": "3018", // Booking Canceled
			"Wq1pAdPc": "3019", // Duplicate ClientReference
			"Vr3qBeQd": "3021", // Is Stop Sell
			"Us5rCfRe": "3022", // Is Invalid CheckIn
			"Tt7sDgSf": "3025", // ClientReference Exceed Max Length
			"Su9tEhTg": "3026", // ClientReference Not Provided
			"Rv1uFiUh": "3028", // Failed To Update ClientReference
			"Qw3vGjVi": "3035", // Invalid Guest Name
			"Px5wHkWj": "3036", // Booking Payment Expired
			"Oy7xIlXk": "3038", // Guest Name Not Allow
			"Nz9yJmYl": "3039", // Duplicate Booking
			"Ma1zKnZm": "3040", // Price Out Of Tolerance
			"Lb3aLoAn": "3041", // MealType Not Match
			"Kc5bMpBo": "3050", // No RatePlan Rate Provided
			"Jd7cNqCp": "3060", // OnRequest Booking Confirm TimeOut
			"Ie9dOrDq": "3070", // OnRequest Booking Confirm Failed
			"Hf1ePsDr": "3090", // Pay Failed
		},
	},
	commonEnum.HotelProviderAgoda: {
		ActionCreateBooking: {
			"T1TdDPGr": "909-Sorry, there are no available rooms for your chosen dates.",
			"uut8gN1p": "909-Invalid User ID",
			"IuVsplBr": "909-Site ID mismatched with Ref ID",
			"hjEEpWGG": "909-Hotel name is empty",
			"AOewmh3i": "909-Category is empty",
			"Off2iZ6L": "909-Invalid special request information",
			"MtkkF7Nq": "940-Hotel name is empty",
			"XShdbnsu": "Unknown",
		},
		ActionOrderBookingFinishStatus: {
			"c3F9WZ1x": "BookingTest",
			"a7LmK0Ds": "BookingRejected",
			"v4JpR8Yt": "TechnicalError",
			"X2oNz1Pq": "AllotmentAlert",
			"k8GhD5Wu": "Departed",
			"M9eTr4Uv": "BookingReceived",
			"y6HsF3Qp": "BookingConfirmed",
		},
		ActionPriceCheck: {
			"sifwgusb": "501",
			"whqhHDWI": "502",
			"euifehaq": "503",
		},
	},
}

func GetTestCaseValue(adapter commonEnum.HotelProvider, action, key string) string {
	actionMap, adapterExists := TestCaseMap[adapter]
	if !adapterExists {
		return ""
	}

	keyMap, actionExists := actionMap[action]
	if !actionExists {
		return ""
	}

	value, keyExists := keyMap[key]
	if !keyExists {
		return ""
	}

	return value
}
