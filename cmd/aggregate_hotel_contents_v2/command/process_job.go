package command

import (
	"context"
	"sync/atomic"
	"time"

	"github.com/gammazero/workerpool"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/repo/models"
	veroModels "gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/repo/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/mongo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func (h *JobHandlerImpl) ProcessJobsQueue(ctx context.Context) error {
	pool := workerpool.New(MAX_CONCURRENCY)
	counter := atomic.Int32{}

	cur, err := h.jobQueueRepo.FindActiveJobs(ctx)
	if err != nil {
		log.Error("jobQueueRepo.FindActiveJobs error", log.Any("error", err))
		return err
	}

	for cur.Next(ctx) {
		jobItem := &models.ContentMappingJobItem{}
		if err := cur.Decode(jobItem); err != nil {
			log.Error("cur.Decode error", log.Any("error", err))
			return err
		}

		pool.Submit(func() {
			log.Info("jobItem submit", log.Int("jobItem", jobItem.Index))

			if err := h.processJob(ctx, jobItem); err != nil {
				log.Error("processJob error", log.Any("error", err))
			}

			counter.Add(1)

			log.Info("jobItem item finished", log.Int32("counter", counter.Load()), log.Int("jobItem", jobItem.Index))
		})
	}

	pool.StopWait()

	return nil
}

func (h *JobHandlerImpl) processJob(ctx context.Context, jobItem *models.ContentMappingJobItem) error {
	mappings, err := h.mappingRepo.FindByThirdPartyIds(ctx, jobItem.QueueIds)
	if err != nil {
		return err
	}

	startAt := time.Now()
	failedIds := []string{}
	processsedIds := []string{}

	for _, mapping := range mappings {
		ok, err := h.processMapping(ctx, mapping)
		if err != nil {
			return err
		}

		if !ok {
			failedIds = append(failedIds, mapping.ThridPartyID)
			continue
		}

		processsedIds = append(processsedIds, mapping.ThridPartyID)
	}

	elapsed := time.Since(startAt)

	//
	jobItem.CompletedAt = time.Now().UnixMilli()
	jobItem.TotalDurationMs = elapsed.Milliseconds()
	jobItem.ProcessedIds = processsedIds
	jobItem.FailedIds = failedIds

	if err := h.jobQueueRepo.UpdateOne(ctx, jobItem); err != nil {
		return err
	}

	return nil
}

func (h *JobHandlerImpl) processMapping(ctx context.Context, mappingItem *veroModels.ContentMappingItem) (bool, error) {
	if len(mappingItem.ExpediaHotelIds) == 0 || len(mappingItem.RatehawkHotelIds) == 0 {
		return false, nil
	}

	// Dynamic handling
	mappingItem.ExpediaHotelID = mappingItem.ExpediaHotelIds[0]

	mappingItem.RatehawkHotelID = mappingItem.RatehawkHotelIds[0]

	// End Dymnamic

	// Verify provider id
	baseHotels, err := h.hotelV2Repo.FindByHotelIDV2(ctx, mappingItem.ExpediaHotelIds[0])
	if err != nil {
		return false, err
	}

	if len(baseHotels) == 0 {
		return false, nil
	}

	hotelID := baseHotels[0].HotelID

	langIDMap := map[string]string{}

	rhhotels, err := h.ratehawkHotelRepo.FindByProviderIds(ctx, commonEnum.HotelProviderRateHawk, mappingItem.RatehawkHotelIds[0])
	if err != nil {
		return false, err
	}

	if len(rhhotels) == 0 {
		return false, nil
	}

	roomIds := []string{}

	for _, rhHotel := range rhhotels {
		langIDMap[rhHotel.Language] = rhHotel.ID

		ids := lo.Map(rhHotel.RoomReferences, func(roomRef *domain.RoomRefInfo, _ int) string {
			return roomRef.ID
		})

		roomIds = append(roomIds, ids...)
	}

	if len(roomIds) == 0 {
		return true, nil
	}

	// BEGIN
	if err := h.hotelV2Repo.UpdateProviderIds(ctx, hotelID, commonEnum.HotelProviderRateHawk, mappingItem.RatehawkHotelID); err != nil {
		return false, err
	}

	rooms, err := h.ratehawkRoomRepo.FindByRoomObjectIDs(ctx, roomIds)
	if err != nil {
		log.Error("ratehawkRoomRepo.FindByRoomIDs error", log.Any("error", err))
		return false, err
	}

	for _, room := range rooms {
		room.HotelRef = langIDMap[room.Language]
		room.HotelID = hotelID
	}

	if err := h.roomV2Repo.CreateMany(ctx, rooms, ""); err != nil && !mongo.IsDuplicateKeyError(err) {
		log.Error("roomV2Repo.CreateMany error", log.Any("error", err))
		return false, err
	}

	return true, nil
}
