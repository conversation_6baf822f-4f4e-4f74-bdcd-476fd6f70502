package main

import (
	"context"
	"flag"
	"os"
	"time"

	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/aggregate_hotel_contents_v2/repo"
	veroRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/repo"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

var db commonMongoDB.DB

func main() {
	ctx := context.TODO()
	var err error
	startT := time.Now()

	helpers.LoadEnv()

	shouldInitJobs := flag.String("init", "", "Giá trị của tham số 1")

	flag.Parse()

	// Init CommonDB connection.
	db, err = commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: os.Getenv("COMMON_WRITE_URL"),
		ReadURL:  os.Getenv("COMMON_WRITE_URL"),
		Database: os.Getenv("COMMON_MONGO_DB"),
	})
	if err != nil {
		panic(err)
	}

	cfgRH := &config.Schema{
		ContentVersion: "1",
		EnableProvider: "rate-hawk",
	}

	cfgV2 := &config.Schema{
		ContentVersion: "2",
		EnableProvider: "rate-hawk",
	}

	hotelV2Repo := repositories.NewHotelRepository(db, cfgV2)
	roomsV2Repo := repositories.NewRoomRepository(db, cfgV2)

	rateHawkHotelRepo := repositories.NewHotelRepository(db, cfgRH)
	rateHawkRoomRepo := repositories.NewRoomRepository(db, cfgRH)

	mappingRepo := veroRepo.NewContentMappingRepository(db)
	jobQueueRepo := repo.NewContentMappingJobRepository(db)

	jobCommand := command.NewJobHandler(jobQueueRepo, mappingRepo, hotelV2Repo, rateHawkHotelRepo, rateHawkRoomRepo, roomsV2Repo)

	if shouldInitJobs != nil && *shouldInitJobs == "true" {
		if err := jobCommand.InitJobsQueue(ctx); err != nil {
			log.Error("initJobsQueue error", log.Any("error", err))
			return
		}
	}

	if err := jobCommand.ProcessJobsQueue(ctx); err != nil {
		log.Error("processJobsQueue error", log.Any("error", err))
		return
	}

	log.Info("Done", log.Any("time", time.Since(startT).Milliseconds()))
}
