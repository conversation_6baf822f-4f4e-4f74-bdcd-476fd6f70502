package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"path/filepath"

	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type TAHNHRoomMapping struct {
	HubProviderID string `json:"hub_provider_id"`
	HNHProviderID string `json:"hnh_provider_id"`
	Name          string `json:"hotel_name"`
	Address       string `json:"address"`
}

type Hotel struct {
	ID string `bson:"_id"`
}

const (
	hotelIDLanguageVersionIndex = "__hotel_id__language__version__"
)

func main() {
	// Load config
	cfg, err := config.New()
	if err != nil {
		log.Fatal("Failed to load config", log.Any("error", err))
	}

	// Connect to common DB
	commonDB, err := commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: cfg.CommonReadURL,
		ReadURL:  cfg.CommonReadURL,
		Database: cfg.CommonMongoDB,
	})
	if err != nil {
		log.Fatal("Failed to connect to common DB", log.Any("error", err))
	}

	// Read mapping file
	jsonPath, err := filepath.Abs("docs/ta_hnh_room.json")
	if err != nil {
		log.Fatal("Failed to get absolute path for JSON file", log.Any("error", err))
	}

	jsonData, err := os.ReadFile(jsonPath)
	if err != nil {
		log.Fatal("Failed to read JSON file", log.Any("error", err))
	}

	var mappings []TAHNHRoomMapping

	err = json.Unmarshal(jsonData, &mappings)
	if err != nil {
		log.Fatal("Failed to unmarshal JSON data", log.Any("error", err))
	}

	// Process mappings
	ctx := context.Background()

	totalHotels := len(mappings)
	updatedCount := 0
	notFoundCount := 0

	log.Info("Starting migration process", log.Int("total_hotels", totalHotels))

	hotelCollection := "hotels_v2"

	log.Info("Using collection", log.String("collection", hotelCollection))

	for _, mapping := range mappings {
		hotelID := mapping.HubProviderID
		hnhProviderID := mapping.HNHProviderID

		var hotel Hotel
		findFilter := bson.M{"hotel_id": hotelID}

		err := commonDB.FindOne(
			ctx,
			hotelCollection,
			&hotel,
			commonMongoDB.WithFilter(findFilter),
			commonMongoDB.WithHint(hotelIDLanguageVersionIndex),
		)
		if err != nil {
			if errors.Is(err, mongo.ErrNoDocuments) {
				log.Warn("Hotel not found",
					log.String("hotel_id", hotelID),
					log.String("hotel_name", mapping.Name),
				)

				notFoundCount++

				continue
			}

			log.Error("Error finding hotel",
				log.String("hotel_id", hotelID),
				log.Any("error", err),
			)

			continue
		}

		providerKey := fmt.Sprintf("provider_ids.%d", commonEnum.HotelProviderHNHTravelAgent)
		updateObj := make(map[string]string)
		updateObj[providerKey] = hnhProviderID

		err = commonDB.UpdateManyRaw(
			ctx,
			hotelCollection,
			findFilter,
			bson.M{"$set": updateObj},
			&options.UpdateOptions{
				Hint: hotelIDLanguageVersionIndex,
			},
		)
		if err != nil {
			log.Error("Failed to update hotel",
				log.String("hotel_id", hotelID),
				log.String("hnh_provider_id", hnhProviderID),
				log.Any("error", err),
			)

			continue
		}

		updatedCount++

		log.Info("Updated hotel",
			log.String("hotel_id", hotelID),
			log.String("hotel_name", mapping.Name),
			log.String("hnh_provider_id", hnhProviderID),
		)
	}

	log.Info("Migration completed",
		log.Int("total_hotels", totalHotels),
		log.Int("updated_hotels", updatedCount),
		log.Int("not_found_hotels", notFoundCount),
	)
}
