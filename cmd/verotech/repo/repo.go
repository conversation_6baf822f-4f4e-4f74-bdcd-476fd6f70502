package repo

import (
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
)

func NewCommonRepository(db mongodb.DB) CommonRepository {
	return &commonRepository{db, NewCollections(), NewIndexes()}
}

func NewCollections() collections {
	return collections{
		expediaHotels:   "expedia_hotels",
		ratehawkHotels:  "hotels",
		expediaRooms:    "expedia_hotel_rooms",
		ratehawkRooms:   "hotel_rooms",
		expediaRegions:  "expedia_regions",
		ratehawkRegions: "regions",
		roomV2:          "hotel_rooms_v2",
		hotelV2:         "hotels_v2",
		regionsV2:       "regions_v2",
	}
}

func NewIndexes() indexes {
	return indexes{}
}

type collections struct {
	expediaHotels  string
	ratehawkHotels string

	expediaRooms  string
	ratehawkRooms string

	expediaRegions  string
	ratehawkRegions string

	roomV2    string
	hotelV2   string
	regionsV2 string
}

type indexes struct{}

type CommonRepository interface{}

type commonRepository struct {
	db mongodb.DB
	collections
	indexes
}
