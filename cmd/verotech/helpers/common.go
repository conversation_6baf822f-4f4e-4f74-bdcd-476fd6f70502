package helpers

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
	"gitlab.deepgate.io/apps/common/log"
)

func LoadEnv() {
	err := godotenv.Load("../../.env")
	if err != nil {
		log.Fatal("Error loading .env file")
	}
}

func WriteResumeKey(resumeKey string) {
	file, err := os.OpenFile("resume_key.txt", os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0o644)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()

	_, err = fmt.Fprintln(file, resumeKey+"\n"+time.Now().Format("2006-01-02 15:04:05")+"\n")
	if err != nil {
		fmt.Println(err)
		return
	}
}

func WriteProcessedCount(total int) {
	file, err := os.OpenFile("total_processed.txt", os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0o644)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()

	_, err = fmt.Fprintln(file, strconv.Itoa(total)+"\n"+time.Now().Format("2006-01-02 15:04:05")+"\n")
	if err != nil {
		fmt.Println(err)
		return
	}
}
