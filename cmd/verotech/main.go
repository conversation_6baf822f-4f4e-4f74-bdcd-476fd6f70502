package main

import (
	"context"
	"fmt"
	"os"
	"time"

	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/adapter"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/repo"
)

func main() {
	helpers.LoadEnv()

	ctx := context.Background()
	startTime := time.Now()

	verotechAdapter := adapter.NewVerotechAdapter(
		os.Getenv("VEROTECH_BASE_URL"),
		os.Getenv("VEROTECH_API_KEY"),
		os.Getenv("VEROTECH_ACCOUNT_ID"),
	)

	// Init CommonDB connection.
	db, err := commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: os.Getenv("COMMON_WRITE_URL"),
		ReadURL:  os.Getenv("COMMON_WRITE_URL"),
		Database: os.Getenv("COMMON_MONGO_DB"),
	})
	if err != nil {
		panic(err)
	}

	contentMappingRepo := repo.NewContentMappingRepository(db)

	streamingJSON, close, err := verotechAdapter.GetNewMappingsStatic(ctx)
	if close != nil {
		defer close()
	}

	if err != nil {
		panic(err)
	}

	itemPool := []*entities.Mapping{}

	begin := 0

	for streamingJSON.More() {
		item := &entities.Mapping{}
		if err := streamingJSON.Decode(&item); err != nil {
			fmt.Println("Error decoding JSON:", err)
			return
		}

		itemPool = append(itemPool, item)

		if len(itemPool) == 1000 {
			mappingItems := converts.FromVeroTechMappingsStatic(itemPool)

			if err := contentMappingRepo.UpsertMany(ctx, mappingItems); err != nil {
				log.Error("contentMappingRepo.UpsertMany err", log.Any("err", err))
			}

			itemPool = []*entities.Mapping{}

			begin += len(mappingItems)

			if begin%100 == 0 {
				log.Info("Dump verotech mappings to DB progress", log.Any("begin", begin))
			}
		}
	}

	if len(itemPool) > 0 {
		mappingItems := converts.FromVeroTechMappingsStatic(itemPool)

		if err := contentMappingRepo.UpsertMany(ctx, mappingItems); err != nil {
			log.Error("contentMappingRepo.UpsertMany err", log.Any("err", err))
		}

		begin += len(mappingItems)
	}

	helpers.WriteProcessedCount(begin)

	log.Info("Dump verotech mappings to DB finished", log.Any("duration", time.Since(startTime)), log.Int("count", begin))
}

// FROM API

// const limt = "1000"
// 	resumeKey := ""

// 	begin := 0
// 	for {
// 		data, err := verotechAdapter.GetNewMappings(ctx, resumeKey, limt)
// 		if err != nil {
// 			log.Error("GetNewMappings err", log.Any("err", err))
// 		}

// 		if data.Message == "No Records Found" && data.StatusCode == 2000 {
// 			break
// 		}

// 		resumeKey = data.ResumeKey
// 		helpers.WriteResumeKey(resumeKey)

// 		mappingItems := converts.FromVeroTechMappings(data)

// 		if err := contentMappingRepo.UpsertMany(ctx, mappingItems); err != nil {
// 			log.Error("contentMappingRepo.UpsertMany err", log.Any("err", err))
// 		}

// 		if len(data.Mappings) == 0 || resumeKey == "" {
// 			log.Error("GetNewMappings data.Mappings empty", log.Any("data", data))
// 			break
// 		}

// 		begin += len(data.Mappings)

// 		if begin%100 == 0 {
// 			log.Info("Dump verotech mappings to DB progress", log.Any("begin", begin))
// 		}
// 	}
