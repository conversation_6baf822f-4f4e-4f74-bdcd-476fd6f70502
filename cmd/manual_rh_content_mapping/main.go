package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/cmd/verotech/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"golang.org/x/sync/errgroup"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type ExpRhMapping struct {
	IDEsp         primitive.ObjectID `json:"_id_esp"`
	IDRh          primitive.ObjectID `json:"_id_rh"`
	EspProviderID string             `json:"esp_provider_id"`
	RhProviderID  string             `json:"rh_provider_id"`
}

var (
	commonDB commonMongoDB.DB
	prodDB   commonMongoDB.DB
)

func main() {
	var err error
	helpers.LoadEnv()

	// Init CommonDB connection.
	commonDB, err = commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: os.Getenv("COMMON_WRITE_URL"),
		ReadURL:  os.Getenv("COMMON_WRITE_URL"),
		Database: os.Getenv("COMMON_MONGO_DB"),
	})
	if err != nil {
		panic(err)
	}

	prodDB, err = commonMongoDB.New(&commonMongoDB.Config{
		WriteURL: os.Getenv("PROD_URL"),
		ReadURL:  os.Getenv("PROD_URL"),
		Database: os.Getenv("COMMON_MONGO_DB"),
	})
	if err != nil {
		panic(err)
	}

	loadFile()
}

func loadFile() {
	// ctx := context.Background()
	file, err := os.Open("table_id.json")
	if err != nil {
		fmt.Println("Error opening file:", err)
		return
	}
	defer file.Close()

	// Create a JSON decoder
	decoder := json.NewDecoder(file)

	// Read the opening bracket of the JSON array
	_, err = decoder.Token()
	if err != nil {
		fmt.Println("Error reading JSON token:", err)
		return
	}

	count := loadPreviousCount()
	resume := 1

	jobsPool := []*ExpRhMapping{}
	const maxItem = 10
	const maxJob = 10
	resumed := false
	runTicket := 0

	var errGroup errgroup.Group
	// Stream and decode each element of the array
	for decoder.More() {
		if resume < count && !resumed {
			fmt.Printf("resume %d/%d\n", resume, count)

			resume++

			continue
		} else {
			resumed = true
		}

		item := &ExpRhMapping{}

		err := decoder.Decode(item)
		if err != nil {
			fmt.Println("Error decoding JSON:", err)
			return
		}

		jobsPool = append(jobsPool, item)

		if len(jobsPool) == maxItem {
			runTicket++

			_jobsPool := jobsPool
			jobsPool = []*ExpRhMapping{}

			errGroup.Go(func() error {
				ctxBg, cc := context.WithTimeout(context.Background(), 10*time.Minute)
				defer cc()

				err := processBulk(ctxBg, _jobsPool)
				if err != nil {
					writeErrItem(err.Error())
					return errors.Wrap(err, "processBulk")
				}

				return nil
			})

			if runTicket == maxJob {
				if err := errGroup.Wait(); err != nil {
					writeErrItem(err.Error())
					return
				}

				runTicket = 0
				count += maxJob * maxItem
				writeProcessedItem(count)
				fmt.Println(count)
			}
		}
	}

	if runTicket != 0 {
		fmt.Println("run last runTicket", runTicket)

		if err := errGroup.Wait(); err != nil {
			writeErrItem(err.Error())
			return
		}

		runTicket = 0
		count += runTicket * maxItem
		writeProcessedItem(count)
		fmt.Println(count)
	}

	if len(jobsPool) > 0 {
		fmt.Println("run last jobPool", len(jobsPool))

		ctxBg, cc := context.WithTimeout(context.Background(), 10*time.Minute)
		defer cc()

		err := processBulk(ctxBg, jobsPool)
		if err != nil {
			writeErrItem(err.Error())
			return
		}

		count += len(jobsPool)
		writeProcessedItem(count)
		fmt.Println(count)

		jobsPool = nil
	}

	// Read the closing bracket of the JSON array
	_, err = decoder.Token()
	if err != nil {
		fmt.Println("Error reading JSON token:", err)
	}
}

func writeErrItem(errMsg string) {
	file, err := os.OpenFile("errors.txt", os.O_APPEND|os.O_WRONLY|os.O_CREATE, 0o644)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()

	_, err = fmt.Fprintln(file, errMsg+"\n")
	if err != nil {
		fmt.Println(err)
		return
	}
}

func writeProcessedItem(item int) {
	file, err := os.OpenFile("processed.txt", os.O_WRONLY|os.O_CREATE, 0o644)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer file.Close()

	_, err = fmt.Fprintln(file, strconv.Itoa(item)+"\n")
	if err != nil {
		fmt.Println(err)
		return
	}
}

func loadPreviousCount() int {
	file, err := os.Open("processed.txt")
	if err != nil {
		fmt.Println("Error opening file:", err)
		return 0
	}
	defer file.Close()

	// Create a scanner to read the file line by line
	scanner := bufio.NewScanner(file)

	// Read the first line
	if scanner.Scan() {
		// Convert the first line to an integer
		number, err := strconv.Atoi(scanner.Text())
		if err != nil {
			fmt.Println("Error converting to int:", err)
			return 0
		}

		return number
		// The variable `number` now holds the integer value
	}

	// Check for scanner errors
	if err := scanner.Err(); err != nil {
		fmt.Println("Error reading file:", err)
	}

	return 0
}

func processBulk(ctx context.Context, jobs []*ExpRhMapping) error {
	exIds := lo.Map(jobs, func(item *ExpRhMapping, _ int) primitive.ObjectID {
		return item.IDEsp
	})

	rhIds := lo.Map(jobs, func(item *ExpRhMapping, _ int) primitive.ObjectID {
		return item.IDRh
	})

	exHotels := []*models.Hotel{}
	rhHotels := []*models.Hotel{}

	exFilter := bson.M{"_id": bson.M{"$in": exIds}}

	exOtps := []mongodb.Option{
		mongodb.WithHint("_id_"),
		mongodb.WithFilter(exFilter),
	}

	if err := commonDB.Find(ctx, "expedia_hotels", &exHotels, exOtps...); err != nil {
		return err
	}

	rhFilter := bson.M{"_id": bson.M{"$in": rhIds}}

	rhOtps := []mongodb.Option{
		mongodb.WithHint("_id_"),
		mongodb.WithFilter(rhFilter),
	}

	if err := prodDB.Find(ctx, "hotels", &rhHotels, rhOtps...); err != nil {
		return err
	}

	rhHotelMap := map[string]*models.Hotel{}

	// Ex id -> rh id
	exIDJobMap := map[string]*ExpRhMapping{}

	for _, item := range jobs {
		exIDJobMap[item.IDEsp.Hex()] = item
	}

	for _, item := range rhHotels {
		rhHotelMap[item.ID.Hex()] = item
	}

	for _, item := range exHotels {
		job := exIDJobMap[item.ID.Hex()]

		if job == nil {
			return errors.New("Job not found. Ex id:" + item.ID.Hex())
		}

		rhID := job.IDRh.Hex()

		rhHotel := rhHotelMap[rhID]

		if rhHotel == nil {
			return errors.New("Hotel not found. Ex id:" + item.ID.Hex())
		}

		item.HotelID = rhHotel.HotelID
		item.ProviderIds = rhHotel.ProviderIds
		item.RoomReferences = rhHotel.RoomReferences
		item.ProviderIds[commonEnum.HotelProviderExpedia] = job.EspProviderID
	}

	updateItems := make([]mongo.WriteModel, 0, len(exHotels))

	for _, updateItem := range exHotels {
		rhObjID := exIDJobMap[updateItem.ID.Hex()].IDRh
		updateItem.ID = primitive.NilObjectID

		update := bson.M{
			"$set": updateItem,
		}

		filter := bson.M{
			"_id": rhObjID,
		}

		updateItems = append(updateItems, mongo.NewUpdateOneModel().SetUpdate(update).SetFilter(filter).SetHint("_id_"))
	}

	err := prodDB.BulkWriteRaw(ctx, "hotels", updateItems)
	if err != nil {
		return err
	}

	return nil
}
