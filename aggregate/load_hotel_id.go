package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type Hotel struct {
	HotelID  string `json:"hotel_id" bson:"hotel_id"`
	Name     string `json:"-" bson:"name"`
	Language string `json:"-" bson:"language"`
}

type NameResponse struct {
	NameVi string `json:"name_vi"`
	NameEn string `json:"name_en"`
	NameKr string `json:"name_kr"`
}

func loadDataFromFile(filename string) (map[string]NameResponse, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	byteValue, err := ioutil.ReadAll(file)
	if err != nil {
		return nil, err
	}

	var result map[string]NameResponse
	if err := json.Unmarshal(byteValue, &result); err != nil {
		return nil, err
	}

	return result, nil
}

func queryMongoDB() (map[string]*NameResponse, error) {
	cfg, err := config.New()
	if err != nil {
		panic(err)
	}

	clientOptions := options.Client().ApplyURI(cfg.CommonMongoDB)
	ctx, cc := context.WithCancel(context.Background())
	defer cc()

	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		return nil, err
	}
	defer client.Disconnect(ctx)

	collection := client.Database("common-db-v2").Collection("hotels_v2")

	filter := bson.M{}
	cursor, err := collection.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	hotelResult := map[string]*NameResponse{}
	for cursor.Next(ctx) {
		var hotel Hotel
		if err := cursor.Decode(&hotel); err != nil {
			return nil, err
		}
		fmt.Println("hotel", hotel)
		if hotelResult[hotel.HotelID] == nil {
			hotelResult[hotel.HotelID] = &NameResponse{
				NameVi: "",
				NameEn: "",
				NameKr: "",
			}
		}
		switch hotel.Language {
		case "vi-VN":
			{
				hotelResult[hotel.HotelID].NameVi = hotel.Name
			}
		case "en-US":
			{
				hotelResult[hotel.HotelID].NameEn = hotel.Name
			}
		case "ko-KR":
			{
				hotelResult[hotel.HotelID].NameVi = hotel.Name
			}
		}
	}

	return hotelResult, nil
}

func writeHotelsToFile(hotels interface{}, filename string) error {
	jsonData, err := json.Marshal(hotels)
	if err != nil {
		return err
	}

	return ioutil.WriteFile(filename, jsonData, 0644)
}

func generateHotelWithoutNameEn() {
	inputFilename := "./filter_list_not_all.json"
	outputFilename := "./inactive_without_name_en.json" // Update with desired output file path

	expandedPath, err := expandPath(inputFilename)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	data, err := loadDataFromFile(expandedPath)
	if err != nil {
		log.Fatal("Error loading array from file:", err)
	}

	hotels := []Hotel{}

	for key, value := range data {
		if value.NameEn == "" {
			hotels = append(hotels, Hotel{HotelID: key})
		}
	}

	if err := writeHotelsToFile(hotels, outputFilename); err != nil {
		log.Fatal("Error writing to file:", err)
	}

	fmt.Printf("Unique hotels written to %s\n", outputFilename)
}

func getHotelWithName() {
	outputFilename := "./filter_list_not_all.json"

	hotels, err := queryMongoDB()
	if err != nil {
		log.Fatal("Error querying MongoDB:", err)
	}

	if err := writeHotelsToFile(hotels, outputFilename); err != nil {
		log.Fatal("Error writing to file:", err)
	}

	fmt.Printf("Unique hotels written to %s\n", outputFilename)
}
