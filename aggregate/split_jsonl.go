package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
)

const maxFileSize = 1024 * 1024 * 1024 // 1GB

// expandPath expands the ~ to the user's home directory.
func expandPath(p string) (string, error) {
	if p[:2] == "~/" {
		homeDir, err := os.UserHomeDir()
		if err != nil {
			return "", err
		}

		return filepath.Join(homeDir, p[2:]), nil
	}

	return p, nil
}

func SplitFileJsonl() {
	inputFile := "~/en-US.expediacollect.propertycontent.jsonl"
	outputDir := "./output/"

	// Tạo thư mục đầu ra nếu chưa có
	if err := os.MkdirAll(outputDir, 0o755); err != nil {
		fmt.Println("Lỗi khi tạo thư mục:", err)
		return
	}

	expandedPath, err := expandPath(inputFile)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	// Mở tệp JSONL gốc để đọc
	file, err := os.Open(expandedPath)
	if err != nil {
		fmt.Println("Không thể mở file:", err)
		return
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	partNumber := 1
	var currentFile *os.File
	var currentSize int64

	// Hàm để tạo file mới
	createNewFile := func() (*os.File, error) {
		if currentFile != nil {
			currentFile.Close()
		}
		outputPath := filepath.Join(outputDir, "part_"+strconv.Itoa(partNumber)+".jsonl")
		partNumber++

		return os.Create(outputPath)
	}

	// Mở file đầu tiên
	currentFile, err = createNewFile()
	if err != nil {
		fmt.Println("Không thể tạo file mới:", err)
		return
	}
	defer currentFile.Close()

	writer := bufio.NewWriter(currentFile)

	for {
		// Đọc từng dòng từ file JSONL gốc
		line, err := reader.ReadString('\n')
		if err != nil {
			if err.Error() != "EOF" {
				fmt.Println("Lỗi khi đọc file:", err)
			}

			break
		}

		lineSize := int64(len(line))

		// Kiểm tra nếu kích thước file hiện tại vượt quá 500MB
		if currentSize+lineSize > maxFileSize {
			writer.Flush()

			currentFile, err = createNewFile()
			if err != nil {
				fmt.Println("Không thể tạo file mới:", err)
				return
			}
			writer = bufio.NewWriter(currentFile)
			currentSize = 0
		}

		// Ghi dòng vào file
		_, err = writer.WriteString(line)
		if err != nil {
			fmt.Println("Lỗi khi ghi vào file:", err)
			return
		}
		currentSize += lineSize
	}

	writer.Flush()
	fmt.Println("Hoàn thành chia nhỏ file!")
}
