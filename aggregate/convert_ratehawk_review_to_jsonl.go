package main

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ConvertRHReviewToJsonl() {
	inputFile := "~/Downloads/feed_v3_en.json/feed_v3_en.json"
	outputDir := "./output_rh_review/"

	// Tạo thư mục đầu ra nếu chưa có
	if err := os.MkdirAll(outputDir, 0o755); err != nil {
		fmt.Println("Lỗi khi tạo thư mục:", err)
		return
	}

	expandedPath, err := expandPath(inputFile)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}

	// Mở tệp JSONL gốc để đọc
	file, err := os.Open(expandedPath)
	if err != nil {
		fmt.Println("Không thể mở file:", err)
		return
	}
	defer file.Close()

	reader := bufio.NewReader(file)
	partNumber := 1
	var currentFile *os.File
	var currentSize int64

	// Hàm để tạo file mới
	createNewFile := func() (*os.File, error) {
		if currentFile != nil {
			currentFile.Close()
		}
		outputPath := filepath.Join(outputDir, "part_"+strconv.Itoa(partNumber)+".jsonl")
		partNumber++

		return os.Create(outputPath)
	}

	// Mở file đầu tiên
	currentFile, err = createNewFile()
	if err != nil {
		fmt.Println("Không thể tạo file mới:", err)
		return
	}
	defer currentFile.Close()

	writer := bufio.NewWriter(currentFile)

	for {
		// Đọc từng dòng từ file JSONL gốc
		line, err := reader.ReadString('\n')
		if err != nil {
			if err.Error() != "EOF" {
				fmt.Println("Lỗi khi đọc file:", err)
			}

			break
		}

		if line == "" || strings.HasPrefix(line, "{") || strings.HasPrefix(line, "}") {
			continue
		}

		line = strings.TrimSuffix(line, ",\n")
		line = fmt.Sprintf("{%s}", line)
		var hotelReview map[string]*domain.RateHawkHotelReview

		err = json.Unmarshal([]byte(line), &hotelReview)
		if err != nil {
			fmt.Println("Error Unmarshal record to JSON:", err, line)
			return
		}

		if len(hotelReview) == 0 {
			fmt.Println("review null", line)
		}

		isSkip := false

		var data *domain.RateHawkHotelReview

		for key, value := range hotelReview {
			if value == nil {
				fmt.Println("review null", key, line)
				isSkip = true

				continue
			}

			value.PropertyID = key
			data = value
		}

		if isSkip {
			continue
		}

		newLine, err := json.Marshal(data)
		if err != nil {
			fmt.Println("Error marshal record to JSON:", err)
			return
		}

		lineSize := int64(len(newLine))

		// Kiểm tra nếu kích thước file hiện tại vượt quá 500MB
		if currentSize+lineSize > maxFileSize {
			writer.Flush()

			currentFile, err = createNewFile()
			if err != nil {
				fmt.Println("Không thể tạo file mới:", err)
				return
			}
			writer = bufio.NewWriter(currentFile)
			currentSize = 0
		}

		// Ghi dòng vào file
		_, err = writer.WriteString(string(newLine) + "\n")
		if err != nil {
			fmt.Println("Lỗi khi ghi vào file:", err)
			return
		}
		currentSize += lineSize
	}

	writer.Flush()
	fmt.Println("Hoàn thành chia nhỏ file!")
}
