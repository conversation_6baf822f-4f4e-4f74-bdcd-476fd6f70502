package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type TAClient struct {
	tokenURL    string
	clientID    string
	secretKey   string
	scope       string
	bookingURL  string
	requestRepo repositories.RequestRepository
	redis       redis.IRedis
}

func NewTAClient(cfg *config.Schema, requestRepo repositories.RequestRepository, redis redis.IRedis) TAClient {
	return TAClient{
		tokenURL:    cfg.TATokenURL,
		clientID:    cfg.TAClientID,
		secretKey:   cfg.TASecretKey,
		scope:       cfg.TAScope,
		bookingURL:  cfg.TABookingURL,
		requestRepo: requestRepo,
		redis:       redis,
	}
}

func (c *TAClient) getToken(ctx context.Context, tracingID string) (_ string, err error) {
	const tokenKey = "ta_token"

	rCMD := c.redis.CMD().Get(ctx, tokenKey)
	if rCMD.Err() != nil && !errors.Is(rCMD.Err(), goRedis.Nil) {
		return "", rCMD.Err()
	}

	token := rCMD.Val()
	if token != "" {
		return token, nil
	}

	res := &entities.GetTokenResponse{}
	path := "/connect/token"
	tokenURL := c.tokenURL + path

	data := url.Values{}
	data.Set("grant_type", "client_credentials")
	data.Set("client_id", c.clientID)
	data.Set("client_secret", c.secretKey)
	data.Set("scope", c.scope)

	var statusCode int
	startTiime := time.Now()

	defer func() {
		if c.requestRepo == nil {
			return
		}

		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)
		defer cancel()

		requestID := uuid.NewString()
		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       tokenURL,
			Method:     http.MethodPost,
			StatusCode: statusCode,
			Duration:   time.Since(startTiime).Milliseconds(),
			Action:     path,
			Provider:   commonEnum.HotelProviderTA,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("TA get token adapter error",
				log.Any("error", err),
			)
		}
	}()

	resp, err := http.PostForm(tokenURL, data)
	if err != nil {
	}
	defer resp.Body.Close()

	statusCode = resp.StatusCode

	if statusCode != http.StatusOK {
		return "", fmt.Errorf("failed to get token: %s", resp.Status)
	}

	json.NewDecoder(resp.Body).Decode(&res)

	token = res.AccessToken

	if err := c.redis.SetWithExpiration(tokenKey, token, time.Duration(res.ExpiresIn-300)*time.Second); err != nil {
		return "", err
	}

	return token, nil
}

func (c *TAClient) GetPrice(ctx context.Context, request *entities.GetPriceRequest, tracing string) (*entities.GetPriceResponse, error) {
	const path = "api/ExternalBooking/GetExternalPrice"

	url, err := url.Parse(c.bookingURL)
	if err != nil {
		return nil, errors.Wrap(err, "url.Parse")
	}

	token, err := c.getToken(ctx, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "getToken")
	}

	header := c.getHeader(token)

	url = url.JoinPath(path)

	res, err := c.doRequest(ctx, http.MethodPost, path, url.String(), header, request, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.GetPriceResponse{}
	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	return out, nil
}

func (c *TAClient) getHeader(token string) map[string]string {
	return map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + token,
	}
}

func (c *TAClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("rateHawk Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))

		return nil, 0, duration, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)

	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != http.StatusOK {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *TAClient) doRequest(
	ctx context.Context,
	method string,
	action string,
	fullPath string,
	header map[string]string,
	body interface{},
	tracingID string,
) ([]byte, error) {
	var err error

	response, statusCode, duration, err := c.do(ctx, header, fullPath, method, action, body)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := fullPath
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		delete(headerClone, "Authorization")

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     method,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     action,
			Provider:   commonEnum.HotelProviderTA,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("RateHawk requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", action),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if err != nil {
		return nil, errors.Wrap(err, "c.do")
	}

	return response, nil
}

func (c *TAClient) CreateBooking(ctx context.Context, request *entities.CreateBookingRequest, tracing string) (*entities.CreateBookingResponse, error) {
	const path = "/api/ExternalBooking/Create"

	url, err := url.Parse(c.bookingURL)
	if err != nil {
		return nil, errors.Wrap(err, "url.Parse")
	}

	token, err := c.getToken(ctx, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "getToken")
	}

	header := c.getHeader(token)

	url = url.JoinPath(path)

	res, err := c.doRequest(ctx, http.MethodPost, path, url.String(), header, request, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.CreateBookingResponse{}
	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	return out, nil
}

func (c *TAClient) RetrieveBooking(ctx context.Context, bookingID, tracing string) (*entities.RetrieveBookingResponse, error) {
	const path = "/api/ExternalBooking/Retrieve/"

	url, err := url.Parse(c.bookingURL)
	if err != nil {
		return nil, errors.Wrap(err, "url.Parse")
	}
	url = url.JoinPath(path, bookingID)

	token, err := c.getToken(ctx, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "getToken")
	}

	header := c.getHeader(token)

	res, err := c.doRequest(ctx, http.MethodGet, path, url.String(), header, nil, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.RetrieveBookingResponse{}
	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	return out, nil
}

func (c *TAClient) ConfirmBooking(ctx context.Context, bookingID, tracing string) (*entities.CreateBookingResponse, error) {
	const path = "/api/ExternalBooking/Confirm/"

	url, err := url.Parse(c.bookingURL)
	if err != nil {
		return nil, errors.Wrap(err, "url.Parse")
	}
	url = url.JoinPath(path, bookingID)

	token, err := c.getToken(ctx, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "getToken")
	}

	header := c.getHeader(token)

	res, err := c.doRequest(ctx, http.MethodPut, path, url.String(), header, nil, tracing)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	out := &entities.CreateBookingResponse{}
	if err := json.Unmarshal(res, out); err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	return out, nil
}

func (c *TAClient) DeleteBooking(ctx context.Context, bookingID, tracing string) error {
	const path = "/api/ExternalBooking/Cancel/"

	url, err := url.Parse(c.bookingURL)
	if err != nil {
		return errors.Wrap(err, "url.Parse")
	}
	url = url.JoinPath(path, bookingID)

	token, err := c.getToken(ctx, tracing)
	if err != nil {
		return errors.Wrap(err, "getToken")
	}

	header := c.getHeader(token)

	res, err := c.doRequest(ctx, http.MethodDelete, path, url.String(), header, nil, tracing)
	if err != nil {
		return errors.Wrap(err, "doRequest")
	}

	out := &entities.DeleteBookingResponse{}
	if err := json.Unmarshal(res, out); err != nil {
		return errors.Wrap(err, "json.Unmarshal")
	}

	if out.StatusCode != http.StatusOK {
		return errors.New(out.Message)
	}

	return nil
}
