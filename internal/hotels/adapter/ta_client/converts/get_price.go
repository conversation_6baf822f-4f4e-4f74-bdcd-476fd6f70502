package converts

import (
	"fmt"
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func toUTCUnix(in string) (int64, error) {
	utcLoc, err := time.LoadLocation("UTC")
	if err != nil {
		return 0, err
	}

	t, err := time.ParseInLocation("2006-01-02", in, utcLoc)
	if err != nil {
		return 0, err
	}

	return t.Unix(), nil
}

func ToGetPriceRequest(req *domain.CacheCheckAvailabilityRequest, taHotelID, taRoomID, productName string, hubRateData *domain.HubRateData, bedOptionID string) (*entities.GetPriceRequest, error) {
	checkIn, err := toUTCUnix(req.Stay.CheckIn)
	if err != nil {
		return nil, err
	}

	checkOut, err := toUTCUnix(req.Stay.CheckOut)
	if err != nil {
		return nil, err
	}

	var bedOption *domain.BedOption

	for _, bed := range hubRateData.BedOptions {
		if bed.OptionID == bedOptionID {
			bedOption = bed
			break
		}
	}

	reqBeds := []entities.RequestBedGroup{}

	if bedOption != nil {
		for _, bedConfig := range bedOption.BedConfigs {
			reqBeds = append(reqBeds, entities.RequestBedGroup{
				Quantity: int(bedConfig.Quantity),
				Type:     bedConfig.Type,
			})
		}
	}

	reqOccus := []entities.RequestOccupancy{}
	reqOccuMap := map[string]*entities.RequestOccupancy{}

	genKey := func(a int, c []int) string {
		return fmt.Sprintf("%d_%v", a, c)
	}

	toIntArr := func(input []uint) []int {
		out := make([]int, len(input))
		for i, v := range input {
			out[i] = int(v)
		}

		return out
	}

	for _, occu := range req.Occupancies {
		var chd []int

		if occu.Children != nil {
			chd = toIntArr(occu.Children.Age)
		}

		key := genKey(int(occu.Adults), chd)

		if reqOccuMap[key] == nil {
			reqOccuMap[key] = &entities.RequestOccupancy{
				Quantity:     int(occu.Rooms),
				AdultSlot:    int(occu.Adults),
				ChildrenOlds: chd,
			}
		} else {
			reqOccuMap[key].Quantity += int(occu.Rooms)
		}
	}

	for _, occuReq := range reqOccuMap {
		reqOccus = append(reqOccus, *occuReq)
	}

	return &entities.GetPriceRequest{
		ManufacturerId:   taHotelID,
		ProductId:        taRoomID,
		ProductName:      productName,
		Currency:         hubRateData.Currency,
		CheckinTime:      checkIn,
		CheckoutTime:     checkOut,
		ProviderCode:     "TRAVEL",
		AgentCode:        "BIZITRIP",
		Occupancies:      reqOccus,
		IncludeBreakfast: hubRateData.HasBreakfast,
		Refundable:       hubRateData.Refundable,
		Smoking:          !hubRateData.NonSmoking,
		ExtraBed:         hubRateData.HasExtraBed,
		View:             "",
		BedGroups:        reqBeds,
		ExternalAmount:   hubRateData.PayNow,
		PricingType:      1,
	}, nil
}
