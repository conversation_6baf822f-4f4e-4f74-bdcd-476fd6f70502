package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func MapProviderBookingStatus(status string) enum.BookingStatus {
	switch status {
	case constants.BookingStatusCompleted:
		return enum.BookingStatusSuccess
	case constants.BookingStatusConfirmed:
		return enum.BookingStatusPending
	case constants.BookingStatusCanceled:
		return enum.BookingStatusCancel
	default:
		return enum.BookingStatusNone
	}
}

func MapReservationCode(order *domain.HubHotelOrder, input []entities.BookingConfirmationIdInfo) []*domain.HubRetrieveConfirmationID {
	if len(input) == 0 {
		return nil
	}

	out := []*domain.HubRetrieveConfirmationID{}
	baseCfID := order.ReservationCode

	for _, item := range input {
		if item.ConfirmationId != "" {
			baseCfID = item.ConfirmationId
			break
		}
	}

	if baseCfID == "" {
		return nil
	}

	for _, room := range order.Hotel.ListRooms {
		ok := false

		for _, info := range input {
			if info.ProductId == room.ProviderRoomID && !info.Used {
				out = append(out, &domain.HubRetrieveConfirmationID{
					ProviderRoomID: room.ProviderRoomID,
					ConfirmationID: info.ConfirmationId,
					OccupancyType:  room.OccupancyType,
					GivenName:      room.GivenName,
					Surname:        room.Surname,
					BedOptionID:    room.BedOption.OptionID,
					BookStatus:     enum.BookingStatusSuccess,
				})

				ok = true
				info.Used = true

				break
			}
		}

		if !ok {
			out = append(out, &domain.HubRetrieveConfirmationID{
				ProviderRoomID: room.ProviderRoomID,
				ConfirmationID: baseCfID,
				OccupancyType:  room.OccupancyType,
				GivenName:      room.GivenName,
				Surname:        room.Surname,
				BedOptionID:    room.BedOption.OptionID,
				BookStatus:     enum.BookingStatusSuccess,
			})
		}
	}

	return out
}
