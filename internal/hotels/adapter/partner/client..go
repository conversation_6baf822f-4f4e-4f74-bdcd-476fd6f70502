package partner

import (
	"context"
	"fmt"

	bPartnerPb "gitlab.deepgate.io/apps/api/gen/go/partner/backend"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/server"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type PartnerClient interface {
	AuthByOffice(ctx context.Context, req *domain.LoginReq) (*domain.PartnerUser, error)
	GetOfficeInfo(ctx context.Context, officeID, partnershipID string) (*domain.PartnerShopInfo, error)
	GetShopsByManagerID(ctx context.Context, pID, managerID string) ([]*domain.PartnerShopInfo, error)
	GetShopsBySaleCodes(ctx context.Context, pID string, saleCodes []string) ([]*domain.ShopsBySaleCode, error)
}

type partnerClient struct {
	cfg         *config.Schema
	partnerConn *grpc.ClientConn
}

func NewPartnerClient(cfg *config.Schema, partnerConn *grpc.ClientConn) PartnerClient {
	return &partnerClient{cfg, partnerConn}
}

// GetOfficeInfo implements partnerClient.
func (c *partnerClient) GetOfficeInfo(ctx context.Context, officeID, partnershipID string) (*domain.PartnerShopInfo, error) {
	client := bPartnerPb.NewPartnerShopServiceClient(c.partnerConn)

	hubPartnershipID := c.cfg.HubPartnershipID
	if partnershipID != "" {
		hubPartnershipID = partnershipID
	}
	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, hubPartnershipID)
	grpcCtx := metadata.NewOutgoingContext(ctx, md)

	res, err := client.GetByOfficeID(grpcCtx, &bPartnerPb.GetByOfficeIDReq{OfficeId: officeID})
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if res.Info == nil {
		return nil, fmt.Errorf("res info nil")
	}

	info := res.Info

	out := &domain.PartnerShopInfo{
		ID:          info.Id,
		Name:        info.Name,
		OwnerID:     info.Owner,
		PartnerType: info.PartnerType,
		Code:        info.Code,
		OfficeID:    info.OfficeId,
	}

	if info.Webhook != nil {
		webhookURLCfg := domain.WebhookURLCfg{
			Transaction:    "",
			ConfirmationID: "",
		}

		if info.Webhook.UrlConfig != nil {
			webhookURLCfg.Transaction = info.Webhook.UrlConfig.Transaction
			webhookURLCfg.ConfirmationID = info.Webhook.UrlConfig.ConfirmationId
		}

		out.WebhookCfg = &domain.WebhookCfg{
			WebhookKey:    info.Webhook.Key,
			WebhookURLCfg: webhookURLCfg,
		}
	}

	if info.Hotel != nil {
		providers := make([]*domain.ProviderConfig, len(info.Hotel.Providers))
		for i, provider := range info.Hotel.Providers {
			providers[i] = &domain.ProviderConfig{
				Provider: commonEnum.HotelProvider(provider.Provider),
				Enable:   provider.Enable,
			}
		}
		out.Hotel = &domain.PartnerHotelInfo{
			Enable:          info.Hotel.Enable,
			ProviderConfigs: providers,
			DefaultLanguage: info.Hotel.DefaultLanguage,
		}

		pCf := info.Hotel.PriceConditionConfig
		if pCf != nil {
			providerPriceRange := make(map[int64]domain.HotelPriceConditionDetail, 0)

			for provider, value := range pCf.PriceConditions {
				if value == nil {
					continue
				}

				providerPriceRange[provider] = domain.HotelPriceConditionDetail{
					Percent: value.Percent,
					Amount:  value.Amount,
				}
			}

			out.Hotel.PriceConfig = &domain.HotelPriceConditionConfig{
				ProviderOrder:      pCf.ProviderOrder,
				Active:             pCf.Active,
				ProviderPriceRange: providerPriceRange,
			}
		}
	}

	return out, nil
}

func (c *partnerClient) AuthByOffice(ctx context.Context, req *domain.LoginReq) (*domain.PartnerUser, error) {
	client := bPartnerPb.NewAuthServiceClient(c.partnerConn)

	partnershipID := c.cfg.HubPartnershipID
	if req.PartnershipID != "" {
		partnershipID = req.PartnershipID
	}
	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, partnershipID)
	grpcCtx := metadata.NewOutgoingContext(ctx, md)

	res, err := client.AuthenByOfficeID(grpcCtx, &bPartnerPb.AuthenByOfficeIDReq{
		OfficeId: req.OfficeID,
		ApiKey:   req.APIKey,
	})
	if err != nil {
		log.Error("partnerClient AuthByOffice AuthenByOfficeID error", log.Any("error", err), log.Any("req", req))
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if !res.IsSuccess {
		log.Error("partnerClient AuthByOffice AuthenByOfficeID response error", log.Any("res", res), log.Any("req", req))
		return nil, commonError.New(commonError.BadRequest, res.ErrorCode)
	}

	if res.Data == nil {
		log.Error("partner.UserInfo nil error", log.Any("res", res), log.Any("req", req))
		return nil, commonError.ErrSomethingOccurred
	}

	out := &domain.PartnerUser{
		ID:            res.Data.Id,
		CreatedAt:     res.Data.CreatedAt,
		UpdatedAt:     res.Data.UpdatedAt,
		CreatedBy:     res.Data.CreatedBy,
		UpdatedBy:     res.Data.UpdatedBy,
		Email:         res.Data.Email,
		Name:          res.Data.Name,
		PartnershipID: res.Data.PartnershipId,
		PartnerShopID: res.Data.PartnerShopId,
	}

	if res.WebhookCfg != nil {
		webhookURLCfg := domain.WebhookURLCfg{
			Transaction:    "",
			ConfirmationID: "",
		}

		if res.Data.WebhookUrlConfig != nil {
			webhookURLCfg.Transaction = res.Data.WebhookUrlConfig.Transaction
			webhookURLCfg.ConfirmationID = res.Data.WebhookUrlConfig.ConfirmationId
		}

		out.WebhookCfg = domain.WebhookCfg{
			WebhookKey:    res.WebhookCfg.Key,
			WebhookURLCfg: webhookURLCfg,
		}
	}

	return out, nil
}

func (c *partnerClient) GetShopsByManagerID(ctx context.Context, pID, managerID string) ([]*domain.PartnerShopInfo, error) {
	partnershipID := c.cfg.HubPartnershipID
	if pID != "" {
		partnershipID = pID
	}

	md := server.WithMetadataInternalPartner(c.cfg.InternalSecretToken, partnershipID, "")
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := bPartnerPb.NewPartnerShopServiceClient(c.partnerConn)

	res, err := client.GetShopsByManagerID(newCtx, &bPartnerPb.GetShopsByManagerIDReq{
		ManagerId: managerID,
	})
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	out := []*domain.PartnerShopInfo{}

	if len(res.Items) == 0 {
		return out, nil
	}

	for _, item := range res.Items {
		out = append(out, &domain.PartnerShopInfo{
			Name:     item.Name,
			Code:     item.Code,
			OfficeID: item.OfficeId,
		})
	}

	return out, nil
}
	
func (c *partnerClient) GetShopsBySaleCodes(ctx context.Context, pID string, saleCodes []string) ([]*domain.ShopsBySaleCode, error) {
	md := server.WithMetadataInternalPartner(c.cfg.InternalSecretToken, pID, "")
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := bPartnerPb.NewPartnerShopServiceClient(c.partnerConn)

	res, err := client.GetShopsBySaleCodes(newCtx, &bPartnerPb.GetShopsBySaleCodesReq{SaleCodes: saleCodes})
	if err != nil {
		log.Error("GetShopsBySaleCode", log.Any("Error:", err))
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}
	if res == nil {
		log.Error("GetShopsBySaleCode", log.String("Error:", "res is nil"))
		return nil, nil
	}

	return converts.ToDomainShopsBySaleCodes(res), nil
}
