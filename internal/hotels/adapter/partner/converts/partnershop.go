package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/partner"
	bPartnerPb "gitlab.deepgate.io/apps/api/gen/go/partner/backend"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func ToDomainProviderConfigs(ins []*partner.ProviderConfig) []*domain.ProviderConfig {
	out := []*domain.ProviderConfig{}

	for _, item := range ins {
		out = append(out, &domain.ProviderConfig{
			Provider: commonEnum.HotelProvider(item.Value),
			Enable:   item.Enabled,
		})
	}

	return out
}

func ToDomainPartnerShops(pbs []*partner.PartnerShopInfo) []*domain.PartnerShopInfo {
	if pbs == nil {
		return nil
	}

	if len(pbs) == 0 {
		return nil
	}

	domains := []*domain.PartnerShopInfo{}
	for _, pb := range pbs {
		if pb != nil {
			domains = append(domains, &domain.PartnerShopInfo{
				Code:     pb.Code,
				ID:       pb.Id,
				OfficeID: pb.OfficeId,
			})
		}
	}

	return domains
}
func ToDomainShopsBySaleCodes(pbs *bPartnerPb.GetShopsBySaleCodesRes) []*domain.ShopsBySaleCode {
	if pbs == nil || pbs.Items == nil {
		return nil
	}

	if len(pbs.Items) == 0 {
		return nil
	}

	domains := []*domain.ShopsBySaleCode{}
	for _, item := range pbs.Items {
		if item != nil {
			data := &domain.ShopsBySaleCode{
				Shops: ToDomainPartnerShops(item.Shops),
			}
			data.SaleCode = ""
			if item.SaleCode != nil {
				data.SaleCode = *item.SaleCode
			}

			domains = append(domains, data)
		}
	}

	return domains
}
