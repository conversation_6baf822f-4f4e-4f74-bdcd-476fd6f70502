package models

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"

type SearchHotelRequest struct {
	Occupancies   []*domain.HubSearchOccupancy `json:"occupancies"`
	Stay          domain.HubSearchStay         `json:"stay" validate:"required"`
	CountryCode   string                       `json:"country_code"`
	HotelIDs      []string                     `json:"hotel_ids" validate:"required"`
	RatePlanCount int                          `json:"rate_plan_count"`
}

type SearchHotelResponse struct {
	domain.ErrorRes
	SearchKey string             `json:"search_key,omitempty"`
	Hotels    []*domain.HubHotel `json:"hotels"`
}
