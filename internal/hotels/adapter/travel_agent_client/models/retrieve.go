package models

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type RetrieveBookingRes struct {
	domain.ErrorRes
	TAOrderCode    string             `json:"ta_order_code"`
	ConfirmationID []string           `json:"confirmation_id"`
	BookingStatus  enum.BookingStatus `json:"booking_status"`
	PayNow         float64            `json:"pay_now"`
	RefundAmount   float64            `json:"refund_amount"`
	Currency       string             `json:"currency"`
}
