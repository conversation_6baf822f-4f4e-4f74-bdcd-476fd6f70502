package converts

import (
	"fmt"

	"gitlab.deepgate.io/apps/common/helpers"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToSearchHotelRequest(in *domain.SearchHotelRequestData) *models.SearchHotelRequest {
	if in == nil || in.SearchReq == nil {
		return nil
	}

	result := &models.SearchHotelRequest{
		Occupancies:   in.SearchReq.Occupancies,
		Stay:          in.SearchReq.Stay,
		CountryCode:   in.SearchReq.CountryCode,
		HotelIDs:      in.ProviderHotelIds,
		RatePlanCount: in.SearchReq.RatePlanCount,
	}

	if result.RatePlanCount == 0 {
		result.RatePlanCount = constants.DefaultRatePlanCount
	}

	return result
}

func MappingRateAmenities(in *domain.HubRateData, amenities map[string]*domain.AmenityContent, bedOptions map[string]*domain.BedOptionContent) {
	if in == nil {
		return
	}

	for _, amenity := range in.Amenities {
		if value, ok := amenities[amenity.ID]; ok {
			amenity.Name = value.Name
		}
	}

	for _, bedOption := range in.BedOptions {
		if value, ok := bedOptions[bedOption.OptionID]; ok {
			bedOption.Name = value.Name
		}
	}
}

func ProcessHubHotel(in *domain.HubHotel, searchKey string, amenities map[string]*domain.AmenityContent, bedOptions map[string]*domain.BedOptionContent) {
	if in.Currency != "" || len(in.ListRooms) == 0 {
		return
	}

	if in.ProviderHotelID == "" {
		in.ProviderHotelID = in.HotelID
	}

	for _, room := range in.ListRooms {
		room.ProviderRoomID = room.RoomID
		room.BedGroups = []*domain.BedGroup{}

		for _, rate := range room.RateData {
			if in.Currency == "" {
				in.Currency = rate.Currency
			}

			for _, policy := range rate.CancelPolicies {
				policy.Refundable = policy.PenaltyAmount != rate.PayNow
			}

			rate.Available = fmt.Sprintf("%d", helpers.GenerateRandomNumber(1)+5) // 5 -> 14 rooms
			MappingRateAmenities(rate, amenities, bedOptions)

			for _, bed := range rate.BedOptions {
				bed.PriceCheckToken = searchKey
			}
		}
	}
}

func ProcessHubHotels(in []*domain.HubHotel, searchKey string, amenities map[string]*domain.AmenityContent, bedOptions map[string]*domain.BedOptionContent) {
	for _, item := range in {
		ProcessHubHotel(item, searchKey, amenities, bedOptions)
	}
}
