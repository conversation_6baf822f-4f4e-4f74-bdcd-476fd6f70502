package travel_agent_client

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type TravelAgentAdapter interface {
	SearchHotel(ctx context.Context, req *domain.SearchHotelRequestData, provider commonEnum.HotelProvider, tracingID string) ([]*domain.HubHotel, error)
	PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq, provider commonEnum.HotelProvider, tracingID string) (*domain.HubPriceCheckRes, error)
	RetrieveBooking(ctx context.Context, req *domain.HubRetrieveBookingReq, provider commonEnum.HotelProvider, tracingID string) ([]string, string, enum.BookingStatus, error)
	CancelBooking(ctx context.Context, req *domain.HubCancelBookingReq, provider commonEnum.HotelProvider, tracingID string) error
	Booking(ctx context.Context, req *domain.HubBookReq, provider commonEnum.HotelProvider, sessionKey, orderCode string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error)
	RetrieveBookingForRefund(ctx context.Context, req *domain.HubRetrieveBookingReq, provider commonEnum.HotelProvider, autoCancel bool, tracingID string) (bool, bool, *domain.RefundData)
}

type travelAgentAdapter struct {
	hnhTAClient          client.TravelAgentClient
	mayTAClient          client.TravelAgentClient
	bztTAClient          client.TravelAgentClient
	contentAmenitiesRepo repositories.AmenityRepository
	contentBedOptionRepo repositories.BedOptionRepository
	cfg                  *config.Schema
}

func NewTravelAgentAdapter(
	cfg *config.Schema,
	redis redis.IRedis,
	requestRepo repositories.RequestRepository,
	contentAmenitiesRepo repositories.AmenityRepository,
	contentBedOptionRepo repositories.BedOptionRepository,
) TravelAgentAdapter {
	return &travelAgentAdapter{
		hnhTAClient:          client.NewTravelAgentClient(cfg, requestRepo, cfg.HNHTravelAgentOfficeID, cfg.HNHTravelAgentApiKey, cfg.HNHTravelAgentPartnershipID),
		mayTAClient:          client.NewTravelAgentClient(cfg, requestRepo, cfg.MayTravelAgentOfficeID, cfg.MayTravelAgentApiKey, cfg.MayTravelAgentPartnershipID),
		bztTAClient:          client.NewTravelAgentClient(cfg, requestRepo, cfg.BZTTravelAgentOfficeID, cfg.BZTTravelAgentApiKey, cfg.BZTTravelAgentPartnershipID),
		contentAmenitiesRepo: contentAmenitiesRepo,
		contentBedOptionRepo: contentBedOptionRepo,
		cfg:                  cfg,
	}
}

func (a *travelAgentAdapter) GetAmenitiesMap(ctx context.Context, language string) (map[string]*domain.AmenityContent, error) {
	amenities, err := a.contentAmenitiesRepo.FindByLanguage(ctx, language)
	if err != nil {
		return nil, errors.Wrap(err, "contentAmenitiesRepo.FindByLanguage")
	}

	result := make(map[string]*domain.AmenityContent)

	for _, amenity := range amenities {
		result[amenity.AmenityID] = amenity
	}

	return result, nil
}

func (a *travelAgentAdapter) GetBedOptionsMap(ctx context.Context, language string) (map[string]*domain.BedOptionContent, error) {
	bedOptions, err := a.contentBedOptionRepo.FindByLanguage(ctx, language)
	if err != nil {
		return nil, errors.Wrap(err, "contentBedOptionRepo.FindByLanguage")
	}

	result := make(map[string]*domain.BedOptionContent)

	for _, bedOption := range bedOptions {
		result[bedOption.BedID] = bedOption
	}

	return result, nil
}

func (a *travelAgentAdapter) GetProvider(_ context.Context, provider commonEnum.HotelProvider) (client.TravelAgentClient, error) {
	switch provider { //nolint
	case commonEnum.HotelProviderHNHTravelAgent:
		return a.hnhTAClient, nil
	case commonEnum.HotelProviderMayTravelAgent:
		return a.mayTAClient, nil
	case commonEnum.HotelProviderBZTTravelAgent:
		return a.bztTAClient, nil
	}

	return nil, domain.ErrProviderNotAllowed
}

func (a *travelAgentAdapter) SearchHotel(ctx context.Context, req *domain.SearchHotelRequestData, provider commonEnum.HotelProvider, tracingID string) ([]*domain.HubHotel, error) {
	if req == nil {
		log.Error("travelAgentAdapter.SearchHotel: req == nil")
		return nil, domain.ErrInvalidValue
	}

	searchReq := converts.ToSearchHotelRequest(req)
	if searchReq == nil {
		log.Error("travelAgentAdapter.SearchHotel: searchReq == nil")
		return nil, domain.ErrInvalidValue
	}

	client, err := a.GetProvider(ctx, provider)
	if err != nil {
		log.Error("GetProvider err", log.Any("err", err), log.Any("provider", provider))
		return nil, errors.Wrap(err, "hnhTAClient.SearchHotel")
	}

	response, err := client.SearchHotel(ctx, searchReq, tracingID)
	if err != nil {
		log.Error("hnhTAClient.SearchHotel error", log.Any("req", searchReq), log.Any("err", err))
		return nil, errors.Wrap(err, "hnhTAClient.SearchHotel")
	}

	amenitiesMap, err := a.GetAmenitiesMap(ctx, req.SearchReq.Language)
	if err != nil {
		log.Error("hnhTAClient.GetAmenitiesMap error", log.Any("language", req.SearchReq.Language), log.Any("err", err))
		amenitiesMap = make(map[string]*domain.AmenityContent)
	}

	bedOptionsMap, err := a.GetBedOptionsMap(ctx, req.SearchReq.Language)
	if err != nil {
		log.Error("hnhTAClient.GetBedOptionsMap error", log.Any("language", req.SearchReq.Language), log.Any("err", err))
		amenitiesMap = make(map[string]*domain.AmenityContent)
	}

	converts.ProcessHubHotels(response.Hotels, response.SearchKey, amenitiesMap, bedOptionsMap)

	return response.Hotels, nil
}

func (a *travelAgentAdapter) PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq, provider commonEnum.HotelProvider, tracingID string) (*domain.HubPriceCheckRes, error) {
	if req == nil {
		log.Error("PriceCheck req == nil")
		return nil, domain.ErrInvalidValue
	}

	client, err := a.GetProvider(ctx, provider)
	if err != nil {
		log.Error("GetProvider err", log.Any("err", err), log.Any("provider", provider))
		return nil, errors.Wrap(err, "hnhTAClient.PriceCheck")
	}

	response, err := client.PriceCheck(ctx, req, tracingID)
	if err != nil {
		log.Error("hnhTAClient.PriceCheck error", log.Any("req", req), log.Any("err", err))
		return nil, errors.Wrap(err, "hnhTAClient.PriceCheck")
	}

	return response, nil
}

func (a *travelAgentAdapter) RetrieveBooking(ctx context.Context, req *domain.HubRetrieveBookingReq, provider commonEnum.HotelProvider, tracingID string) ([]string, string, enum.BookingStatus, error) {
	if req == nil {
		log.Error("travelAgentAdapter.RetrieveBooking: req == nil")
		return nil, "", enum.BookingStatusPending, domain.ErrInvalidValue
	}

	client, err := a.GetProvider(ctx, provider)
	if err != nil {
		log.Error("GetProvider err", log.Any("err", err), log.Any("provider", provider))
		return nil, "", enum.BookingStatusPending, errors.Wrap(err, "hnhTAClient.PriceCheck")
	}

	hotelConfirmationIDs, confirmationID, status, err := client.RetrieveBooking(ctx, req, tracingID)
	if err != nil {
		log.Error("hnhTAClient.RetrieveBooking error", log.Any("req", req), log.Any("err", err))
		return hotelConfirmationIDs, "", status, errors.Wrap(err, "hnhTAClient.RetrieveBooking")
	}

	return hotelConfirmationIDs, confirmationID, status, nil
}

func (a *travelAgentAdapter) RetrieveBookingForRefund(ctx context.Context, req *domain.HubRetrieveBookingReq, provider commonEnum.HotelProvider, autoCancel bool, tracingID string) (bool, bool, *domain.RefundData) {
	if req == nil {
		return false, false, nil
	}

	client, err := a.GetProvider(ctx, provider)
	if err != nil {
		log.Error("GetProvider err", log.Any("err", err), log.Any("provider", provider))
		return false, false, nil
	}

	return client.RetrieveBookingForRefund(ctx, req, autoCancel, tracingID)
}

func (a *travelAgentAdapter) CancelBooking(ctx context.Context, req *domain.HubCancelBookingReq, provider commonEnum.HotelProvider, tracingID string) error {
	if req == nil {
		log.Error("travelAgentAdapter.CancelBooking: req == nil")
		return domain.ErrInvalidValue
	}

	client, err := a.GetProvider(ctx, provider)
	if err != nil {
		log.Error("GetProvider err", log.Any("err", err), log.Any("provider", provider))
		return errors.Wrap(err, "hnhTAClient.CancelBooking")
	}

	err = client.CancelBooking(ctx, req, tracingID)
	if err != nil {
		log.Error("hnhTAClient.CancelBooking error", log.Any("req", req), log.Any("err", err))
		return errors.Wrap(err, "hnhTAClient.CancelBooking")
	}

	return nil
}

func (a *travelAgentAdapter) Booking(ctx context.Context, req *domain.HubBookReq, provider commonEnum.HotelProvider, sessionKey, orderCode string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	if req == nil {
		log.Error("travelAgentAdapter.Booking: req == nil")
		return "", nil, enum.BookingStatusFailed, domain.ErrInvalidValue
	}

	bookingReq := &models.CreateBookingReq{
		HubBookReq: req,
		OrderCode:  orderCode,
	}
	bookingReq.SessionID = sessionKey

	client, err := a.GetProvider(ctx, provider)
	if err != nil {
		log.Error("GetProvider err", log.Any("err", err), log.Any("provider", provider))
		return "", nil, enum.BookingStatusFailed, errors.Wrap(err, "hnhTAClient.GetProvider")
	}

	response, err := client.Booking(ctx, bookingReq, orderCode)
	if err != nil {
		log.Error("hnhTAClient.Booking error", log.Any("req", req), log.Any("err", err))
		return "", nil, enum.BookingStatusPending, errors.Wrap(err, "hnhTAClient.Booking")
	}

	if response == nil || !response.IsSuccess {
		log.Error("hnhTAClient.Booking response err", log.Any("response", response))
	}

	return "", nil, enum.BookingStatusPending, nil
}
