package client

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (c *travelAgentClient) getHeader() map[string]string {
	return map[string]string{
		auth.OfficeIDHeaderKey:     c.officeID,
		auth.APIKeyHeader:          c.apiKey,
		auth.PartnershipContextKey: c.partnershipID,
		"Content-Type":             "application/json",
	}
}

func (c *travelAgentClient) doRequest( //nolint:funlen
	ctx context.Context,
	method string,
	fullPath string,
	header map[string]string,
	body interface{},
	action string,
	tracingID string,
	responseData interface{},
) error {
	var err error

	response, statusCode, duration, err := c.do(ctx, header, fullPath, method, action, body)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	headerClone["x-api-key"] = "*"

	go func(headerClone map[string]string) {
		path := fullPath
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		doErr := err

		// TODO add provider correct for ta_client
		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     action,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("Expedia requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", action),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if statusCode != http.StatusNoContent {
		if err := json.Unmarshal(response, responseData); err != nil {
			return errors.Wrap(err, "Unmarshal")
		}
	}

	if err != nil {
		return errors.Wrap(err, "c.do")
	}

	return nil
}

func (c *travelAgentClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) ([]byte, int, int64, error) {
	var duration int64
	beginAt := time.Now()

	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("expedia Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))

		return nil, 0, duration, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)

	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		duration = time.Since(beginAt).Milliseconds()
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	duration = time.Since(beginAt).Milliseconds()

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode >= http.StatusBadRequest {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}
