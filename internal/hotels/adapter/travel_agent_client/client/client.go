package client

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	successfulCode             = 200
	methodGet                  = "GET"
	methodPost                 = "POST"
	defaultRequestTimeout      = 60
	actionSearchHotel          = "search_hotel"
	actionSearchDestinations   = "search-destinations"
	actionPriceCheckHotel      = "price_check_hotel"
	actionRetrieveBookingHotel = "retrieve_booking_hotel"
	actionBookingHotel         = "booking_hotel"
	actionGetHotelReviews      = "hotel_reviews"
	actionCancelBooking        = "cancel_booking"

	searchHotelPath            = "/hotels/search-hotels"
	searchDestinationsPath     = "/hotels/search-destinations"
	checkAvailabilityHotelPath = "/hotels/check-availability"
	priceCheckHotelPath        = "/hotels/price-check"
	retrieveBookingHotelPath   = "/hotels/retrieve"
	bookingHotelPath           = "/hotels/book"
	getHotelReviewsPath        = "/hotels/hotel-reviews"
	cancelBookingPath          = "/hotels/cancel-booking"
)

type TravelAgentClient interface {
	SearchHotel(ctx context.Context, req *models.SearchHotelRequest, tracingID string) (*models.SearchHotelResponse, error)
	PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq, tracingID string) (*domain.HubPriceCheckRes, error)
	RetrieveBooking(ctx context.Context, req *domain.HubRetrieveBookingReq, tracingID string) ([]string, string, enum.BookingStatus, error)
	CancelBooking(ctx context.Context, req *domain.HubCancelBookingReq, tracingID string) error
	Booking(ctx context.Context, req *models.CreateBookingReq, tracingID string) (*domain.HubBookRes, error)
	RetrieveBookingForRefund(ctx context.Context, req *domain.HubRetrieveBookingReq, autoCancel bool, tracingID string) (bool, bool, *domain.RefundData)
}

type travelAgentClient struct {
	baseUrl       string
	officeID      string
	apiKey        string
	partnershipID string
	requestRepo   repositories.RequestRepository
}

func NewTravelAgentClient(cfg *config.Schema, requestRepo repositories.RequestRepository, officeID, apiKey, partnershipID string) TravelAgentClient {
	return &travelAgentClient{
		baseUrl:       cfg.TravelAgentEndPoint,
		officeID:      officeID,
		apiKey:        apiKey,
		requestRepo:   requestRepo,
		partnershipID: partnershipID,
	}
}

func (c *travelAgentClient) SearchHotel(ctx context.Context, req *models.SearchHotelRequest, tracingID string) (*models.SearchHotelResponse, error) {
	response := &models.SearchHotelResponse{}

	err := c.doRequest(ctx, methodPost, c.baseUrl+searchHotelPath, c.getHeader(), req, actionSearchHotel, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if response == nil || !response.IsSuccess {
		log.Info("travelAgentClient SearchHotel response err", log.Any("response", response))
		return response, nil
	}

	return response, nil
}

func (c *travelAgentClient) PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq, tracingID string) (*domain.HubPriceCheckRes, error) {
	response := &domain.HubPriceCheckRes{}

	priceCheckReq := &models.PriceCheckReq{
		SearchKey:   req.SearchKey,
		HotelID:     req.HotelID,
		RoomID:      req.RoomID,
		RateID:      req.RateID,
		BedOptionID: req.BedOptionID,
		Currency:    constants.VNDCurrency,
	}

	err := c.doRequest(ctx, methodPost, c.baseUrl+priceCheckHotelPath, c.getHeader(), priceCheckReq, actionPriceCheckHotel, tracingID, &response)
	if err != nil {
		if response != nil && response.ErrorCode == constants.ErrCodeRoomSoldOut {
			return nil, domain.ErrRoomSoldOut
		}

		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *travelAgentClient) RetrieveBooking(ctx context.Context, req *domain.HubRetrieveBookingReq, tracingID string) ([]string, string, enum.BookingStatus, error) {
	response := &models.RetrieveBookingRes{}
	req.OfficeID = c.officeID

	err := c.doRequest(ctx, methodGet, c.baseUrl+retrieveBookingHotelPath, c.getHeader(), req, actionRetrieveBookingHotel, tracingID, &response)
	if err != nil {
		return nil, "", enum.BookingStatusPending, errors.Wrap(err, "doRequest")
	}

	if response == nil || !response.IsSuccess {
		log.Error("travelAgentClient RetrieveBooking response err", log.Any("response", response))
		return nil, "", enum.BookingStatusPending, nil
	}

	return response.ConfirmationID, response.TAOrderCode, response.BookingStatus, nil
}

func (c *travelAgentClient) RetrieveBookingForRefund(ctx context.Context, req *domain.HubRetrieveBookingReq, autoCancel bool, tracingID string) (bool, bool, *domain.RefundData) {
	response := &models.RetrieveBookingRes{}
	req.OfficeID = c.officeID

	err := c.doRequest(ctx, methodGet, c.baseUrl+retrieveBookingHotelPath, c.getHeader(), req, actionRetrieveBookingHotel, tracingID, &response)
	if err != nil {
		return false, false, &domain.RefundData{}
	}

	if response == nil || !response.IsSuccess {
		log.Error("travelAgentClient RetrieveBooking response err", log.Any("response", response))
		return false, false, &domain.RefundData{}
	}

	if autoCancel {
		// Auto cancel +  refund full
		cancelStatus := []enum.BookingStatus{enum.BookingStatusCancel, enum.BookingStatusCanceling}

		response.RefundAmount = response.PayNow

		return helpers.Contains(cancelStatus, response.BookingStatus),
			true, &domain.RefundData{
				ProviderRefundAmount: response.RefundAmount,
				PenaltyAmount:        response.PayNow - response.RefundAmount,
				Currency:             response.Currency,
			}
	}

	return response.BookingStatus == enum.BookingStatusCancel, response.PayNow == response.RefundAmount, &domain.RefundData{
		ProviderRefundAmount: response.RefundAmount,
		PenaltyAmount:        response.PayNow - response.RefundAmount,
		Currency:             response.Currency,
	}
}

func (c *travelAgentClient) CancelBooking(ctx context.Context, req *domain.HubCancelBookingReq, tracingID string) error {
	req.OfficeID = c.officeID

	err := c.doRequest(ctx, methodPost, c.baseUrl+cancelBookingPath, c.getHeader(), req, actionRetrieveBookingHotel, tracingID, nil)
	if err != nil {
		return errors.Wrap(err, "doRequest")
	}

	return nil
}

func (c *travelAgentClient) Booking(ctx context.Context, req *models.CreateBookingReq, tracingID string) (*domain.HubBookRes, error) {
	response := &domain.HubBookRes{}

	err := c.doRequest(ctx, methodPost, c.baseUrl+bookingHotelPath, c.getHeader(), req, actionBookingHotel, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}
