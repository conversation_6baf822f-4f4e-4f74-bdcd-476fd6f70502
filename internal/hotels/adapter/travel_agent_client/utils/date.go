package utils

import (
	"time"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const ExpediaSearchDateFormat = "2006-01-02"

// parse HubSearch date string to Expedia Search String.
func ConvertToExpediaTimeStr(timeStr string) (string, error) {
	t, err := time.Parse(constants.HubSearchDateFormat, timeStr)
	if err != nil {
		log.Error("ConvertToExpediaTimeStr parse err", log.Any("err", err), log.String("timeStr", timeStr))
		return "", err
	}

	return t.Format(ExpediaSearchDateFormat), nil
}

func ConvertToExpediaFullDateStr(timeStr string) (string, error) {
	t, err := time.Parse(time.RFC3339Nano, timeStr)
	if err != nil {
		log.Error("ConvertToExpediaFullDateStr parse err", log.Any("err", err), log.String("timeStr", timeStr))
		return "", err
	}

	return t.Format(constants.HubDateFormat), nil
}
