package hub_provider

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ProcessHubRateData(in *domain.HubRateData) {
	if in == nil {
		return
	}

	for _, policy := range in.CancelPolicies {
		policy.Refundable = policy.PenaltyAmount != in.PayNow
		policy.PartialRefund = policy.PenaltyAmount != 0
	}
}

func ProcessHubHotel(in *domain.HubHotel) {
	if in.Currency != "" || len(in.ListRooms) == 0 {
		return
	}

	if in.ProviderHotelID == "" {
		in.ProviderHotelID = in.HotelID
	}

	for _, room := range in.ListRooms {
		room.ProviderRoomID = room.RoomID

		for _, rate := range room.RateData {
			if in.Currency == "" {
				in.Currency = rate.Currency
			}

			ProcessHubRateData(rate)
		}
	}
}

func ProcessHubHotels(in []*domain.HubHotel) {
	for _, item := range in {
		ProcessHubHotel(item)
	}
}
