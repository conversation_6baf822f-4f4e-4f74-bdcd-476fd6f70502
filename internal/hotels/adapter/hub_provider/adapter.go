package hub_provider

import (
	"context"
	"fmt"

	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

// SUB Hub sử dụng chính xác request/response types từ HTTP port.
type HubAdapter interface {
	// SearchHotel sử dụng HubSearchHotelRequest từ HTTP port
	SearchHotel(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error)
	SearchListHotel(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error)

	// CheckAvailability sử dụng HubCheckAvailabilityReq từ HTTP port
	CheckAvailability(ctx context.Context, req *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error)

	// PriceCheck sử dụng HubPriceCheckReq từ HTTP port
	PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error)

	// Book sử dụng HubBookReq từ HTTP port
	Book(ctx context.Context, req *domain.HubBookReq) (*domain.HubBookRes, error)

	// Retrieve sử dụng HubRetrieveBookingReq từ HTTP port
	Retrieve(ctx context.Context, req *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error)

	// Cancel booking methods - giữ nguyên vì không có HTTP endpoint tương ứng
	Cancel(ctx context.Context, orderCode, tracingID string) error
}

// hubAdapter implements HubAdapter interface.
type hubAdapter struct {
	client Client
	cfg    *config.Schema
}

// NewHubAdapter tạo Hub adapter mới.
func NewHubAdapter(client Client, cfg *config.Schema) HubAdapter {
	return &hubAdapter{
		client: client,
		cfg:    cfg,
	}
}

// SearchHotel gọi Hub search hotels API với HubSearchHotelRequest từ HTTP port.
func (h *hubAdapter) SearchHotel(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	// Lấy SUB Hub context từ request context
	subHubContext := h.getSubHubContext(ctx)
	if subHubContext == nil {
		return nil, fmt.Errorf("SUB Hub context not found in request")
	}

	// Gọi Hub API với request từ HTTP port
	var response *domain.HubSearchHotelResponse

	err := h.client.DoRequest(ctx, "POST", constants.PathSearchHotels, MapSubHubContextToHeaders(subHubContext), req, "", &response)
	if err != nil {
		log.Error("Hub SearchHotels API call failed",
			log.Any("error", err),
			log.String("correlation_id", subHubContext.CorrelationID))

		return nil, fmt.Errorf("hub search hotels failed: %w", err)
	}

	return response, nil
}

func (h *hubAdapter) SearchListHotel(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	// Lấy SUB Hub context từ request context
	subHubContext := h.getSubHubContext(ctx)
	if subHubContext == nil {
		return nil, fmt.Errorf("SUB Hub context not found in request")
	}

	// Gọi Hub API với request từ HTTP port
	var response *domain.HubSearchHotelResponse

	err := h.client.DoRequest(ctx, "POST", constants.PathSearchHotelList, MapSubHubContextToHeaders(subHubContext), req, "", &response)
	if err != nil {
		log.Error("Hub SearchHotels API call failed",
			log.Any("error", err),
			log.String("correlation_id", subHubContext.CorrelationID))

		return nil, fmt.Errorf("hub search hotels failed: %w", err)
	}

	return response, nil
}

// CheckAvailability gọi Hub check availability API với HubCheckAvailabilityReq từ HTTP port.
func (h *hubAdapter) CheckAvailability(ctx context.Context, req *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error) {
	subHubContext := h.getSubHubContext(ctx)
	if subHubContext == nil {
		return nil, fmt.Errorf("SUB Hub context not found in request")
	}

	// Gọi Hub API với request từ HTTP port
	var response *domain.HubCheckAvailabilityRes

	err := h.client.DoRequest(ctx, "POST", constants.PathCheckAvailability, MapSubHubContextToHeaders(subHubContext), req, "", &response)
	if err != nil {
		log.Error("Hub CheckAvailability API call failed",
			log.Any("error", err),
			log.String("correlation_id", subHubContext.CorrelationID))

		return nil, fmt.Errorf("hub check availability failed: %w", err)
	}

	if response != nil {
		ProcessHubHotels(response.Hotels)
	}

	return response, nil
}

// PriceCheck gọi Hub price check API với HubPriceCheckReq từ HTTP port.
func (h *hubAdapter) PriceCheck(ctx context.Context, req *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error) {
	subHubContext := h.getSubHubContext(ctx)
	if subHubContext == nil {
		return nil, fmt.Errorf("SUB Hub context not found in request")
	}

	// Gọi Hub API với request từ HTTP port
	var response *domain.HubPriceCheckRes

	err := h.client.DoRequest(ctx, "POST", constants.PathPriceCheck, MapSubHubContextToHeaders(subHubContext), req, req.SearchKey, &response)
	if err != nil {
		log.Error("Hub PriceCheck API call failed",
			log.Any("error", err),
			log.String("correlation_id", subHubContext.CorrelationID))

		return nil, fmt.Errorf("hub price check failed: %w", err)
	}

	if response != nil {
		ProcessHubRateData(response.RateDataCf)
		ProcessHubRateData(response.RateData)
	}

	return response, nil
}

// Book gọi Hub booking API với HubBookReq từ HTTP port.
func (h *hubAdapter) Book(ctx context.Context, req *domain.HubBookReq) (*domain.HubBookRes, error) {
	subHubContext := h.getSubHubContext(ctx)
	if subHubContext == nil {
		return nil, fmt.Errorf("SUB Hub context not found in request")
	}

	// Gọi Hub API với request từ HTTP port
	var response *domain.HubBookRes

	err := h.client.DoRequest(ctx, "POST", constants.PathBookHotel, MapSubHubContextToHeaders(subHubContext), req, req.SessionID, &response)
	if err != nil {
		log.Error("Hub Book API call failed",
			log.Any("error", err),
			log.String("correlation_id", subHubContext.CorrelationID))

		return nil, fmt.Errorf("hub book failed: %w", err)
	}

	return response, nil
}

// Retrieve gọi Hub retrieve booking API với HubRetrieveBookingReq từ HTTP port.
func (h *hubAdapter) Retrieve(ctx context.Context, req *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error) {
	subHubContext := h.getSubHubContext(ctx)
	if subHubContext == nil {
		return nil, fmt.Errorf("SUB Hub context not found in request")
	}

	// Gọi Hub API với request từ HTTP port
	var response *domain.HubRetrieveBookingRes
	request := &domain.HubRetrieveBookingReq{
		OrderCode: req.OrderCode,
	}

	err := h.client.DoRequest(ctx, "POST", constants.PathRetrieveBooking, MapSubHubContextToHeaders(subHubContext), request, "", &response)
	if err != nil {
		log.Error("Hub Retrieve API call failed",
			log.Any("error", err),
			log.String("correlation_id", subHubContext.CorrelationID))

		return nil, fmt.Errorf("hub retrieve failed: %w", err)
	}

	return response, nil
}

// Cancel gọi Hub cancel booking API.
func (h *hubAdapter) Cancel(ctx context.Context, orderCode, tracingID string) error {
	subHubContext := h.getSubHubContext(ctx)
	if subHubContext == nil {
		return fmt.Errorf("SUB Hub context not found in request")
	}

	// Tạo request payload cho Hub
	cancelReq := &domain.HubCancelBookingReq{
		OrderCode: orderCode,
	}

	var response domain.ErrorRes

	err := h.client.DoRequest(ctx, "POST", constants.PathCancelBooking, MapSubHubContextToHeaders(subHubContext), cancelReq, "", &response)
	if err != nil {
		log.Error("Hub Cancel API call failed",
			log.Any("error", err),
			log.String("correlation_id", subHubContext.CorrelationID))

		return fmt.Errorf("hub cancel failed: %w", err)
	}

	if response.ErrorCode != "" {
		return fmt.Errorf(response.ErrorCode)
	}

	return nil
}

// getSubHubContext lấy SUB Hub context từ request context.
func (h *hubAdapter) getSubHubContext(ctx context.Context) *config.SubHubContext {
	if subHubContext, ok := ctx.Value("sub_hub_context").(*config.SubHubContext); ok {
		return subHubContext
	}

	return nil
}

func MapSubHubContextToHeaders(subHubContext *config.SubHubContext) map[string]string {
	headers := make(map[string]string)

	// Basic headers
	headers["Content-Type"] = "application/json"
	headers[auth.OfficeIDHeaderKey] = subHubContext.SubHubMapping.HubOfficeID
	headers[auth.APIKeyHeader] = subHubContext.SubHubMapping.HubAPIKey

	// Forward client headers from SubHubContext
	if subHubContext.ClientHeaders != nil {
		if subHubContext.ClientHeaders.UserAgent != "" {
			headers[constants.HeaderKeyClientBrowserAgent] = subHubContext.ClientHeaders.UserAgent
		}

		if subHubContext.ClientHeaders.IP != "" {
			headers[constants.HeaderKeyClientIP] = subHubContext.ClientHeaders.IP
		}

		if subHubContext.ClientHeaders.Country != "" {
			headers[constants.HeaderKeyClientCountryCode] = subHubContext.ClientHeaders.Country
		}
	}

	return headers
}
