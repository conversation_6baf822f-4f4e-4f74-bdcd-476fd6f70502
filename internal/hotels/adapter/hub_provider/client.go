package hub_provider

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

// Client interface - Đơn giản hóa chỉ cần 1 method chính.
type Client interface {
	DoRequest(ctx context.Context, method string, apiPath string, header map[string]string, body interface{}, tracingID string, responseData interface{}) error
}

// Client - Hub Provider client đơn giản.
type client struct {
	baseURL     string
	requestRepo repositories.RequestRepository
}

// NewClient tạo Hub Provider client mới.
func NewClient(cfg *config.Schema, requestRepo repositories.RequestRepository) *client {
	return &client{
		baseURL:     fmt.Sprintf("http://localhost:%s", cfg.HTTPPort),
		requestRepo: requestRepo,
	}
}

func (c *client) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("rateHawk Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))

		return nil, 0, duration, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)

	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *client) DoRequest(
	ctx context.Context,
	method string,
	apiPath string,
	header map[string]string,
	body interface{},
	tracingID string,
	responseData interface{},
) error {
	var err error

	fullPath, err := url.JoinPath(c.baseURL, apiPath)
	if err != nil {
		return errors.Wrap(err, "Parse base URL")
	}

	response, statusCode, duration, err := c.do(ctx, header, fullPath, method, apiPath, body)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := fullPath
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     method,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     apiPath,
			Provider:   commonEnum.HotelProviderHub,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		}); err != nil {
			log.Error("Hub requestRepo.CreateV2 error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", apiPath),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if err != nil {
		return errors.Wrap(err, "c.do")
	}

	if len(response) == 0 {
		return nil
	}

	if err := json.Unmarshal(response, responseData); err != nil {
		return errors.Wrap(err, "Unmarshal")
	}

	return nil
}
