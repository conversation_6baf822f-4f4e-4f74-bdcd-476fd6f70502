package order

import (
	"context"

	"github.com/pkg/errors"
	backendPb "gitlab.deepgate.io/apps/api/gen/go/order/backend"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"google.golang.org/grpc/metadata"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/order/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type orderServiceClient struct {
	cfg *config.Schema
}

type OrderServiceClient interface {
	CreateHotelOrder(ctx context.Context, order *domain.HotelOrder) (string, error)
	PlaceHotelOrder(ctx context.Context, order *domain.HubHotelOrder, shopInfo *domain.PartnerShopInfo) (*domain.TransactionInfo, error)
}

func NewOrderServiceClient(cfg *config.Schema) OrderServiceClient {
	return &orderServiceClient{
		cfg: cfg,
	}
}

func (s *orderServiceClient) CreateHotelOrder(ctx context.Context, order *domain.HotelOrder) (string, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.OrderServiceEndpoint)
	if err != nil {
		return "", errors.Wrap(err, "Cannot connect order service")
	}
	defer conn.Close()

	partnershipID := order.PartnershipID
	if partnershipID == "" {
		partnershipID = s.cfg.HubPartnershipID
	}
	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, partnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := backendPb.NewOrderServiceClient(conn)

	res, err := client.CreateHotelOrder(
		newCtx,
		&backendPb.CreateHotelOrderReq{
			Order: converts.FromDomainHotelOrder(order),
		},
	)
	if err != nil {
		return "", errors.Wrap(err, "CreateHotelOrder failed")
	}

	return res.Id, nil
}

func (s *orderServiceClient) PlaceHotelOrder(ctx context.Context, order *domain.HubHotelOrder, shopInfo *domain.PartnerShopInfo) (*domain.TransactionInfo, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.OrderServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "Cannot connect order service")
	}
	defer conn.Close()

	if shopInfo == nil {
		return nil, errors.Wrap(commonError.ErrInvalidInput, "shopInfo nil")
	}

	partnerhipID := order.PartnershipID
	if partnerhipID == "" {
		partnerhipID = s.cfg.HubPartnershipID
	}

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, partnerhipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	var priceBreakdown *backendPb.PriceBreakdown
	if order.ExchangedRateDataCf != nil && order.ExchangedRateDataCfRaw != nil {
		priceBreakdown = &backendPb.PriceBreakdown{
			HiddenFee: order.ExchangedRateDataCf.HiddenFeeAmount,
			Discount:  order.ExchangedRateDataCf.DiscountAmount,
		}
	}

	if order.ExchangedRateDataCfRaw != nil {
		priceBreakdown.BasePrice = order.ExchangedRateDataCfRaw.PayNow
	}

	client := backendPb.NewOrderServiceClient(conn)

	res, err := client.PlaceOrder(
		newCtx,
		&backendPb.PlaceOrderReq{
			OrderId:      order.OrderPaymentID,
			CustomerId:   shopInfo.OwnerID,
			CustomerName: shopInfo.Code,
			AdditionData: &backendPb.AdditionData{
				HubMetadata: &backendPb.HubMetadata{
					AgentCode: shopInfo.Code,
					AgentName: shopInfo.Name,
					Provider:  commonEnum.HotelProviderName[order.Provider],
				},
				ReferenceCode:  order.OrderCode,
				PriceBreakdown: priceBreakdown,
				Source:         int64(commonEnum.TransactionSourceHubHotel),
				SourceRef:      order.ID,
				PartnerShopId:  shopInfo.ID,
			},
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "PlaceHotelOrder failed")
	}

	if !res.IsSuccess {
		if res.ErrorCode == "INSUFFICIENT_FUNDS" {
			return nil, domain.ErrInsufficientBalance
		}

		return nil, errors.New(res.ErrorCode)
	}

	return converts.ToDomainTransactionInfo(res), nil
}
