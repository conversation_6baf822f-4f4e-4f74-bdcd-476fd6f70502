package repositories

import (
	"context"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"go.mongodb.org/mongo-driver/bson"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const amenityCollectionName = "amenities"
const amenityIndex = "__amenity_id__language__"
const amenityIndexLanguage = "__language__"

type AmenityRepository interface {
	FindByAmenityIDs(ctx context.Context, amenityIDs []string, language string) ([]*domain.AmenityContent, error)
	FindByLanguage(ctx context.Context, language string) ([]*domain.AmenityContent, error)
}

type amenityRepository struct {
	db mongodb.DB
}

func NewAmenityRepository(db mongodb.DB) AmenityRepository {
	return &amenityRepository{
		db: db,
	}
}

func (r *amenityRepository) FindByAmenityIDs(ctx context.Context, amenityIDs []string, language string) ([]*domain.AmenityContent, error) {
	var sf []*models.AmenityContent
	if err := r.db.Find(ctx, amenityCollectionName, &sf,
		mongodb.WithHint(amenityIndex),
		mongodb.WithFilter(bson.M{
			"amenity_id": bson.M{
				"$in": amenityIDs,
			},
			"language": language,
		})); err != nil {
		return nil, err
	}

	return converts.ToDomainContentAmenities(sf), nil
}

func (r *amenityRepository) FindByLanguage(ctx context.Context, language string) ([]*domain.AmenityContent, error) {
	var sf []*models.AmenityContent
	if err := r.db.Find(ctx, amenityCollectionName, &sf,
		mongodb.WithHint(amenityIndexLanguage),
		mongodb.WithFilter(bson.M{
			"language": language,
		})); err != nil {
		return nil, err
	}

	return converts.ToDomainContentAmenities(sf), nil
}
