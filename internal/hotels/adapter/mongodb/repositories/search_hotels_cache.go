package repositories

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	searchHotelCachesColName       = "search_hotels"
	searchHotelCachesParentColName = "search_hotels_parent"
)

const (
	searchHotelIndexParentID = "__parent_id_index__"
)

const (
	searchHotelParentIndexSearchKey = "__search_key__"
)

type SearchHotelCachesRepository interface {
	Insert(ctx context.Context, key string, data map[commonEnum.HotelProvider][]*domain.HotelSummary) error
	FindByParentID(ctx context.Context, parentID string) ([]*domain.HotelSummary, error)
	FindParentIDByKey(ctx context.Context, key string, providers []commonEnum.HotelProvider) (map[commonEnum.HotelProvider]string, error)
	RemoveSoldOutCache(ctx context.Context, hotelID string, provider commonEnum.HotelProvider) error
	ClearExpiredHotelRecords(ctx context.Context) error
}

type searchHotelCachesRepository struct {
	db mongodb.DB
}

func NewSearchHotelCachesRepository(db mongodb.DB) SearchHotelCachesRepository {
	return &searchHotelCachesRepository{db}
}

func (r *searchHotelCachesRepository) Insert(ctx context.Context, key string, data map[commonEnum.HotelProvider][]*domain.HotelSummary) error {
	for provider, req := range data {
		if _, err := r.db.WithTransaction(ctx, func(transCtx context.Context) (interface{}, error) {
			createParent := &models.HubSearchHotelCache{
				SearchKey: key,
				IsParent:  true,
				Provider:  provider,
				ExpiredAt: time.Now().Add(constants.SearchHotelCacheTime).UnixMilli(),
			}

			createParent.BeforeCreate()
			err := r.db.Insert(ctx, searchHotelCachesParentColName, &createParent)
			if err != nil {
				return nil, errors.Wrap(err, "db.Insert")
			}

			request := make([]mongo.WriteModel, 0, len(req))

			m := converts.ConvertDomainToModelsHotelSummaries(req)
			for index, data := range m {
				createInput := &models.HubSearchHotelCache{
					Index:        index + 1,
					ParentID:     createParent.ID,
					HotelSummary: data,
				}
				createInput.BeforeCreate()

				request = append(request, mongo.NewInsertOneModel().SetDocument(createInput))
			}

			if len(request) == 0 {
				return nil, nil
			}

			err = r.db.BulkWriteRaw(ctx, searchHotelCachesColName, request)
			if err != nil {
				return nil, errors.Wrap(err, "db.BulkWriteRaw")
			}

			return nil, nil
		}); err != nil {
			log.Error("Create cache for provider failed", log.Any("err", err), log.Any("provider", provider))
		}
	}

	return nil
}

func (r *searchHotelCachesRepository) FindParentIDByKey(ctx context.Context, key string, providers []commonEnum.HotelProvider) (map[commonEnum.HotelProvider]string, error) {
	m := []*models.HubSearchHotelCache{}

	filter := bson.M{
		"search_key": key,
		"provider": bson.M{
			"$in": providers,
		},
		"expired_at": bson.M{
			"$gt": time.Now().UnixMilli(),
		},
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchHotelParentIndexSearchKey),
	}

	err := r.db.Find(ctx, searchHotelCachesParentColName, &m, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "r.db.FindOne")
	}

	result := map[commonEnum.HotelProvider]string{}

	for _, item := range m {
		result[item.Provider] = item.ID.Hex()
	}

	return result, nil
}

func (r *searchHotelCachesRepository) FindByParentID(ctx context.Context, parentID string) ([]*domain.HotelSummary, error) {
	m := []*models.HubSearchHotelCache{}

	objectID, err := primitive.ObjectIDFromHex(parentID)
	if err != nil {
		log.Error("primitive.ObjectIDFromHex err", log.Any("err", err), log.Any("parentID", parentID))
		return nil, err
	}

	filter := bson.M{
		"parent_id": objectID,
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchHotelIndexParentID),
		mongodb.WithSorter(bson.M{"index": 1}),
	}

	err = r.db.Find(ctx, searchHotelCachesColName, &m, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "r.db.FindOne")
	}

	result := []*domain.HotelSummary{}
	for _, item := range m {
		result = append(result, converts.ConvertModelsHotelSummaryToDomainHotelSummary(item.HotelSummary))
	}

	return result, nil
}

func (r *searchHotelCachesRepository) RemoveSoldOutCache(ctx context.Context, hotelID string, provider commonEnum.HotelProvider) error {
	m := []*models.HubSearchHotelCache{}

	filter := bson.M{
		"hotel_summary.id":       hotelID,
		"hotel_summary.provider": provider,
	}

	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(searchHotelIndexParentID),
	}

	err := r.db.Find(ctx, searchHotelCachesColName, &m, opts...)
	if err != nil {
		return errors.Wrap(err, "r.db.FindOne")
	}

	parentIDs := []primitive.ObjectID{}
	for _, item := range m {
		parentIDs = append(parentIDs, item.ParentID)
	}

	if len(parentIDs) > 0 {
		err = r.db.UpdateMany(ctx, searchHotelCachesParentColName, bson.M{
			"_id": bson.M{
				"$in": parentIDs,
			},
		}, bson.M{
			"expired_at": time.Now().UnixMilli(),
		}, &options.UpdateOptions{
			Hint: models.IndexDefault,
		})

		if err != nil {
			return errors.Wrap(err, "r.db.Delete")
		}
	}

	return nil
}

func (r *searchHotelCachesRepository) ClearExpiredHotelRecords(ctx context.Context) error {
	currentTime := time.Now().UnixMilli()

	expiredFilter := bson.M{"expired_at": bson.M{"$lt": currentTime}}

	var expiredParents []*models.HubSearchHotelCache
	err := r.db.Find(ctx, searchHotelCachesParentColName, &expiredParents, mongodb.WithFilter(expiredFilter), mongodb.WithHint(models.DefaultIndex))
	if err != nil {
		return errors.Wrap(err, "failed to find expired parent records")
	}

	parentIDs := make([]primitive.ObjectID, 0, len(expiredParents))
	for _, parent := range expiredParents {
		parentIDs = append(parentIDs, parent.ID)
	}

	childFilter := bson.M{"parent_id": bson.M{"$in": parentIDs}}

	err = r.db.Delete(ctx, searchHotelCachesColName, childFilter, &options.DeleteOptions{
		Hint: searchHotelIndexParentID,
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return errors.Wrap(err, "failed to delete expired child records")
	}

	err = r.db.Delete(ctx, searchHotelCachesParentColName, expiredFilter, &options.DeleteOptions{
		Hint: models.DefaultIndex,
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return errors.Wrap(err, "failed to delete expired parent records")
	}

	log.Info("Clear hotel cache done!", log.Any("HotelParent", len(expiredParents)))

	return nil
}
