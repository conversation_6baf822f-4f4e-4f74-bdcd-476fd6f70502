package repositories

import (
	"context"
	"regexp"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

const orderCollectionName = "hub_hotel_orders"
const (
	orderIndexID              = "_id_"
	orderIndexSessionID       = "__session_id__"
	orderIndexReservationCode = "__reservation_code__"
	orderIndexOrderCode       = "__order_code__"
	orderIndexBookingStatus   = "__booking_status__"
)

type OrderRepository interface {
	InsertOne(ctx context.Context, req *domain.HubHotelOrder) error
	FindOrderBySessionID(ctx context.Context, officeID string, sessionID string) (*domain.HubHotelOrder, error)
	FindOrderBySessionIDWithPartnership(ctx context.Context, officeID, sessionID, partnershipID string) (*domain.HubHotelOrder, error)
	FindOrderByID(ctx context.Context, id string) (*domain.HubHotelOrder, error)
	FindOneByOrderCode(ctx context.Context, officeID string, orderCode string) (*domain.HubHotelOrder, error)
	FindOneByOrderCodeWithPartnership(ctx context.Context, officeID, orderCode, partnershipID string) (*domain.HubHotelOrder, error)
	FindOneByReservationCode(ctx context.Context, reservationCode string, provider commonEnum.HotelProvider) (*domain.HubHotelOrder, error)
	UpdateOne(ctx context.Context, id string, req *domain.HubHotelOrder) error
	UpdateOneV2(ctx context.Context, id string, req *domain.HubOrderUpdate) error
	ListExpiredOrder(ctx context.Context) ([]*domain.HubHotelOrder, error)
	ListOrder(ctx context.Context, pagination *commonDomain.Pagination, req *domain.ListBookingRequest) ([]*domain.HubHotelOrder, error)
	ListOrderByFilter(ctx context.Context, pagination *commonDomain.Pagination, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error)
	WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error
	ListCancelingOrder(ctx context.Context) ([]*domain.HubHotelOrder, error)
	ListOrderAggregate(ctx context.Context, pagination *commonDomain.Pagination, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error)
	ListBookingWithoutHotelConfirmationID(ctx context.Context) (*mongo.Cursor, error)
	UpdateConfirmationIDs(ctx context.Context, orderID string, currentStatus enum.BookingStatus, rooms []*domain.HubOrderRoomItem) error
}

type orderRepository struct {
	db mongodb.DB
}

func NewOrderRepository(db mongodb.DB) OrderRepository {
	return &orderRepository{db}
}

func (r *orderRepository) WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error {
	_, err := r.db.WithTransaction(ctx, fn)
	if err != nil {
		return err
	}

	return nil
}

func (r *orderRepository) ListOrder(ctx context.Context, pagination *commonDomain.Pagination, req *domain.ListBookingRequest) ([]*domain.HubHotelOrder, error) {
	result := []*models.HubHotelOrder{}

	bookingStatus := req.BookingStatus
	sortKey := req.OrderKey
	sortVal := req.OrderVal

	filter := bson.M{
		"booking_status": bookingStatus,
		"pending_deadline": bson.D{
			{Key: "$gt", Value: time.Now().UTC().UnixMilli()},
		},
		"unknown_pending": bson.M{"$ne": true},
	}

	if req.OrderStatus != enum.HubOrderStatusNone {
		filter["status"] = req.OrderStatus
	}

	if req.PendingStartAtLt != 0 {
		filter["pending_start_at"] = bson.D{
			{Key: "$lt", Value: req.PendingStartAtLt},
		}
	}

	opts := []mongodb.Option{
		mongodb.WithPaging(pagination),
		mongodb.WithHint(orderIndexBookingStatus),
		mongodb.WithFilter(filter),
	}

	if sortKey != "" {
		order := bson.M{sortKey: sortVal}

		opts = append(opts, mongodb.WithSorter(order))
	}

	err := r.db.Find(ctx, orderCollectionName, &result, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	cResult := converts.ToDomainHubHotelOrders(result)

	return cResult, nil
}

func (r *orderRepository) ListOrderByFilter(ctx context.Context, pagination *commonDomain.Pagination, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error) {
	result := []*models.HubHotelOrder{}
	filter := bson.M{}
	filter["office_id"] = bson.M{"$in": req.ManageOfficeIDs}

	if req.PartnershipID != "" {
		filter["partnership_id"] = req.PartnershipID
	}

	if len(req.BookingStatuses) > 0 {
		filter["booking_status"] = bson.M{"$in": req.BookingStatuses}
	} else if len(req.NotInStatuses) > 0 {
		filter["booking_status"] = bson.M{"$nin": req.NotInStatuses}
	}

	if req.UnknownPending != nil {
		filter["unknown_pending"] = *req.UnknownPending
	}

	if req.OrderCode != "" {
		filter["$or"] = []bson.M{
			{"order_code": bson.M{"$regex": regexp.QuoteMeta(req.OrderCode)}},
			{"hotel.rooms.confirmation_id": bson.M{"$regex": regexp.QuoteMeta(req.OrderCode)}},
			{"client_order_code": bson.M{"$regex": regexp.QuoteMeta(req.OrderCode)}},
		}
	}

	if req.OfficeID != nil {
		filter["office_id"] = *req.OfficeID
	}

	if req.From != 0 && req.To != 0 {
		filter["created_at"] = bson.M{"$gte": req.From, "$lte": req.To}
	}

	opts := []mongodb.Option{
		mongodb.WithHint(orderIndexBookingStatus),
		mongodb.WithFilter(filter),
	}

	if pagination != nil && pagination.PageLimit > 0 {
		opts = append(opts, mongodb.WithPaging(pagination))
	}

	if req.Pagination != nil && req.Pagination.PageLimit > 0 {
		opts = append(opts, mongodb.WithPaging(req.Pagination))
	}

	if err := r.db.Find(ctx, orderCollectionName, &result, opts...); err != nil {
		return nil, err
	}

	cResult := converts.ToDomainHubHotelOrders(result)

	return cResult, nil
}

func (r *orderRepository) ListExpiredOrder(ctx context.Context) ([]*domain.HubHotelOrder, error) {
	result := []*models.HubHotelOrder{}

	filter := bson.D{
		{Key: "last_confirm_date", Value: bson.D{{Key: "$lt", Value: time.Now().UnixMilli()}}},
	}

	opts := []mongodb.Option{
		mongodb.WithPaging(&commonDomain.Pagination{
			PageLimit:   30,
			PageCurrent: 1,
		}),
		mongodb.WithHint(orderIndexBookingStatus),
		mongodb.WithFilter(filter),
	}

	err := r.db.Find(ctx, orderCollectionName, &result, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	cResult := converts.ToDomainHubHotelOrders(result)

	return cResult, nil
}

func (r *orderRepository) ListCancelingOrder(ctx context.Context) ([]*domain.HubHotelOrder, error) {
	result := []*models.HubHotelOrder{}

	filter := bson.D{
		{Key: "canceling_start_at", Value: bson.D{{Key: "$lt", Value: time.Now().UnixMilli()}}},
		{Key: "booking_status", Value: enum.BookingStatusCanceling},
		{Key: "skip_scan_check_cancel", Value: false},
	}

	opts := []mongodb.Option{
		mongodb.WithPaging(&commonDomain.Pagination{
			PageLimit:   30,
			PageCurrent: 1,
		}),
		mongodb.WithHint(orderIndexBookingStatus),
		mongodb.WithFilter(filter),
	}

	err := r.db.Find(ctx, orderCollectionName, &result, opts...)
	if err != nil {
		return nil, errors.Wrap(err, "db.Find")
	}

	cResult := converts.ToDomainHubHotelOrders(result)

	return cResult, nil
}

func (r *orderRepository) UpdateOne(ctx context.Context, id string, req *domain.HubHotelOrder) error {
	m := converts.FromDomainHubHotelOrder(req)

	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.Wrap(err, "ObjectIDFromHex id")
	}

	filter := bson.M{
		"_id": objID,
	}

	return r.db.UpdateOne(ctx, orderCollectionName, filter, m, &options.UpdateOptions{
		Hint: orderIndexID,
	})
}

func (r *orderRepository) UpdateOneV2(ctx context.Context, id string, req *domain.HubOrderUpdate) error {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.Wrap(err, "ObjectIDFromHex id")
	}

	filter := bson.M{
		"_id": objID,
	}

	update := &bson.M{}

	if req != nil {
		if req.AgentCode != "" {
			(*update)["agent_code"] = req.AgentCode
		}
	}

	if req.LastTransactionID != nil {
		(*update)["last_transaction_id"] = req.LastTransactionID
	}

	if req.LastTransactionIDOld != nil {
		(*update)["last_transaction_id_old"] = req.LastTransactionIDOld
	}

	if req.NewOfficeID != nil {
		(*update)["office_id"] = req.NewOfficeID
	}

	if req.OfficeIDOld != nil {
		(*update)["office_id_old"] = req.OfficeIDOld
	}

	return r.db.UpdateOne(ctx, orderCollectionName, filter, update, &options.UpdateOptions{
		Hint: orderIndexID,
	})
}

func (r *orderRepository) InsertOne(ctx context.Context, req *domain.HubHotelOrder) error {
	m := converts.FromDomainHubHotelOrder(req)

	m.BeforeCreate()

	return r.db.InsertOne(ctx, orderCollectionName, m, &options.InsertOneOptions{})
}

func (r *orderRepository) FindOrderBySessionID(ctx context.Context, officeID, sessionID string) (*domain.HubHotelOrder, error) {
	var res *models.HubHotelOrder

	filter := bson.M{
		"session_id": sessionID,
		"office_id":  officeID,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(orderIndexSessionID),
	}

	err := r.db.FindOne(ctx,
		orderCollectionName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response := converts.ToDomainHubHotelOrder(res)

	return response, nil
}

func (r *orderRepository) FindOrderByID(ctx context.Context, id string) (*domain.HubHotelOrder, error) {
	var res *models.HubHotelOrder

	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.Wrap(err, "ObjectIDFromHex id")
	}
	filter := bson.M{
		"_id": objID,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(orderIndexID),
	}

	err = r.db.FindOne(ctx,
		orderCollectionName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response := converts.ToDomainHubHotelOrder(res)

	return response, nil
}

// FindOrderBySessionIDWithPartnership tìm order theo sessionID và partnershipID để đảm bảo partnership isolation.
func (r *orderRepository) FindOrderBySessionIDWithPartnership(ctx context.Context, officeID, sessionID, partnershipID string) (*domain.HubHotelOrder, error) {
	var res *models.HubHotelOrder

	filter := bson.M{
		"session_id": sessionID,
		"office_id":  officeID,
	}

	// Thêm filter theo PartnershipID nếu có
	if partnershipID != "" {
		filter["partnership_id"] = partnershipID
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(orderIndexSessionID),
	}

	err := r.db.FindOne(ctx,
		orderCollectionName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response := converts.ToDomainHubHotelOrder(res)

	return response, nil
}

func (r *orderRepository) FindOneByOrderCode(ctx context.Context, officeID string, orderCode string) (*domain.HubHotelOrder, error) {
	var res *models.HubHotelOrder

	filter := bson.M{
		"order_code": orderCode,
	}

	if officeID != "" {
		filter["office_id"] = officeID
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(orderIndexOrderCode),
	}

	err := r.db.FindOne(ctx,
		orderCollectionName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response := converts.ToDomainHubHotelOrder(res)

	return response, nil
}

// FindOneByOrderCodeWithPartnership tìm order theo orderCode và partnershipID để đảm bảo partnership isolation.
func (r *orderRepository) FindOneByOrderCodeWithPartnership(ctx context.Context, officeID, orderCode, partnershipID string) (*domain.HubHotelOrder, error) {
	var res *models.HubHotelOrder

	filter := bson.M{
		"order_code": orderCode,
	}

	if officeID != "" {
		filter["office_id"] = officeID
	}

	// Thêm filter theo PartnershipID nếu có
	if partnershipID != "" {
		filter["partnership_id"] = partnershipID
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(orderIndexOrderCode),
	}

	err := r.db.FindOne(ctx,
		orderCollectionName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response := converts.ToDomainHubHotelOrder(res)

	return response, nil
}

func (r *orderRepository) FindOneByReservationCode(ctx context.Context, reservationCode string, provider commonEnum.HotelProvider) (*domain.HubHotelOrder, error) {
	if reservationCode == "" {
		return nil, nil
	}

	var res *models.HubHotelOrder

	filter := bson.M{
		"reservation_code": reservationCode,
		"provider":         provider,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(orderIndexReservationCode),
	}

	err := r.db.FindOne(ctx,
		orderCollectionName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	response := converts.ToDomainHubHotelOrder(res)

	return response, nil
}

func (r *orderRepository) ListOrderAggregate(ctx context.Context, pagination *commonDomain.Pagination, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, error) {
	result := []*models.HubHotelOrder{}
	filter := bson.M{
		"is_migrated":             true,
		"last_transaction_id_old": bson.M{"$exists": false},
	}

	if len(req.BookingStatuses) > 0 {
		filter["booking_status"] = bson.M{"$in": req.BookingStatuses}
	} else if len(req.NotInStatuses) > 0 {
		filter["booking_status"] = bson.M{"$nin": req.NotInStatuses}
	}

	if req.OrderCode != "" {
		filter["$or"] = []bson.M{
			{"order_code": bson.M{"$regex": regexp.QuoteMeta(req.OrderCode)}},
			// {"hotel.rooms.confirmation_id": bson.M{"$regex": regexp.QuoteMeta(req.OrderCode)}},
		}
	}

	if req.OfficeID != nil {
		filter["office_id"] = *req.OfficeID
	}

	if req.From != 0 && req.To != 0 {
		filter["created_at"] = bson.M{"$gte": req.From, "$lte": req.To}
	}

	opts := []mongodb.Option{
		mongodb.WithHint(orderIndexBookingStatus),
		mongodb.WithFilter(filter),
	}

	if pagination != nil && pagination.PageLimit > 0 {
		opts = append(opts, mongodb.WithPaging(pagination))
	}

	if req.Pagination != nil && req.Pagination.PageLimit > 0 {
		opts = append(opts, mongodb.WithPaging(req.Pagination))
	}

	if err := r.db.Find(ctx, orderCollectionName, &result, opts...); err != nil {
		return nil, err
	}

	cResult := converts.ToDomainHubHotelOrders(result)

	return cResult, nil
}

func (r *orderRepository) ListBookingWithoutHotelConfirmationID(ctx context.Context) (*mongo.Cursor, error) {
	filter := bson.M{
		"booking_status":                      enum.BookingStatusSuccess,
		"hotel.rooms.hotel_confirmation_id":   bson.M{"$exists": false},
		"pending_hotel_confirmation_deadline": bson.M{"$ne": 0, "$gte": time.Now().UnixMilli()},
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(orderIndexBookingStatus),
		mongodb.WithSorter(bson.M{"created_at": 1}),
	}

	cursor, err := r.db.FindWithCursor(ctx, orderCollectionName, options...)
	if err != nil {
		log.Error("FindWithCursor err", log.Any("err", err))
		return nil, err
	}

	return cursor, nil
}

func (r *orderRepository) UpdateConfirmationIDs(ctx context.Context, orderID string, currentStatus enum.BookingStatus, rooms []*domain.HubOrderRoomItem) error {
	objID, err := primitive.ObjectIDFromHex(orderID)
	if err != nil {
		return errors.Wrap(err, "ObjectIDFromHex id")
	}

	filter := bson.M{
		"_id": objID,
	}

	update := bson.M{
		"hotel.rooms": converts.FromDomainHubOrderRoomItems(rooms),
		"updated_at":  time.Now().UTC().UnixMilli(),
	}

	return r.db.UpdateOne(ctx, orderCollectionName, filter, update, &options.UpdateOptions{
		Hint: orderIndexID,
	})
}
