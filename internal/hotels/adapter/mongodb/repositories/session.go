package repositories

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const sessionColName = "sessions"
const (
	sessionIndexOfficeIDSessionID = "__office_id__session_id__"
)

type SessionRepository interface {
	WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error
	InsertOne(ctx context.Context, req *domain.HotelSession) error
	FindBySessionID(ctx context.Context, officeID, sessionID string) (*domain.HotelSession, error)
	UpdateExpireTime(ctx context.Context, sessionID string, newExpire int64) error
	FindBySessionIDs(ctx context.Context, sessionIDs []string) ([]*domain.HotelSession, error)
	FindBySessionIDWithoutExpire(ctx context.Context, officeID, sessionID string) (*domain.HotelSession, error)
}

type sessionRepository struct {
	db mongodb.DB
}

func NewSessionRepository(db mongodb.DB) SessionRepository {
	return &sessionRepository{db}
}

func (r *sessionRepository) WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error {
	_, err := r.db.WithTransaction(ctx, fn)
	if err != nil {
		return err
	}

	return nil
}

func (r *sessionRepository) UpdateExpireTime(ctx context.Context, sessionID string, newExpire int64) error {
	filter := bson.M{
		"session_id": sessionID,
	}

	update := &bson.M{
		"expired_at": newExpire,
		"updated_at": time.Now().UnixMilli(),
	}

	return r.db.UpdateOne(ctx, sessionColName, filter, update, &options.UpdateOptions{
		Hint: sessionIndexOfficeIDSessionID,
	})
}

func (r *sessionRepository) InsertOne(ctx context.Context, req *domain.HotelSession) error {
	m := converts.FromDomainHotelSession(req)

	m.BeforeCreate()

	return r.db.InsertOne(ctx, sessionColName, &m, &options.InsertOneOptions{})
}

func (r *sessionRepository) FindBySessionIDs(ctx context.Context, sessionIDs []string) ([]*domain.HotelSession, error) {
	var res []*models.HotelSession

	filter := bson.M{
		"session_id": bson.M{
			"$in": sessionIDs,
		},
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(sessionIndexOfficeIDSessionID),
	}

	err := r.db.FindWithRetry(ctx,
		constants.MongoRetryCount,
		constants.MongoRetryDelay,
		sessionColName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	return converts.ToDomainHotelSessions(res), nil
}

func (r *sessionRepository) FindBySessionID(ctx context.Context, officeID, sessionID string) (*domain.HotelSession, error) {
	var res *models.HotelSession
	filter := bson.M{
		"session_id": sessionID,
		"office_id":  officeID,
		"expired_at": bson.M{
			"$gt": time.Now().UnixMilli(),
		},
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(sessionIndexOfficeIDSessionID),
	}

	err := r.db.FindOneWithRetry(ctx,
		constants.MongoRetryCount,
		constants.MongoRetryDelay,
		sessionColName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	return converts.ToDomainHotelSession(res), nil
}

func (r *sessionRepository) FindBySessionIDWithoutExpire(ctx context.Context, officeID, sessionID string) (*domain.HotelSession, error) {
	var res *models.HotelSession
	filter := bson.M{
		"session_id": sessionID,
		"office_id":  officeID,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(sessionIndexOfficeIDSessionID),
	}

	err := r.db.FindOneWithRetry(ctx,
		constants.MongoRetryCount,
		constants.MongoRetryDelay,
		sessionColName,
		&res,
		options...,
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, errors.Wrap(err, "FindOne")
	}

	return converts.ToDomainHotelSession(res), nil
}
