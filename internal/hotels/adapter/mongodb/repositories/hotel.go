package repositories

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"sync"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const hotelCollectionName = "hotels_v2"

type HotelRepository interface {
	Create(ctx context.Context, req *domain.Hotel) error
	Update(ctx context.Context, input *domain.Hotel) error
	WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error
	CreateMany(ctx context.Context, fareRulesReqs []*domain.Hotel) error
	FindByHotelID(ctx context.Context, hotelID, language string) (*domain.Hotel, error)
	FindByHotelIDV2(ctx context.Context, hotelID string) ([]*domain.Hotel, error)
	FindByHotelIDs(ctx context.Context, hotelIDs []string, language string) ([]*domain.Hotel, error)
	FindByHotelByExpediaID(ctx context.Context, expediaID, language string) (*domain.Hotel, error)
	FindByHotelIDsWithDefault(ctx context.Context, hotelIDs []string, language string, minimize, fullImage bool) (map[string][]*domain.Hotel, error)
	FindWithFilter(ctx context.Context, filterReq *domain.FindHotelsWithFilterReq) ([]*domain.Hotel, error)
	UpdateProviderIds(ctx context.Context, hotelID string, provider commonEnum.HotelProvider, value string) error
	FindAllByCountryCode(ctx context.Context, countryCode string) ([]*domain.Hotel, error)
	FindHotelsByRadius(ctx context.Context, longitude, latitude, distance float64, language, exceptionID string, limit int) ([]*domain.Hotel, error)
	FindByProviderHotelID(ctx context.Context, provider commonEnum.HotelProvider, hotelIDs []string, language string) ([]*domain.Hotel, error)
	FindByProviderAndContentVersionHotelID(ctx context.Context, provider commonEnum.HotelProvider, contentVersion, hotelID, language string) (*domain.Hotel, error)
	UpdateHotelRating(ctx context.Context, hotelID string, provider commonEnum.HotelProvider, ratings *domain.GuestRating) error
	FindByProviderHotelIDForCache(ctx context.Context, provider commonEnum.HotelProvider, hotelIDs []string, language string, limit int) ([]*domain.Hotel, error)
	FindByProviderIds(ctx context.Context, provider commonEnum.HotelProvider, providerId string) ([]*domain.Hotel, error)
	FindHotelIDForCache(ctx context.Context, hotelIDs []string, language string, limit int) ([]*domain.Hotel, error)
}

type hotelRepositoryIndexes struct {
	hotelKeyStage1         string
	hotelIDLanguageVersion string
	hotelKeyStage2LatLong  string
	indexDefault           string
}

type hotelRepository struct {
	db      mongodb.DB
	cfg     *config.Schema
	indexes *hotelRepositoryIndexes
}

func newHotelRepositoryIndexes() *hotelRepositoryIndexes {
	return &hotelRepositoryIndexes{
		hotelKeyStage1:         "__hotel_key_stage_1__",
		hotelIDLanguageVersion: "__hotel_id__language__version__",
		hotelKeyStage2LatLong:  "__hotel_key_stage_2_lat_long__",
		indexDefault:           "_id_",
	}
}

func NewHotelRepository(db mongodb.DB, cfg *config.Schema,
) HotelRepository {
	return &hotelRepository{
		db:      db,
		cfg:     cfg,
		indexes: newHotelRepositoryIndexes(),
	}
}

func (r *hotelRepository) getCollectionName() string {
	if r.cfg.ContentVersion != "1" {
		return "hotels_v2"
	}

	switch commonEnum.HotelProviderValue[r.cfg.EnableProvider] {
	case commonEnum.HotelProviderExpedia:
		return "expedia_hotels"
	}

	return "hotels"
}

func (r *hotelRepository) UpdateProviderIds(ctx context.Context, hotelID string, provider commonEnum.HotelProvider, value string) error {
	filter := bson.M{
		"hotel_id": hotelID,
	}

	update := bson.M{
		"provider_ids." + strconv.Itoa(int(provider)): value,
	}

	return r.db.UpdateMany(ctx, r.getCollectionName(), filter, update, &options.UpdateOptions{
		Hint: r.indexes.hotelIDLanguageVersion,
	})
}

func (r *hotelRepository) UpdateHotelRating(ctx context.Context, hotelID string, provider commonEnum.HotelProvider, ratings *domain.GuestRating) error {
	filter := bson.M{
		"hotel_id": hotelID,
	}

	mRatings := converts.FromDomainGuestRating(ratings)
	if mRatings == nil {
		return nil
	}

	update := bson.M{
		"ratings.guest": mRatings,
	}

	return r.db.UpdateMany(ctx, r.getCollectionName(), filter, update, &options.UpdateOptions{
		Hint: r.indexes.hotelIDLanguageVersion,
	})
}

func (r *hotelRepository) FindWithFilter(ctx context.Context, filterReq *domain.FindHotelsWithFilterReq) ([]*domain.Hotel, error) {
	var sf []*models.Hotel

	filter := bson.M{}
	hint := r.indexes.indexDefault

	if filterReq.HotelKeyStage1 != "" {
		filter["hotel_key_stage_1"] = filterReq.HotelKeyStage1
		hint = r.indexes.hotelKeyStage1
	}

	if filterReq.HotelKeyStage2 != "" {
		filter["hotel_key_stage_2"] = filterReq.HotelKeyStage2

		latDown := filterReq.Lat - filterReq.Radius
		latUp := filterReq.Lat + filterReq.Radius

		longDown := filterReq.Long - filterReq.Radius
		longUp := filterReq.Long + filterReq.Radius

		filter["location.coordinates.latitude"] = bson.M{"$gte": latDown, "$lte": latUp}
		filter["location.coordinates.longitude"] = bson.M{"$gte": longDown, "$lte": longUp}

		hint = r.indexes.hotelKeyStage2LatLong
	}

	otps := []mongodb.Option{
		mongodb.WithHint(hint),
		mongodb.WithFilter(filter),
	}

	if err := r.db.Find(ctx, r.getCollectionName(), &sf, otps...); err != nil {
		return nil, err
	}

	return converts.ToDomainHotels(sf), nil
}

func (r *hotelRepository) Create(ctx context.Context, req *domain.Hotel) error {
	data := converts.FromDomainHotel(req)
	data.BeforeCreate()

	if err := r.db.Insert(ctx, r.getCollectionName(), data); err != nil {
		log.Error("hotelRepository Create err", log.Any("err", err), log.Any("data", data.Name))
		return err
	}

	return nil
}

func (r *hotelRepository) Update(ctx context.Context, input *domain.Hotel) error {
	data := converts.FromDomainHotel(input)
	data.BeforeUpdate()

	objID, err := primitive.ObjectIDFromHex(input.ID)
	if err != nil {
		return errors.Wrap(err, "id ObjectIDFromHex")
	}

	filter := bson.M{
		"_id": objID,
	}

	if err := r.db.UpdateOne(ctx, r.getCollectionName(), filter, data, &options.UpdateOptions{
		Hint: models.DefaultIndex,
	}); err != nil {
		log.Error("hotelRepository Update err", log.Any("err", err), log.Any("data", data.Name))
		return err
	}

	return nil
}

func (r *hotelRepository) CreateMany(ctx context.Context, fareRulesReqs []*domain.Hotel) error {
	request := make([]mongo.WriteModel, len(fareRulesReqs))

	for i, req := range fareRulesReqs {
		m := converts.FromDomainHotel(req)
		m.BeforeCreate()
		filter := bson.M{}
		update := bson.M{
			"$set": m,
		}
		request[i] = mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpsert(true).
			SetHint(models.HotelCollectionHotelIDLanguageVersionIndex).
			SetUpdate(update)
	}

	err := r.db.BulkWriteRaw(ctx, r.getCollectionName(), request)
	if err != nil {
		return err
	}

	return nil
}

func (r *hotelRepository) WithTransaction(ctx context.Context, fn func(tnxSession context.Context) (interface{}, error)) error {
	_, err := r.db.WithTransaction(ctx, fn)
	if err != nil {
		return err
	}

	return nil
}

func (r *hotelRepository) FindByHotelIDV2(ctx context.Context, hotelID string) ([]*domain.Hotel, error) {
	var sf []*models.Hotel

	filter := bson.M{
		"hotel_id": hotelID,
	}

	colName := r.getCollectionName()

	if err := r.db.Find(ctx, colName, &sf,
		mongodb.WithHint(models.HotelCollectionHotelIDLanguageVersionIndex),
		mongodb.WithFilter(filter)); err != nil {
		return nil, err
	}

	return converts.ToDomainHotels(sf), nil
}

func (r *hotelRepository) FindByHotelID(ctx context.Context, hotelID, language string) (*domain.Hotel, error) {
	var sf *models.Hotel

	filter := bson.M{
		"hotel_id": hotelID,
		"language": language,
	}

	if err := r.db.FindOne(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.HotelCollectionHotelIDLanguageVersionIndex),
		mongodb.WithFilter(filter)); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainHotel(sf), nil
}

// Response request_language and vi-VN, en-US
func (r *hotelRepository) FindByHotelIDsWithDefault(ctx context.Context, hotelIDs []string, language string, minimize bool, fullImage bool) (map[string][]*domain.Hotel, error) {
	var sf []*models.Hotel
	result := map[string][]*domain.Hotel{}

	filter := bson.M{
		"hotel_id": bson.M{
			"$in": hotelIDs,
		},
		"language": bson.M{
			"$in": []string{language, constants.LanguageEnglish, constants.DefaultLanguage},
		},
	}

	options := []mongodb.Option{
		mongodb.WithHint(models.HotelCollectionHotelIDLanguageVersionIndex),
		mongodb.WithFilter(filter),
	}

	projection := bson.M{}

	if minimize {
		projection = bson.M{
			"language":     1,
			"hotel_id":     1,
			"name":         1,
			"provider_ids": 1,
			"ratings":      1,
			"address":      1,
		}

		if fullImage {
			projection["images"] = 1
		}
	} else if !fullImage {
		projection["images"] = 0
	}

	if len(projection) > 0 {
		options = append(options, mongodb.WithProjection(projection))
	}

	if err := r.db.Find(ctx, r.getCollectionName(), &sf,
		options...); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return result, nil
		}

		return nil, err
	}

	result = converts.ToDomainHotelsWithDefault(sf, constants.DefaultLanguage)

	return result, nil
}

func (r *hotelRepository) FindByHotelIDs(ctx context.Context, hotelIDs []string, language string) ([]*domain.Hotel, error) {
	var sf []*models.Hotel

	filter := bson.M{
		"hotel_id": bson.M{
			"$in": hotelIDs,
		},
		"language": language,
	}

	if err := r.db.Find(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.HotelCollectionHotelIDLanguageVersionIndex),
		mongodb.WithFilter(filter)); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainHotels(sf), nil
}

func (r *hotelRepository) FindByHotelByExpediaID(ctx context.Context, expediaID, language string) (*domain.Hotel, error) {
	var hotel *models.Hotel

	filter := bson.M{
		"provider_ids.11": expediaID,
		"language":        language,
	}

	if err := r.db.FindOne(ctx, r.getCollectionName(), &hotel,
		mongodb.WithHint(models.CollectionProviderExpediaIDIndex),
		mongodb.WithFilter(filter)); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainHotel(hotel), nil
}

func (r *hotelRepository) FindAllByCountryCode(ctx context.Context, countryCode string) ([]*domain.Hotel, error) {
	sf := []*models.Hotel{}
	// start := time.Now().UnixMilli()

	projection := bson.M{
		"hotel_id":                       1,
		"name":                           1,
		"language":                       1,
		"address":                        1,
		"location.coordinates.latitude":  1,
		"location.coordinates.longitude": 1,
		"version":                        1,
		"images":                         1,
	}

	filter := bson.M{
		"address.country_code": countryCode,
	}

	if err := r.db.Find(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(models.HotelCollectionCountryCodeVersionIndex),
		mongodb.WithProjection(projection),
		mongodb.WithFilter(filter),
	); err != nil {
		return nil, err
	}

	// fmt.Println("FindAllByCountryCode exec time", time.Now().UnixMilli()-start)
	return converts.ToDomainHotels(sf), nil
}

// radius Unit Km (kilometer).
func (r *hotelRepository) FindHotelsByRadius(ctx context.Context, longitude, latitude, distance float64, language, exceptionID string, limit int) ([]*domain.Hotel, error) {
	// start := time.Now().UnixMilli()
	query := bson.D{
		{Key: "language", Value: language},
	}

	if exceptionID != "" {
		query = append(query, bson.E{Key: "hotel_id", Value: bson.M{
			"$ne": exceptionID,
		}})
	}

	pipeline := mongo.Pipeline{
		{
			{Key: "$geoNear", Value: bson.D{
				{Key: "near", Value: bson.D{
					{Key: "coordinates", Value: []float64{longitude, latitude}},
				}},
				{Key: "distanceField", Value: "distance"},
				{Key: "maxDistance", Value: distance},
				{Key: "spherical", Value: true},
				{Key: "query", Value: query},
			}},
		},
		{
			{Key: "$project", Value: bson.D{
				{Key: "hotel_id", Value: 1},
				{Key: "name", Value: 1},
				{Key: "address", Value: 1},
				{Key: "language", Value: 1},
				{Key: "distance", Value: 1},
				{Key: "amenities", Value: 1},
				{Key: "images", Value: 1},
				{Key: "category", Value: 1},
				{Key: "ratings", Value: 1},
				{Key: "provider_ids", Value: 1},
				{Key: "location.coordinates.latitude", Value: 1},
				{Key: "location.coordinates.longitude", Value: 1},
			}},
		},
		{
			{Key: "$limit", Value: limit},
		},
	}

	var hotels []*models.Hotel

	cursor, err := r.db.Aggregate(ctx, r.getCollectionName(), pipeline, &options.AggregateOptions{
		Hint: models.HotelCollectionCoordinateGeo,
	})
	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	if err := cursor.All(ctx, &hotels); err != nil {
		return nil, err
	}

	// fmt.Println("exec query geo", time.Now().UnixMilli()-start)
	return converts.ToDomainHotels(hotels), nil
}

func (r *hotelRepository) buildQueryFindHotelID(hotelIDs []string, language string) []bson.M {
	chunkSize := 1000
	var chunks [][]string

	for i := 0; i < len(hotelIDs); i += chunkSize {
		end := i + chunkSize
		if end > len(hotelIDs) {
			end = len(hotelIDs)
		}

		chunks = append(chunks, hotelIDs[i:end])
	}

	result := []bson.M{}

	for _, chunk := range chunks {
		filter := bson.M{
			"$or": bson.A{},
		}
		for _, hotelID := range chunk {
			filter["$or"] = append(filter["$or"].(bson.A), bson.M{
				"hotel_id": hotelID,
			})
		}

		filter["language"] = language

		result = append(result, filter)
	}

	return result
}

func (r *hotelRepository) buildQueryFindByProviderHotelID(provider commonEnum.HotelProvider, hotelIDs []string, language string) []bson.M {
	chunkSize := 1000
	var chunks [][]string

	for i := 0; i < len(hotelIDs); i += chunkSize {
		end := i + chunkSize
		if end > len(hotelIDs) {
			end = len(hotelIDs)
		}

		chunks = append(chunks, hotelIDs[i:end])
	}

	providerKey := fmt.Sprintf("provider_ids.%d", provider)

	result := []bson.M{}

	for _, chunk := range chunks {
		filter := bson.M{
			"$or": bson.A{},
		}
		for _, hotelID := range chunk {
			filter["$or"] = append(filter["$or"].(bson.A), bson.M{
				providerKey: hotelID,
			})
		}

		filter["language"] = language

		result = append(result, filter)
	}

	return result
}

// Deprecated due to performance.
func (r *hotelRepository) FindByProviderHotelIDForCache_Deprecated(ctx context.Context, provider commonEnum.HotelProvider, hotelIDs []string, language string, limit int) ([]*domain.Hotel, error) {
	sf := []*models.Hotel{}

	providerKey := fmt.Sprintf("provider_ids.%d", provider)

	filter := bson.M{
		providerKey: bson.M{
			"$in": hotelIDs,
		},
		"language": language,
	}

	// t := time.Now()
	index := models.GetProviderIdsLanguageSorterIndex(provider)

	if err := r.db.Find(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(index),
		mongodb.WithFilter(filter),
		mongodb.WithSorter(bson.M{"ratings.guest.overall": -1}),
		mongodb.WithPaging(&commonDomain.Pagination{
			PageLimit:      int64(limit),
			PageCurrent:    1,
			SkipCountTotal: true,
			MaxPageLimit:   int64(limit),
		}),
	); err != nil {
		log.Error("FindByProviderHotelID err", log.Any("err", err))
		return nil, err
	}

	// log.Info("exec time FindByProviderHotelID", log.Any("time ms", time.Now().UnixMilli()-t.UnixMilli()))

	return converts.ToDomainHotels(sf), nil
}

func (r *hotelRepository) FindByProviderHotelIDForCache(ctx context.Context, provider commonEnum.HotelProvider, hotelIDs []string, language string, limit int) ([]*domain.Hotel, error) {
	sf := []*models.Hotel{}

	if len(hotelIDs) == 0 {
		return converts.ToDomainHotels(sf), nil
	}

	var wg sync.WaitGroup
	var mutex sync.Mutex

	index := models.GetProviderIdsLanguageSorterIndex(provider)

	sort.Strings(hotelIDs)

	// Limit due to timeout
	const MAX_IDS = 10000
	if len(hotelIDs) > MAX_IDS {
		hotelIDs = hotelIDs[:MAX_IDS]
	}
	// start := time.Now().UnixMilli()
	for _, filter := range r.buildQueryFindByProviderHotelID(provider, hotelIDs, language) {
		wg.Add(1)

		go func() {
			mHotels := []*models.Hotel{}
			if err := r.db.Find(ctx, r.getCollectionName(), &mHotels,
				mongodb.WithHint(index),
				mongodb.WithFilter(filter),
			); err != nil {
				log.Error("FindByProviderHotelID err", log.Any("err", err))
				return
			}

			mutex.Lock()
			sf = append(sf, mHotels...)
			mutex.Unlock()

			defer wg.Done()
		}()
	}

	wg.Wait()
	// fmt.Println("exec time FindByProviderHotelID", time.Now().UnixMilli()-start)

	sort.Sort(HotelSlice(sf))

	if len(sf) > limit {
		sf = sf[:limit]
	}

	return converts.ToDomainHotels(sf), nil
}

func (r *hotelRepository) FindHotelIDForCache(ctx context.Context, hotelIDs []string, language string, limit int) ([]*domain.Hotel, error) {
	sf := []*models.Hotel{}

	if len(hotelIDs) == 0 {
		return converts.ToDomainHotels(sf), nil
	}

	var wg sync.WaitGroup
	var mutex sync.Mutex

	sort.Strings(hotelIDs)

	// Limit due to timeout
	const MAX_IDS = 10000
	if len(hotelIDs) > MAX_IDS {
		hotelIDs = hotelIDs[:MAX_IDS]
	}
	// start := time.Now().UnixMilli()
	for _, filter := range r.buildQueryFindHotelID(hotelIDs, language) {
		wg.Add(1)

		go func() {
			mHotels := []*models.Hotel{}
			if err := r.db.Find(ctx, r.getCollectionName(), &mHotels,
				mongodb.WithHint(models.HotelCollectionHotelIDLanguageVersionIndex),
				mongodb.WithFilter(filter),
			); err != nil {
				log.Error("FindHotelIDForCache err", log.Any("err", err))
				return
			}

			mutex.Lock()
			sf = append(sf, mHotels...)
			mutex.Unlock()

			defer wg.Done()
		}()
	}

	wg.Wait()
	// fmt.Println("exec time FindByProviderHotelID", time.Now().UnixMilli()-start)

	sort.Sort(HotelSlice(sf))

	if len(sf) > limit {
		sf = sf[:limit]
	}

	return converts.ToDomainHotels(sf), nil
}

func (r *hotelRepository) FindByProviderHotelID(ctx context.Context, provider commonEnum.HotelProvider, hotelIDs []string, language string) ([]*domain.Hotel, error) {
	sf := []*models.Hotel{}

	if len(hotelIDs) == 0 {
		return converts.ToDomainHotels(sf), nil
	}

	var wg sync.WaitGroup
	var mutex sync.Mutex

	index := models.GetProviderIdsLanguageSorterIndex(provider)

	sort.Strings(hotelIDs)
	// start := time.Now().UnixMilli()
	for _, filter := range r.buildQueryFindByProviderHotelID(provider, hotelIDs, language) {
		wg.Add(1)

		go func() {
			mHotels := []*models.Hotel{}
			if err := r.db.Find(ctx, r.getCollectionName(), &mHotels,
				mongodb.WithHint(index),
				// mongodb.WithProjection(projection),
				mongodb.WithFilter(filter),
			); err != nil {
				log.Error("FindByProviderHotelID err", log.Any("err", err))
				return
			}

			mutex.Lock()
			sf = append(sf, mHotels...)
			mutex.Unlock()

			defer wg.Done()
		}()
	}

	wg.Wait()
	// fmt.Println("exec time FindByProviderHotelID", time.Now().UnixMilli()-start)

	sort.Sort(HotelSlice(sf))

	return converts.ToDomainHotels(sf), nil
}

func (r *hotelRepository) FindByProviderIds(ctx context.Context, provider commonEnum.HotelProvider, hotelID string) ([]*domain.Hotel, error) {
	var out []*models.Hotel

	providerKey := fmt.Sprintf("provider_ids.%d", provider)
	filter := bson.M{
		providerKey: hotelID,
	}

	index := models.GetProviderIdsLanguageSorterIndex(provider)

	if err := r.db.Find(ctx, r.getCollectionName(), &out,
		mongodb.WithHint(index),
		mongodb.WithFilter(filter),
	); err != nil {
		return nil, err
	}

	return converts.ToDomainHotels(out), nil
}

func (r *hotelRepository) FindByProviderAndContentVersionHotelID(ctx context.Context, provider commonEnum.HotelProvider, contentVersion, hotelID, language string) (*domain.Hotel, error) {
	var sf *models.Hotel

	// start := time.Now().UnixMilli()

	if hotelID == "" {
		return converts.ToDomainHotel(sf), nil
	}

	providerKey := fmt.Sprintf("provider_ids.%d", provider)
	filter := bson.M{
		providerKey: hotelID,
		"language":  language,
	}

	index := models.GetProviderIdsLanguageSorterIndex(provider)

	if err := r.db.FindOne(ctx, r.getCollectionName(), &sf,
		mongodb.WithHint(index),
		mongodb.WithFilter(filter),
	); err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		log.Error("FindByProviderAndContentVersionHotelID err", log.Any("err", err))

		return nil, err
	}

	// fmt.Println("exec FindByProviderAndContentVersionHotelID", time.Now().UnixMilli()-start)
	return converts.ToDomainHotel(sf), nil
}
