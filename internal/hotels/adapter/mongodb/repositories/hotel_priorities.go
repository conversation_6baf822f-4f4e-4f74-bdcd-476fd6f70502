package repositories

import (
	"context"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"go.mongodb.org/mongo-driver/bson"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const priorityHotelsCollectionName = "hotel_priorities"

type HotelPrioritiesRepository interface {
	FindAll(ctx context.Context) ([]*domain.HotelPriority, error)
}

type priorityHotelsRepository struct {
	db mongodb.DB
}

func NewHotelPrioritiesRepository(db mongodb.DB) HotelPrioritiesRepository {
	return &priorityHotelsRepository{
		db: db,
	}
}

func (r *priorityHotelsRepository) FindAll(ctx context.Context) ([]*domain.HotelPriority, error) {
	var sf []*models.HotelPriority

	if err := r.db.Find(ctx, priorityHotelsCollectionName, &sf,
		mongodb.WithHint(models.DefaultIndex),
		mongodb.WithFilter(bson.M{"content_priority": true})); err != nil {
		return nil, err
	}

	return converts.ToDomainHotelPriorities(sf), nil
}
