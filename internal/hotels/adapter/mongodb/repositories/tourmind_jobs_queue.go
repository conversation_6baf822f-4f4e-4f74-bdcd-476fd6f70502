package repositories

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const tourmindJobsQueueCollectionName = "tourmind_jobs_queue"

type TourmindJobsQueueRepository interface {
	RetrieveActiveJobs(ctx context.Context, countryCode string) ([]*domain.TourmindJobsQueue, error)
	CreateMany(ctx context.Context, rourmindJobsQueues []*domain.TourmindJobsQueue) error
	SoftDeleteMany(ctx context.Context, countryCode string) error
	MarkJobAsCompleted(ctx context.Context, job *domain.TourmindJobsQueue) error
}

type tourmindJobsQueueRepositoryIndexes struct {
	Default     string
	CountryCode string
}

type tourmindJobsQueueRepository struct {
	db      mongodb.DB
	cfg     *config.Schema
	indexes tourmindJobsQueueRepositoryIndexes
}

func newTourmindJobsQueueRepositoryIndexes() tourmindJobsQueueRepositoryIndexes {
	return tourmindJobsQueueRepositoryIndexes{
		Default:     "_id_",
		CountryCode: "__country_code__",
	}
}

// @param db: The common db.
func NewTourmindJobsQueueRepository(
	db mongodb.DB,
	cfg *config.Schema,
) TourmindJobsQueueRepository {
	return &tourmindJobsQueueRepository{
		db:      db,
		cfg:     cfg,
		indexes: newTourmindJobsQueueRepositoryIndexes(),
	}
}

func (r *tourmindJobsQueueRepository) SoftDeleteMany(ctx context.Context, countryCode string) error {
	filter := bson.M{
		"country_code": countryCode,
	}

	update := bson.M{
		"deleted_at": time.Now().UnixMilli(),
	}

	return r.db.UpdateMany(ctx, tourmindJobsQueueCollectionName, filter, update, &options.UpdateOptions{
		Hint: r.indexes.CountryCode,
	})
}

func (r *tourmindJobsQueueRepository) MarkJobAsCompleted(ctx context.Context, job *domain.TourmindJobsQueue) error {
	objID, err := primitive.ObjectIDFromHex(job.ID)
	if err != nil {
		return errors.Wrap(err, "ObjectIDFromHex id")
	}

	filter := bson.M{
		"_id": objID,
	}

	update := bson.M{
		"completed_at":   time.Now().UnixMilli(),
		"cannot_map_ids": job.CannotMapIds,
		"success_ids":    job.SuccessIds,
		"duration_ms":    job.DurationMs,
	}

	return r.db.UpdateOne(ctx, tourmindJobsQueueCollectionName, filter, update, &options.UpdateOptions{
		Hint: r.indexes.Default,
	})
}

func (r *tourmindJobsQueueRepository) RetrieveActiveJobs(ctx context.Context, countryCode string) ([]*domain.TourmindJobsQueue, error) {
	filter := bson.M{
		"country_code": countryCode,
		"completed_at": bson.M{"$exists": false},
		"deleted_at":   bson.M{"$exists": false},
	}

	m := []*models.TourmindJobsQueue{}

	err := r.db.Find(ctx, tourmindJobsQueueCollectionName, &m, mongodb.WithFilter(filter), mongodb.WithSorter(bson.M{"seq_num": 1}), mongodb.WithHint(r.indexes.CountryCode))
	if err != nil {
		return nil, err
	}

	return converts.ToDomainTourmindJobsQueues(m), nil
}

func (r *tourmindJobsQueueRepository) CreateMany(ctx context.Context, rourmindJobsQueues []*domain.TourmindJobsQueue) error {
	request := make([]mongo.WriteModel, len(rourmindJobsQueues))

	for i, req := range rourmindJobsQueues {
		m := converts.FromDomainTourmindJobsQueue(req)

		m.BeforeCreate()

		request[i] = mongo.NewInsertOneModel().
			SetDocument(m)
	}

	err := r.db.BulkWriteRaw(ctx, tourmindJobsQueueCollectionName, request)
	if err != nil {
		return err
	}

	return nil
}
