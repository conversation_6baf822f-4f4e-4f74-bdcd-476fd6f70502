package repositories

import (
	"context"
	"sync"

	"gitlab.deepgate.io/apps/common/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

// Custom Repo to sync content from source_db to destination_db
// Connect with cfg instead of db instance

type SyncContentRepository interface {
	SyncRoomContent(ctx context.Context, from int) error
	SyncHotelContent(ctx context.Context, from int) error
	MigrateHotelThumbnail(ctx context.Context, from int) error
}

type syncContentRepository struct {
	cfg *config.Schema
}

func NewSyncContentRepository(cfg *config.Schema,
) SyncContentRepository {
	return &syncContentRepository{
		cfg: cfg,
	}
}

func (r *syncContentRepository) SyncRoomContent(ctx context.Context, from int) error {
	source, err := mongo.Connect(ctx, options.Client().ApplyURI(r.cfg.CommonReadURL))
	if err != nil {
		return err
	}

	destination, err := mongo.Connect(ctx, options.Client().ApplyURI(r.cfg.CommonDesReadURL))
	if err != nil {
		return err
	}

	skip := int64(from)

	cursor, err := source.Database(r.cfg.CommonMongoDB).Collection(roomCollectionName).Find(ctx, bson.M{
		"version": constants.ContentVersion,
	}, &options.FindOptions{
		Hint: models.RoomCollectionLanguageIndex,
		Skip: &skip,
	})

	if err != nil {
		return err
	}

	defer cursor.Close(ctx)

	count := from

	for cursor.Next(ctx) {
		request := make([]mongo.WriteModel, 0, 1)
		var document models.Room

		if err := cursor.Decode(&document); err != nil {
			return err
		}

		filter := bson.M{
			"room_id":  document.RoomID,
			"language": document.Language,
		}

		update := bson.M{
			"$setOnInsert": document,
			"$set":         document,
		}

		request = append(request, mongo.NewUpdateOneModel().
			SetFilter(filter).
			SetUpdate(update).
			SetUpsert(true).
			SetHint(models.RoomCollectionRoomIDLanguageVersionIndex))

		_, err := destination.Database(r.cfg.CommonMongoDBDes).Collection(roomCollectionName).BulkWrite(ctx, request)
		if err != nil {
			return err
		}

		log.Info("Sync room ", log.Any("index", count), log.Any("RoomID", document.RoomID), log.Any("language", document.Language))

		count++
	}

	// Check for errors during iteration
	if err := cursor.Err(); err != nil {
		return err
	}

	return nil
}

func (r *syncContentRepository) SyncHotelContent(ctx context.Context, from int) error {
	source, err := mongo.Connect(ctx, options.Client().ApplyURI(r.cfg.CommonReadURL))
	if err != nil {
		return err
	}

	destination, err := mongo.Connect(ctx, options.Client().ApplyURI(r.cfg.CommonDesReadURL))
	if err != nil {
		return err
	}

	skip := int64(from)

	cursor, err := source.Database(r.cfg.CommonMongoDB).Collection(hotelCollectionName).Find(ctx, bson.M{
		"version": constants.ContentVersion,
	}, &options.FindOptions{
		Hint: models.HotelCollectionVersionIndex,
		Skip: &skip,
	})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	concurrency := 80
	semaphore := make(chan struct{}, concurrency)

	count := 0

	for cursor.Next(ctx) {
		var document models.Hotel
		if err := cursor.Decode(&document); err != nil {
			return err
		}

		filter := bson.M{
			"hotel_id": document.HotelID,
			"language": document.Language,
		}

		document.Base = models.Base{}

		update := bson.M{
			"$set": document,
		}

		count++

		wg.Add(1)

		go func(doc models.Hotel) {
			defer wg.Done()

			semaphore <- struct{}{}

			defer func() { <-semaphore }()

			request := []mongo.WriteModel{
				mongo.NewUpdateOneModel().
					SetFilter(filter).
					SetUpdate(update).
					SetUpsert(true).
					SetHint(models.HotelCollectionHotelIDLanguageVersionIndex),
			}

			_, err := destination.Database(r.cfg.CommonMongoDBDes).Collection(hotelCollectionName).BulkWrite(ctx, request)
			if err != nil {
				errChan <- err
				log.Error("Sync hotel err", log.Any("err", err), log.Any("index", count), log.Any("RoomID", doc.HotelID), log.Any("language", doc.Language))

				return
			}

			log.Info("Sync hotel ", log.Any("index", count), log.Any("hotelID", doc.HotelID), log.Any("language", doc.Language))
		}(document)
	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	for err := range errChan {
		if err != nil {
			log.Error("Bulk write error", log.Any("err", err))
			return err
		}
	}

	if err := cursor.Err(); err != nil {
		return err
	}

	return nil
}

func (r *syncContentRepository) MigrateHotelThumbnail(ctx context.Context, from int) error {
	source, err := mongo.Connect(ctx, options.Client().ApplyURI(r.cfg.CommonReadURL))
	if err != nil {
		return err
	}

	destination, err := mongo.Connect(ctx, options.Client().ApplyURI(r.cfg.CommonDesReadURL))
	if err != nil {
		return err
	}

	skip := int64(from)

	cursor, err := source.Database(r.cfg.CommonMongoDB).Collection(hotelCollectionName).Find(ctx, bson.M{}, &options.FindOptions{
		Hint: models.DefaultIndex,
		Skip: &skip,
	})
	if err != nil {
		return err
	}
	defer cursor.Close(ctx)

	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	concurrency := 80
	semaphore := make(chan struct{}, concurrency)

	count := 0

	for cursor.Next(ctx) {
		var document models.Hotel
		if err := cursor.Decode(&document); err != nil {
			return err
		}

		filter := bson.M{
			"hotel_id": document.HotelID,
			"language": document.Language,
		}

		count++

		wg.Add(1)

		go func(doc models.Hotel) {
			defer wg.Done()

			semaphore <- struct{}{}

			defer func() { <-semaphore }()

			var heroImage *models.Image

			// Check if images array exists and is not empty
			if len(doc.Images) > 0 {

				// Find the first image with hero_image: true
				for _, img := range doc.Images {
					if img.HeroImage {
						heroImage = img
						break
					}
				}

				// If no hero image found, take the first image
				if heroImage == nil {
					heroImage = doc.Images[0]
				}

			}

			_, err := destination.Database(r.cfg.CommonMongoDBDes).Collection(hotelCollectionName).UpdateOne(ctx, filter, bson.M{
				"$set": bson.M{
					"display_image": heroImage,
				},
				"$unset": bson.M{
					"thumbnail_url": "",
				},
			}, &options.UpdateOptions{
				Hint: models.HotelCollectionHotelIDLanguageVersionIndex,
			})
			if err != nil {
				errChan <- err
				log.Error("Sync hotel err", log.Any("err", err), log.Any("index", count), log.Any("RoomID", doc.HotelID), log.Any("language", doc.Language))

				return
			}

			log.Info("Sync hotel ", log.Any("index", count), log.Any("hotelID", doc.HotelID), log.Any("language", doc.Language))
		}(document)
	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	for err := range errChan {
		if err != nil {
			log.Error("Bulk write error", log.Any("err", err))
			return err
		}
	}

	if err := cursor.Err(); err != nil {
		return err
	}

	return nil
}
