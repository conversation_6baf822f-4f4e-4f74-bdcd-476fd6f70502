package repositories

import (
	"context"
	"time"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const tourmindRegionsCollectionName = "tourmind_regions"

type TourmindRegionsRepository interface {
	MarkAsCompleted(ctx context.Context, countryCode string) error
	FindAll(ctx context.Context, findUncompleted bool) ([]*domain.TourmindRegion, error)
}

type tourmindRegionsRepositoryIndexes struct {
	DefaultIndex string
}

type tourmindRegionsRepository struct {
	db      mongodb.DB
	cfg     *config.Schema
	indexes tourmindRegionsRepositoryIndexes
}

func newTourmindRegionsRepositoryIndexes() tourmindRegionsRepositoryIndexes {
	return tourmindRegionsRepositoryIndexes{
		DefaultIndex: "_id_",
	}
}

// @param db: The common db.
func NewTourmindRegionsRepository(
	db mongodb.DB,
	cfg *config.Schema,
) TourmindRegionsRepository {
	return &tourmindRegionsRepository{
		db:      db,
		cfg:     cfg,
		indexes: newTourmindRegionsRepositoryIndexes(),
	}
}

func (r *tourmindRegionsRepository) MarkAsCompleted(ctx context.Context, countryCode string) error {
	filter := bson.M{
		"country_code": countryCode,
	}

	update := bson.M{
		"completed_at": time.Now().UnixMilli(),
	}

	return r.db.UpdateOne(ctx, tourmindRegionsCollectionName, filter, update, &options.UpdateOptions{
		Hint: r.indexes.DefaultIndex,
	})
}

func (r *tourmindRegionsRepository) FindAll(ctx context.Context, findUncompleted bool) ([]*domain.TourmindRegion, error) {
	m := []*models.TourmindRegion{}

	filter := bson.M{}

	if findUncompleted {
		filter["completed_at"] = bson.M{"$exists": false}
	}

	err := r.db.Find(ctx, tourmindRegionsCollectionName, &m, mongodb.WithSorter(bson.M{"seq_num": 1}), mongodb.WithHint(r.indexes.DefaultIndex), mongodb.WithFilter(filter))
	if err != nil {
		return nil, err
	}

	return converts.ToDomainTourmindRegions(m), nil
}
