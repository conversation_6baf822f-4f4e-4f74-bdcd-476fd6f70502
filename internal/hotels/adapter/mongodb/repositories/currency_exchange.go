package repositories

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const currencyExchangeColName = "currency_exchange"

type CurrencyExchangeRepository interface {
	Find(ctx context.Context, from string) ([]*domain.CurrencyExchange, error)
	FindOne(ctx context.Context, req *domain.CurrencyExchange) (*domain.CurrencyExchange, error)
	FindByID(ctx context.Context, id string) (*domain.CurrencyExchange, error)
	FindCurrencyFromTo(ctx context.Context, to, from string) (*domain.CurrencyExchange, error)
	FindWithFilter(ctx context.Context, filterReq *domain.ListCurrencyExchangeReq) ([]*domain.CurrencyExchange, error)
	Create(ctx context.Context, req *domain.CurrencyExchange) error
	Update(ctx context.Context, req *domain.CurrencyExchange) error
}

type currencyExchangeRepositoryIndexes struct {
	from         string
	to           string
	fromTo       string
	indexDefault string
}

type currencyExchangeRepository struct {
	db      mongodb.DB
	indexes *currencyExchangeRepositoryIndexes
}

func newCurrencyExchangeRepositoryIndexes() *currencyExchangeRepositoryIndexes {
	return &currencyExchangeRepositoryIndexes{
		from:         "__from__",
		to:           "__to__",
		fromTo:       "__from_to__",
		indexDefault: "_id_",
	}
}

func NewCurrencyExchangeRepository(db mongodb.DB) CurrencyExchangeRepository {
	return &currencyExchangeRepository{
		db:      db,
		indexes: newCurrencyExchangeRepositoryIndexes(),
	}
}

func (r *currencyExchangeRepository) Find(ctx context.Context, from string) ([]*domain.CurrencyExchange, error) {
	m := []*models.CurrencyExchange{}

	filter := bson.M{
		"from": from,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.indexes.from),
	}

	err := r.db.Find(ctx, currencyExchangeColName, &m, otps...)
	if err != nil {
		return nil, err
	}

	return converts.ToDomainCurrencyExchanges(m), nil
}

func (r *currencyExchangeRepository) FindByID(ctx context.Context, id string) (*domain.CurrencyExchange, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.Wrap(err, "id ObjectIDFromHex")
	}

	result := &models.CurrencyExchange{}

	filter := bson.M{
		"_id": objID,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.indexes.indexDefault),
	}

	err = r.db.FindOne(ctx, currencyExchangeColName, &result, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil //nolint
		}
		return nil, err
	}
	return converts.ToDomainCurrencyExchange(result), nil
}

func (r *currencyExchangeRepository) FindOne(ctx context.Context, req *domain.CurrencyExchange) (*domain.CurrencyExchange, error) {
	if req == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	result := &models.CurrencyExchange{}

	filter := bson.M{
		"from": req.From,
		"to":   req.To,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.indexes.fromTo),
	}

	err := r.db.FindOne(ctx, currencyExchangeColName, &result, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil //nolint
		}
		return nil, err
	}

	return converts.ToDomainCurrencyExchange(result), nil

}

func (r *currencyExchangeRepository) FindCurrencyFromTo(ctx context.Context, to, from string) (*domain.CurrencyExchange, error) {
	m := &models.CurrencyExchange{}

	filter := bson.M{
		"from": from,
		"to":   to,
	}

	otps := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.indexes.from),
	}

	err := r.db.FindOne(ctx, currencyExchangeColName, &m, otps...)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil //nolint
		}

		return nil, err
	}

	return converts.ToDomainCurrencyExchange(m), nil
}

func (r *currencyExchangeRepository) FindWithFilter(ctx context.Context, filterReq *domain.ListCurrencyExchangeReq) ([]*domain.CurrencyExchange, error) {
	result := []*models.CurrencyExchange{}

	filter := bson.M{}
	hint := r.indexes.indexDefault

	isFromValid := filterReq.From != nil && *filterReq.From != ""
	isToValid := filterReq.To != nil && *filterReq.To != ""

	if isFromValid {
		filter["from"] = bson.M{
			"$regex":   *filterReq.From,
			"$options": "i",
		}
		hint = r.indexes.from
	}

	if isToValid {
		filter["to"] = bson.M{
			"$regex":   *filterReq.To,
			"$options": "i",
		}
		hint = r.indexes.to
	}

	if isFromValid && isToValid {
		hint = r.indexes.fromTo
	}

	opts := []mongodb.Option{
		mongodb.WithHint(hint),
		mongodb.WithFilter(filter),
	}

	pagination := filterReq.Pagination

	if pagination != nil && pagination.PageLimit > 0 {
		opts = append(opts, mongodb.WithPaging(pagination))
	}

	if err := r.db.Find(ctx, currencyExchangeColName, &result, opts...); err != nil {
		log.Error("currencyExchangeRepository FindWithFilter err", log.Any("err", err), log.Any("filter", filter))
		return nil, err
	}

	return converts.ToDomainCurrencyExchanges(result), nil
}

func (r *currencyExchangeRepository) Create(ctx context.Context, req *domain.CurrencyExchange) error {
	data := converts.FromDomainCurrencyExchange(req)
	data.BeforeCreate()

	if err := r.db.Insert(ctx, currencyExchangeColName, data); err != nil {
		log.Error("currencyExchangeRepository Create err", log.Any("err", err), log.Any("data", data.ID))
		return err
	}

	return nil
}

func (r *currencyExchangeRepository) Update(ctx context.Context, req *domain.CurrencyExchange) error {
	data := converts.FromDomainCurrencyExchange(req)
	data.BeforeUpdate()

	objID, err := primitive.ObjectIDFromHex(req.ID)
	if err != nil {
		return errors.Wrap(err, "id ObjectIDFromHex")
	}

	filter := bson.M{
		"_id": objID,
	}

	if err := r.db.UpdateOne(ctx, currencyExchangeColName, filter, data, &options.UpdateOptions{
		Hint: r.indexes.indexDefault,
	}); err != nil {
		log.Error("currencyExchangeRepository Update err", log.Any("err", err), log.Any("data", data.ID))
		return err
	}

	return nil
}
