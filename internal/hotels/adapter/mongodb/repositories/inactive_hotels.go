package repositories

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type inActiveHotelsRepository struct {
	db mongodb.DB
}

type InActiveHotelsRepository interface {
	FindByHotelIDs(ctx context.Context, hotelIDs []string) ([]*domain.InActiveHotel, error)
	FindAll(ctx context.Context) ([]*domain.InActiveHotel, error)
}

func NewInActiveHotelsRepository(db mongodb.DB) InActiveHotelsRepository {
	return &inActiveHotelsRepository{db: db}
}

func (r *inActiveHotelsRepository) FindByHotelIDs(ctx context.Context, hotelIDs []string) ([]*domain.InActiveHotel, error) {
	var hotels []*models.InActiveHotel
	filter := bson.M{"hotel_id": bson.M{"$in": hotelIDs}}
	
	opts := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(models.InActiveHotelIndexHotelID),
	}

	err := r.db.Find(ctx, models.InActiveHotelCollection, &hotels, opts...)
	if err != nil {
		return nil, err
	}

	return converts.ToDomainInActiveHotels(hotels), nil
}

func (r *inActiveHotelsRepository) FindAll(ctx context.Context) ([]*domain.InActiveHotel, error) {
	var hotels []*models.InActiveHotel
	
	opts := []mongodb.Option{
		mongodb.WithHint(models.InActiveHotelIndexHotelID),
	}
	
	err := r.db.Find(ctx, models.InActiveHotelCollection, &hotels, opts...)
	if err != nil {
		return nil, err
	}

	return converts.ToDomainInActiveHotels(hotels), nil
}