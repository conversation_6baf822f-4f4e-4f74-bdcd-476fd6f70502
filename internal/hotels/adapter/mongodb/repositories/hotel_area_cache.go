package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type hotelAreaCacheRepository struct {
	db             mongodb.DB
	indices        hotelAreaCacheRepositoryIndices
	collectionName string
}

type HotelAreaCacheRepository interface {
	FindByArea(ctx context.Context, id string, areaType enum.PlaceType) (*domain.HotelAreaCacheItem, error)
	InsertOne(ctx context.Context, req *domain.HotelAreaCacheItem) error
}

func NewHotelAreaCacheRepository(db mongodb.DB) HotelAreaCacheRepository {
	indices := newHotelAreaCacheRepositoryIndices()
	const collectionName = "hotel_area_caches"

	return &hotelAreaCacheRepository{db, indices, collectionName}
}

type hotelAreaCacheRepositoryIndices struct {
	defaultIndex string
	areaID       string
}

func newHotelAreaCacheRepositoryIndices() hotelAreaCacheRepositoryIndices {
	return hotelAreaCacheRepositoryIndices{
		defaultIndex: "_id",
		areaID:       "__area_id__",
	}
}

func (r *hotelAreaCacheRepository) InsertOne(ctx context.Context, req *domain.HotelAreaCacheItem) error {
	m := converts.FromDomainHotelAreaCacheItem(req)

	m.BeforeCreate()

	return r.db.InsertOne(ctx, r.collectionName, &m, &options.InsertOneOptions{})
}

func (r *hotelAreaCacheRepository) FindByArea(ctx context.Context, id string, areaType enum.PlaceType) (*domain.HotelAreaCacheItem, error) {
	var cacheItem *models.HotelAreaCacheItem

	filter := bson.M{
		"area_id":   id,
		"area_type": enum.PlaceTypeName[areaType],
	}

	err := r.db.FindOne(ctx, r.collectionName, &cacheItem,
		mongodb.WithFilter(filter),
		mongodb.WithHint(r.indices.areaID),
	)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}

		return nil, err
	}

	return converts.ToDomainHotelAreaCacheItem(cacheItem), nil
}
