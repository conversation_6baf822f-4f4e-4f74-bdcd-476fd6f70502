package repositories

import (
	"context"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"go.mongodb.org/mongo-driver/bson"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const bedOptionCollectionName = "bed_options"
const bedOptionIndex = "__bed_id__language__"
const bedOptionIndexLanguage = "__language__"

type BedOptionRepository interface {
	FindByBedOptionIDs(ctx context.Context, bedOptionIDs []string, language string) ([]*domain.BedOptionContent, error)
	FindByLanguage(ctx context.Context, language string) ([]*domain.BedOptionContent, error)
}

type bedOptionRepository struct {
	db mongodb.DB
}

func NewBedOptionRepository(db mongodb.DB) BedOptionRepository {
	return &bedOptionRepository{
		db: db,
	}
}

func (r *bedOptionRepository) FindByBedOptionIDs(ctx context.Context, bedOptionIDs []string, language string) ([]*domain.BedOptionContent, error) {
	var sf []*models.BedOptionContent
	if err := r.db.Find(ctx, bedOptionCollectionName, &sf,
		mongodb.WithHint(bedOptionIndex),
		mongodb.WithFilter(bson.M{
			"bed_id": bson.M{
				"$in": bedOptionIDs,
			},
			"language": language,
		})); err != nil {
		return nil, err
	}

	return converts.ToDomainContentBedOptions(sf), nil
}

func (r *bedOptionRepository) FindByLanguage(ctx context.Context, language string) ([]*domain.BedOptionContent, error) {
	var sf []*models.BedOptionContent
	if err := r.db.Find(ctx, bedOptionCollectionName, &sf,
		mongodb.WithHint(bedOptionIndexLanguage),
		mongodb.WithFilter(bson.M{
			"language": language,
		})); err != nil {
		return nil, err
	}

	return converts.ToDomainContentBedOptions(sf), nil
}
