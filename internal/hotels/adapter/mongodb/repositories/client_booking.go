package repositories

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const clientBookingColName = "client_bookings"
const (
	clientBookingIndexHubOrderCode = "__hub_order_code__partnership_id__"
)

type ClientBookingRepository interface {
	WithTransaction(ctx context.Context, fn func(tnxClientBooking context.Context) (interface{}, error)) error
	InsertOne(ctx context.Context, req *domain.ClientBooking) error
	FindByHubOrderCodes(ctx context.Context, hubOrderCodes []string, pID string) ([]*domain.ClientBooking, error)
	UpdateByHubOrderCodes(ctx context.Context, updateReq *domain.ClientBookingUpdateReq) error
}

type clientBookingRepository struct {
	db mongodb.DB
}

func NewClientBookingRepository(db mongodb.DB) ClientBookingRepository {
	return &clientBookingRepository{db}
}

func (r *clientBookingRepository) WithTransaction(ctx context.Context, fn func(tnxClientBooking context.Context) (interface{}, error)) error {
	_, err := r.db.WithTransaction(ctx, fn)
	if err != nil {
		return err
	}

	return nil
}

func (r *clientBookingRepository) UpdateByHubOrderCodes(ctx context.Context, updateReq *domain.ClientBookingUpdateReq) error {
	if updateReq == nil {
		return nil
	}

	filter := bson.M{
		"hub_order_code": bson.M{
			"$in": updateReq.HubOrderCodes,
		},
		"partnership_id": updateReq.PartnershipID,
	}

	update := &bson.M{
		"updated_at": time.Now().UnixMilli(),
		"paid":       updateReq.Paid,
	}

	return r.db.UpdateMany(ctx, clientBookingColName, filter, update, &options.UpdateOptions{
		Hint: clientBookingIndexHubOrderCode,
	})
}

func (r *clientBookingRepository) InsertOne(ctx context.Context, req *domain.ClientBooking) error {
	m := converts.FromDomainClientBooking(req)

	m.BeforeCreate()

	return r.db.InsertOne(ctx, clientBookingColName, &m, &options.InsertOneOptions{})
}

func (r *clientBookingRepository) FindByHubOrderCodes(ctx context.Context, hubOrderCodes []string, partnershipID string) ([]*domain.ClientBooking, error) {
	var res []*models.ClientBooking

	filter := bson.M{
		"hub_order_code": bson.M{
			"$in": hubOrderCodes,
		},
		"partnership_id": partnershipID,
	}

	options := []mongodb.Option{
		mongodb.WithFilter(filter),
		mongodb.WithHint(clientBookingIndexHubOrderCode),
	}

	err := r.db.FindWithRetry(ctx,
		constants.MongoRetryCount,
		constants.MongoRetryDelay,
		clientBookingColName,
		&res,
		options...,
	)
	if err != nil {
		return nil, errors.Wrap(err, "FindWithRetry")
	}

	return converts.ToDomainClientBookings(res), nil
}
