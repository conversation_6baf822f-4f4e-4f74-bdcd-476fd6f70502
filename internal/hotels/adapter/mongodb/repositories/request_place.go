package repositories

import (
	"context"
	"fmt"

	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const placeCollectionName = "request_places"

type RequestPlaceRepository interface {
	LogPlace(ctx context.Context, place *domain.Place) (string, error)
}

type requestPlaceRepository struct {
	db mongodb.DB
}

func NewRequestPlaceRepository(db mongodb.DB) RequestPlaceRepository {
	return &requestPlaceRepository{db}
}

func (r *requestPlaceRepository) LogPlace(ctx context.Context, place *domain.Place) (string, error) {
	mPlace := converts.FromDomainPlace(place)
	mPlace.BeforeCreate()

	if err := r.db.Insert(ctx, placeCollectionName, mPlace); err != nil {
		return "", fmt.Errorf("Insert : %w", err)
	}
	return mPlace.ID.Hex(), nil
}
