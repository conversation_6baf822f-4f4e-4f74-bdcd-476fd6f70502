package repositories

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/mongodb"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const displayImageCollectionName = "hotel_display_images"
const displayImageHotelIDIndex = "__hotel_id__"

type DisplayImageRepository interface {
	FindByDisplayImageHotelIDs(ctx context.Context, hotelIDs []string) ([]*domain.DisplayImageContent, error)
	CreateBulk(ctx context.Context, req []*domain.DisplayImageContent) error
}

type displayImageRepository struct {
	db mongodb.DB
}

func NewDisplayImageRepository(db mongodb.DB) DisplayImageRepository {
	return &displayImageRepository{
		db: db,
	}
}

func (r *displayImageRepository) FindByDisplayImageHotelIDs(ctx context.Context, hotelIDs []string) ([]*domain.DisplayImageContent, error) {
	var sf []*models.DisplayImageContent
	if err := r.db.Find(ctx, displayImageCollectionName, &sf,
		mongodb.WithHint(displayImageHotelIDIndex),
		mongodb.WithFilter(bson.M{
			"hotel_id": bson.M{
				"$in": hotelIDs,
			},
		})); err != nil {
		return nil, err
	}

	result := make([]*domain.DisplayImageContent, 0, len(sf))

	for _, item := range sf {
		if item == nil {
			continue
		}

		images := converts.ConvertModelSummaryImagesToDomain([]*models.HotelSummaryImage{item.Image})
		if len(images) == 0 {
			continue
		}

		result = append(result, &domain.DisplayImageContent{
			HotelID: item.HotelID,
			Image:   images[0],
		})
	}

	return result, nil
}

func (r *displayImageRepository) CreateBulk(ctx context.Context, req []*domain.DisplayImageContent) error {
	request := make([]mongo.WriteModel, 0, len(req))

	for _, item := range req {
		createdData := &models.DisplayImageContent{
			HotelID: item.HotelID,
			Image: &models.HotelSummaryImage{
				HeroImage: item.Image.HeroImage,
				Category:  item.Image.Category,
				Links: models.HotelSummaryImageLink{
					Px70:   item.Image.Links.Px70,
					Px200:  item.Image.Links.Px200,
					Px350:  item.Image.Links.Px350,
					Px1000: item.Image.Links.Px1000,
				},
				Caption: item.Image.Caption,
			},
		}
		createdData.BeforeCreate()

		request = append(request, mongo.NewInsertOneModel().SetDocument(createdData))
	}

	if len(request) == 0 {
		return nil
	}

	// for _, doc := range request {
	// 	err := r.db.Insert(ctx, displayImageCollectionName, doc)
	// 	if err != nil {
	// 		// Log the error and skip this document
	// 		fmt.Printf("Error inserting document: %v. Skipping...\n", err)
	// 		continue
	// 	}
	// }
	err := r.db.BulkWriteRaw(ctx, displayImageCollectionName, request)
	if err != nil {
		return errors.Wrap(err, "db.BulkWriteRaw")
	}

	return nil
}
