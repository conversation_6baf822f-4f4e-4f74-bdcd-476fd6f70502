package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainHotelSession(in *domain.HotelSession) *models.HotelSession {
	if in == nil {
		return nil
	}

	result := &models.HotelSession{
		SessionID: in.SessionID,
		OfficeID:  in.OfficeID,
		ExpiredAt: in.ExpiredAt,
	}

	if in.ExpediaSessionInfo != nil {
		result.ExpediaSessionInfo = &models.ExpediaSessionInfo{
			RetrieveBookingSession: in.ExpediaSessionInfo.RetrieveBookingSession,
			HoldBookingSession:     in.ExpediaSessionInfo.HoldBookingSession,
			CancelBookingSession:   in.ExpediaSessionInfo.CancelBookingSession,
		}
	}

	if in.TourmindSessionInfo != nil {
		result.TourmindSessionInfo = &models.TourmindSessionInfo{
			RateCode:  in.TourmindSessionInfo.RateCode,
			HotelCode: in.TourmindSessionInfo.HotelCode,
		}
	}

	if in.BasicSessionInfo != nil {
		result.BasicSessionInfo = &models.BasicSessionInfo{
			BookingKey: in.BasicSessionInfo.BookingKey,
		}
	}

	if in.DidaSessionInfo != nil {
		result.DidaSessionInfo = &models.DidaSessionInfo{
			ReferenceNo: in.DidaSessionInfo.ReferenceNo,
		}
	}

	if in.HubSessionInfo != nil {
		result.HubSessionInfo = &models.HubSessionInfo{
			SessionID: in.HubSessionInfo.SessionID,
		}
	}

	if in.AgodaSessionInfo != nil {
		result.AgodaSessionInfo = &models.AgodaSessionInfo{
			SearchID:     in.AgodaSessionInfo.SearchID,
			PaymentModel: in.AgodaSessionInfo.PaymentModel,
		}
	}

	return result
}

func ToDomainHotelSessions(ins []*models.HotelSession) []*domain.HotelSession {
	out := make([]*domain.HotelSession, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainHotelSession(item))
	}

	return out
}

func ToDomainHotelSession(in *models.HotelSession) *domain.HotelSession {
	if in == nil {
		return nil
	}

	result := &domain.HotelSession{
		SessionID: in.SessionID,
		OfficeID:  in.OfficeID,
		ExpiredAt: in.ExpiredAt,
	}

	if in.ExpediaSessionInfo != nil {
		result.ExpediaSessionInfo = &domain.ExpediaSessionInfo{
			RetrieveBookingSession: in.ExpediaSessionInfo.RetrieveBookingSession,
			HoldBookingSession:     in.ExpediaSessionInfo.HoldBookingSession,
			CancelBookingSession:   in.ExpediaSessionInfo.CancelBookingSession,
		}
	}

	if in.TourmindSessionInfo != nil {
		result.TourmindSessionInfo = &domain.TourmindSessionInfo{
			RateCode:  in.TourmindSessionInfo.RateCode,
			HotelCode: in.TourmindSessionInfo.HotelCode,
		}
	}

	if in.BasicSessionInfo != nil {
		result.BasicSessionInfo = &domain.BasicSessionInfo{
			BookingKey: in.BasicSessionInfo.BookingKey,
		}
	}

	if in.DidaSessionInfo != nil {
		result.DidaSessionInfo = &domain.DidaSessionInfo{
			ReferenceNo:            in.DidaSessionInfo.ReferenceNo,
			CancelBookingConfirmID: in.DidaSessionInfo.CancelBookingConfirmID,
		}
	}

	if in.HubSessionInfo != nil {
		result.HubSessionInfo = &domain.HubSessionInfo{
			SessionID: in.HubSessionInfo.SessionID,
		}
	}

	if in.AgodaSessionInfo != nil {
		result.AgodaSessionInfo = &domain.AgodaSessionInfo{
			SearchID:     in.AgodaSessionInfo.SearchID,
			PaymentModel: in.AgodaSessionInfo.PaymentModel,
		}
	}

	return result
}
