package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func FromDomainHotel(d *domain.Hotel) *models.Hotel {
	if d == nil {
		return nil
	}

	return &models.Hotel{
		Base:                           FromDomainBase(d.Base),
		Active:                         d.Active,
		MultiUnit:                      d.MultiUnit,
		PaymentRegistrationRecommended: d.PaymentRegistrationRecommended,
		Rank:                           d.Rank,
		Address:                        FromDomainAddress(d.Address),
		Ratings:                        FromDomainRatings(d.Ratings),
		Location:                       FromDomainLocation(d.Location),
		RoomReferences:                 FromDomainRoomReferences(d.RoomReferences),
		Category:                       FromDomainCategory(d.Category),
		BusinessModel:                  FromDomainBusinessModel(d.BusinessModel),
		CheckIn:                        FromDomainCheckin(d.CheckIn),
		Checkout:                       FromDomainCheckout(d.Checkout),
		Fees:                           FromDomain<PERSON>ees(d.<PERSON>es),
		Policies:                       FromDomainPolicies(d.Policies),
		Attributes:                     FromDomainAttributes(d.Attributes),
		OnsitePayments:                 FromDomainOnsitePayments(d.OnsitePayments),
		Dates:                          FromDomainDates(d.Dates),
		Descriptions:                   FromDomainDescriptions(d.Descriptions),
		Chain:                          FromDomainChain(d.Chain),
		Brand:                          FromDomainBrand(d.Brand),
		SpokenLanguages:                FromDomainSpokenLanguages(d.SpokenLanguages),
		ProviderIds:                    d.ProviderIds,
		Language:                       d.Language,
		HotelID:                        d.HotelID,
		Name:                           d.Name,
		Phone:                          d.Phone,
		Fax:                            d.Fax,
		SupplySource:                   d.SupplySource,
		Amenities:                      FromDomainAmenities(d.Amenities),
		Images:                         FromDomainImages(d.Images),
		Rates:                          FromDomainContentRates(d.Rates),
		Statistics:                     FromDomainStatistics(d.Statistics),
		Airports:                       FromDomainAirports(d.Airports),
		AllInclusive:                   FromDomainAllInclusive(d.AllInclusive),
		VacationRentalDetails:          FromDomainVacationRentalDetails(d.VacationRentalDetails),
		TaxID:                          d.TaxID,
		RegistryNumber:                 d.RegistryNumber,
		Themes:                         FromDomainThemes(d.Themes),
		Version:                        d.Version,
		Distance:                       d.Distance,
	}
}

func FromDomainAirports(d *domain.Airports) *models.Airports {
	if d == nil {
		return nil
	}

	return &models.Airports{
		Preferred: FromDomainPreferredAirport(d.Preferred),
	}
}

func FromDomainPreferredAirport(d *domain.PreferredAirport) *models.PreferredAirport {
	if d == nil {
		return nil
	}

	return &models.PreferredAirport{
		IataAirportCode: d.IataAirportCode,
	}
}

func FromDomainAddress(d *domain.Address) *models.Address {
	if d == nil {
		return nil
	}

	return &models.Address{
		Line1:               d.Line1,
		City:                d.City,
		StateProvinceName:   d.StateProvinceName,
		PostalCode:          d.PostalCode,
		CountryCode:         d.CountryCode,
		ObfuscationRequired: d.ObfuscationRequired,
	}
}

func FromDomainRatings(d *domain.Ratings) *models.Ratings {
	if d == nil {
		return nil
	}

	return &models.Ratings{
		Property: FromDomainPropertyRating(d.Property),
		Guest:    FromDomainGuestRating(d.Guest),
	}
}

func FromDomainPropertyRating(d *domain.PropertyRating) *models.PropertyRating {
	if d == nil {
		return nil
	}

	return &models.PropertyRating{
		Rating: d.Rating,
		Type:   d.Type,
	}
}

func FromDomainGuestRating(d *domain.GuestRating) *models.GuestRating {
	if d == nil {
		return nil
	}

	return &models.GuestRating{
		Count:                 d.Count,
		Overall:               d.Overall,
		Cleanliness:           d.Cleanliness,
		Service:               d.Service,
		Comfort:               d.Comfort,
		Condition:             d.Condition,
		Location:              d.Location,
		Neighborhood:          d.Neighborhood,
		Quality:               d.Quality,
		Value:                 d.Value,
		Amenities:             d.Amenities,
		RecommendationPercent: d.RecommendationPercent,
	}
}

func FromDomainLocation(d *domain.Location) *models.Location {
	if d == nil {
		return nil
	}

	return &models.Location{
		Coordinates:           FromDomainCoordinates(d.Coordinates),
		ObfuscatedCoordinates: FromDomainCoordinates(d.ObfuscatedCoordinates),
		ObfuscationRequired:   d.ObfuscationRequired,
	}
}

func FromDomainCoordinates(d *domain.Coordinates) *models.Coordinates {
	if d == nil {
		return nil
	}

	return &models.Coordinates{
		Latitude:  d.Latitude,
		Longitude: d.Longitude,
	}
}

func FromDomainCategory(d *domain.Category) *models.Category {
	if d == nil {
		return nil
	}

	return &models.Category{
		ID:   d.ID,
		Name: d.Name,
	}
}

func FromDomainBusinessModel(d *domain.BusinessModel) *models.BusinessModel {
	if d == nil {
		return nil
	}

	return &models.BusinessModel{
		ExpediaCollect:  d.ExpediaCollect,
		PropertyCollect: d.PropertyCollect,
	}
}

func FromDomainCheckin(d *domain.CheckIn) *models.CheckIn {
	if d == nil {
		return nil
	}

	return &models.CheckIn{
		TwentyFourHour:      d.TwentyFourHour,
		BeginTime:           d.BeginTime,
		EndTime:             d.EndTime,
		Instructions:        d.Instructions,
		SpecialInstructions: d.SpecialInstructions,
		MinAge:              d.MinAge,
	}
}

func FromDomainCheckout(d *domain.Checkout) *models.Checkout {
	if d == nil {
		return nil
	}

	return &models.Checkout{
		Time: d.Time,
	}
}

func FromDomainFees(d *domain.Fees) *models.Fees {
	if d == nil {
		return nil
	}

	return &models.Fees{
		Mandatory:       d.Mandatory,
		Optional:        d.Optional,
		TravelerService: d.TravelerService,
	}
}

func FromDomainPolicies(d *domain.Policies) *models.Policies {
	if d == nil {
		return nil
	}

	return &models.Policies{
		KnowBeforeYouGo: d.KnowBeforeYouGo,
	}
}

func FromDomainAttributes(d *domain.Attributes) *models.Attributes {
	if d == nil {
		return nil
	}

	return &models.Attributes{
		Pets:    FromDomainAttributeList(d.Pets),
		General: FromDomainAttributeList(d.General),
	}
}

func FromDomainAttributeList(d []*domain.Attribute) []*models.Attribute {
	if d == nil {
		return nil
	}

	var result []*models.Attribute
	for _, attr := range d {
		result = append(result, FromDomainAttribute(attr))
	}

	return result
}

func FromDomainAttribute(d *domain.Attribute) *models.Attribute {
	if d == nil {
		return nil
	}

	return &models.Attribute{
		ID:    d.ID,
		Name:  d.Name,
		Value: d.Value,
	}
}

func FromDomainAmenities(d []*domain.Amenity) []*models.Amenity {
	if d == nil {
		return nil
	}

	var result []*models.Amenity
	for _, amenity := range d {
		result = append(result, FromDomainAmenity(amenity))
	}

	return result
}

func FromDomainMapAmenities(d map[string]*domain.Amenity) map[string]*models.Amenity {
	if d == nil {
		return nil
	}

	result := make(map[string]*models.Amenity)
	for key, amenity := range d {
		result[key] = FromDomainAmenity(amenity)
	}

	return result
}

func FromDomainAmenity(d *domain.Amenity) *models.Amenity {
	if d == nil {
		return nil
	}

	return &models.Amenity{
		ID:         d.ID,
		Name:       d.Name,
		Categories: d.Categories,
		Value:      d.Value,
		GroupName:  d.GroupName,
	}
}

func FromDomainImages(d []*domain.Image) []*models.Image {
	if d == nil {
		return nil
	}

	var result []*models.Image
	for _, image := range d {
		result = append(result, FromDomainImage(image))
	}

	return result
}

func FromDomainImage(d *domain.Image) *models.Image {
	if d == nil {
		return nil
	}

	return &models.Image{
		HeroImage: d.HeroImage,
		Category:  d.Category,
		Links:     FromDomainLinks(d.Links),
		Caption:   d.Caption,
	}
}

func FromDomainLinks(d map[string]*domain.Link) map[string]*models.Link {
	if d == nil {
		return nil
	}

	result := make(map[string]*models.Link)
	for key, link := range d {
		result[key] = FromDomainLink(link)
	}

	return result
}

func FromDomainLink(d *domain.Link) *models.Link {
	if d == nil {
		return nil
	}

	return &models.Link{
		Method: d.Method,
		Href:   d.Href,
	}
}

func FromDomainOnsitePayments(d *domain.OnsitePayments) *models.OnsitePayments {
	if d == nil {
		return nil
	}

	return &models.OnsitePayments{
		Currency: d.Currency,
		Types:    FromDomainPaymentTypes(d.Types),
	}
}

func FromDomainPaymentTypes(d []*domain.PaymentType) []*models.PaymentType {
	if d == nil {
		return nil
	}

	var result []*models.PaymentType
	for _, pt := range d {
		result = append(result, FromDomainPaymentType(pt))
	}

	return result
}

func FromDomainPaymentType(d *domain.PaymentType) *models.PaymentType {
	if d == nil {
		return nil
	}

	return &models.PaymentType{
		ID:   d.ID,
		Name: d.Name,
	}
}

func FromDomainContentRates(d []*domain.ContentRate) []*models.ContentRate {
	if d == nil {
		return nil
	}

	var result []*models.ContentRate
	for _, rate := range d {
		result = append(result, FromDomainContentRate(rate))
	}

	return result
}

func FromDomainContentRate(d *domain.ContentRate) *models.ContentRate {
	if d == nil {
		return nil
	}

	return &models.ContentRate{
		ID:        d.ID,
		Amenities: FromDomainAmenities(d.Amenities),
	}
}

func FromDomainDates(d *domain.Dates) *models.Dates {
	if d == nil {
		return nil
	}

	return &models.Dates{
		Added:   d.Added,
		Updated: d.Updated,
	}
}

func FromDomainDescriptions(d *domain.Descriptions) *models.Descriptions {
	if d == nil {
		return nil
	}

	return &models.Descriptions{
		Amenities:         d.Amenities,
		Dining:            d.Dining,
		BusinessAmenities: d.BusinessAmenities,
		Rooms:             d.Rooms,
		Attractions:       d.Attractions,
		Location:          d.Location,
		Headline:          d.Headline,
	}
}

func FromDomainStatistics(d []*domain.Statistic) []*models.Statistic {
	if d == nil {
		return nil
	}

	var result []*models.Statistic
	for _, stat := range d {
		result = append(result, FromDomainStatistic(stat))
	}

	return result
}

func FromDomainStatistic(d *domain.Statistic) *models.Statistic {
	if d == nil {
		return nil
	}

	return &models.Statistic{
		ID:    d.ID,
		Name:  d.Name,
		Value: d.Value,
	}
}

func FromDomainChain(d *domain.Chain) *models.Chain {
	if d == nil {
		return nil
	}

	return &models.Chain{
		ID:   d.ID,
		Name: d.Name,
	}
}

func FromDomainBrand(d *domain.Brand) *models.Brand {
	if d == nil {
		return nil
	}

	return &models.Brand{
		ID:   d.ID,
		Name: d.Name,
	}
}

func FromDomainSpokenLanguages(d map[string]*domain.Language) map[string]*models.Language {
	if d == nil {
		return nil
	}

	result := make(map[string]*models.Language)
	for key, lang := range d {
		result[key] = FromDomainLanguage(lang)
	}

	return result
}

func FromDomainLanguage(d *domain.Language) *models.Language {
	if d == nil {
		return nil
	}

	return &models.Language{
		ID:   d.ID,
		Name: d.Name,
	}
}

func FromDomainRoomReferences(domainRoomReferences []*domain.RoomRefInfo) []*models.RoomRefInfo {
	if len(domainRoomReferences) == 0 {
		return nil
	}

	modelsRoomReferences := make([]*models.RoomRefInfo, len(domainRoomReferences))

	for i, roomReference := range domainRoomReferences {
		objectID, _ := primitive.ObjectIDFromHex(roomReference.ID)
		modelsRoomReferences[i] = &models.RoomRefInfo{
			ID:          objectID,
			ProviderIds: roomReference.ProviderIds,
			RoomID:      roomReference.RoomID,
		}
	}

	return modelsRoomReferences
}

func FromDomainVacationRentalDetails(domainVR *domain.VacationRentalDetails) *models.VacationRentalDetails {
	if domainVR == nil {
		return nil
	}

	return &models.VacationRentalDetails{
		RegistryNumber:     domainVR.RegistryNumber,
		PrivateHost:        domainVR.PrivateHost,
		PropertyManager:    FromDomainPropertyManager(domainVR.PropertyManager),
		RentalAgreement:    FromDomainRentalAgreement(domainVR.RentalAgreement),
		HouseRules:         domainVR.HouseRules,
		EnhancedHouseRules: FromDomainEnhancedHouseRules(domainVR.EnhancedHouseRules),
		Amenities:          FromDomainMapAmenities(domainVR.Amenities),
		VrboSrpID:          domainVR.VrboSrpID,
		ListingID:          domainVR.ListingID,
		ListingNumber:      domainVR.ListingNumber,
		ListingSource:      domainVR.ListingSource,
		ListingUnit:        domainVR.ListingUnit,
		IPMName:            domainVR.IPMName,
		UnitConfigurations: FromDomainUnitConfigurations(domainVR.UnitConfigurations),
	}
}

func FromDomainPropertyManager(domainPM *domain.PropertyManager) *models.PropertyManager {
	if domainPM == nil {
		return nil
	}

	return &models.PropertyManager{
		Name:  domainPM.Name,
		Links: FromDomainLinks(domainPM.Links),
	}
}

func FromDomainRentalAgreement(domainRA *domain.RentalAgreement) *models.RentalAgreement {
	if domainRA == nil {
		return nil
	}

	return &models.RentalAgreement{
		Links: FromDomainLinks(domainRA.Links),
	}
}

func FromDomainEnhancedHouseRules(domainRules map[string]*domain.EnhancedRule) map[string]*models.EnhancedRule {
	if domainRules == nil {
		return nil
	}

	modelRules := make(map[string]*models.EnhancedRule)
	for key, domainRule := range domainRules {
		modelRules[key] = &models.EnhancedRule{
			Rule:                  domainRule.Rule,
			AdditionalInformation: domainRule.AdditionalInformation,
		}
	}

	return modelRules
}

func FromDomainUnitConfigurations(domainConfigs map[string][]*domain.UnitConfig) map[string][]*models.UnitConfig {
	if domainConfigs == nil {
		return nil
	}

	modelConfigs := make(map[string][]*models.UnitConfig)

	for key, domainConfigList := range domainConfigs {
		var modelConfigList []*models.UnitConfig
		for _, domainConfig := range domainConfigList {
			modelConfigList = append(modelConfigList, &models.UnitConfig{
				Type:        domainConfig.Type,
				Description: domainConfig.Description,
				Quantity:    domainConfig.Quantity,
				FreeText:    domainConfig.FreeText,
			})
		}
		modelConfigs[key] = modelConfigList
	}

	return modelConfigs
}

func FromDomainThemes(themes []*domain.Theme) []*models.Theme {
	var modelsThemes []*models.Theme
	for _, theme := range themes {
		modelsThemes = append(modelsThemes, &models.Theme{
			ID:   theme.ID,
			Name: theme.Name,
		})
	}

	return modelsThemes
}

func FromDomainAllInclusive(allInclusive *domain.AllInclusive) *models.AllInclusive {
	if allInclusive == nil {
		return nil
	}

	return &models.AllInclusive{
		AllRatePlans:  allInclusive.AllRatePlans,
		SomeRatePlans: allInclusive.SomeRatePlans,
		Details:       allInclusive.Details,
	}
}
