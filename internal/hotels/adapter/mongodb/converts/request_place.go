package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainPlace(in *domain.Place) *models.RequestPlace {
	if in == nil {
		return nil
	}

	return &models.RequestPlace{
		PlaceID:     in.PlaceID,
		Type:        in.Type,
		Name:        in.Name,
		Lang:        in.Lang,
		Location:    FromDomainCoordinates(in.Location),
		Address:     in.Address,
		CountryCode: in.CountryCode,
		Source: in.Source,
	}
}

func ToDomainPlace(in *models.RequestPlace) *domain.Place {
	if in == nil {
		return nil
	}

	return &domain.Place{
		PlaceID:     in.PlaceID,
		Type:        in.Type,
		Name:        in.Name,
		Lang:        in.Lang,
		Location:    ToDomainCoordinates(in.Location),
		Address:     in.Address,
		CountryCode: in.CountryCode,
		Source: in.Source,
	}
}
