package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ConvertDomainToModelsHotelSummaries(domainSummaries []*domain.HotelSummary) []*models.HotelSummary {
	result := make([]*models.HotelSummary, 0, len(domainSummaries))

	for _, item := range domainSummaries {
		result = append(result, ConvertDomainToModelsHotelSummary(item))
	}

	return result
}

func ConvertDomainToModelsHotelSummary(domainSummary *domain.HotelSummary) *models.HotelSummary {
	if domainSummary == nil {
		return nil
	}

	return &models.HotelSummary{
		ID:           domainSummary.ID,
		Name:         domainSummary.Name,
		NameEn:       domainSummary.NameEn,
		Location:     domainSummary.Location,
		Review:       convertDomainReviewToModel(domainSummary.Review),
		Amenities:    convertDomainAmenitiesToModel(domainSummary.Amenities),
		CategoryType: domainSummary.CategoryType,
		ThumbnailURL: convertDomainSummaryImagesToModel(domainSummary.ThumbnailURL),
		Rating:       domainSummary.Rating,
		Price:        convertDomainPriceToModel(domainSummary.Price),
		CenterInfo:   convertDomainCenterInfoToModel(domainSummary.CenterInfo),
		Available:    domainSummary.Available,
		RoomLeft:     domainSummary.RoomLeft,
		CountryCode:  domainSummary.CountryCode,
		Provider:     domainSummary.Provider,
		MatchKey:     domainSummary.MatchKey,
		CheckIn:      domainSummary.CheckIn,
		CheckOut:     domainSummary.CheckOut,
	}
}

func convertDomainReviewToModel(domainReview *domain.HotelReview) *models.HotelReview {
	if domainReview == nil {
		return nil
	}

	return &models.HotelReview{
		Rate:        domainReview.Rate,
		ReviewCount: domainReview.ReviewCount,
		Label:       domainReview.Label,
		Detail:      convertDomainReviewDetailToModel(domainReview.Detail),
	}
}

func convertDomainReviewDetailToModel(domainDetail *domain.HotelReviewDetail) *models.HotelReviewDetail {
	if domainDetail == nil {
		return nil
	}

	return &models.HotelReviewDetail{
		Reviews: convertDomainReviewsToModel(domainDetail.Reviews),
		Rating:  convertDomainCriteriaRatingToModel(domainDetail.Rating),
	}
}

func convertDomainReviewsToModel(domainReviews []*domain.Review) []*models.Review {
	var modelReviews []*models.Review
	for _, r := range domainReviews {
		modelReviews = append(modelReviews, &models.Review{
			ReviewID:     r.ReviewID,
			UserName:     r.UserName,
			UserType:     r.UserType,
			Date:         r.Date,
			Rating:       r.Rating,
			Title:        r.Title,
			Content:      r.Content,
			Liked:        r.Liked,
			StayDuration: r.StayDuration,
		})
	}

	return modelReviews
}

func convertDomainCriteriaRatingToModel(domainRating *domain.CriteriaRating) *models.CriteriaRating {
	if domainRating == nil {
		return nil
	}

	return &models.CriteriaRating{
		Cleanliness:                  domainRating.Cleanliness,
		StaffService:                 domainRating.StaffService,
		Amenities:                    domainRating.Amenities,
		PropertyConditionsFacilities: domainRating.PropertyConditionsFacilities,
		EcoFriendliness:              domainRating.EcoFriendliness,
	}
}

func convertDomainAmenitiesToModel(domainAmenities []*domain.Amenity) []*models.Amenity {
	var modelAmenities []*models.Amenity
	for _, a := range domainAmenities {
		modelAmenities = append(modelAmenities, &models.Amenity{
			ID:         a.ID,
			Name:       a.Name,
			Categories: a.Categories,
			Value:      a.Value,
			GroupName:  a.GroupName,
		})
	}

	return modelAmenities
}

func convertDomainImagesToModel(domainImages []*domain.Image) []*models.Image {
	var modelImages []*models.Image
	for _, img := range domainImages {
		modelImages = append(modelImages, &models.Image{
			HeroImage: img.HeroImage,
			Category:  img.Category,
			Links:     convertDomainLinksToModel(img.Links),
			Caption:   img.Caption,
		})
	}

	return modelImages
}

func convertDomainSummaryImagesToModel(domainImages []*domain.HotelSummaryImage) []*models.HotelSummaryImage {
	var modelImages []*models.HotelSummaryImage
	for _, img := range domainImages {
		modelImages = append(modelImages, &models.HotelSummaryImage{
			HeroImage: img.HeroImage,
			Category:  img.Category,
			Links: models.HotelSummaryImageLink{
				Px70:   img.Links.Px70,
				Px200:  img.Links.Px200,
				Px350:  img.Links.Px350,
				Px1000: img.Links.Px1000,
			},
			Caption: img.Caption,
		})
	}

	return modelImages
}

func convertDomainLinksToModel(modelLinks map[string]*domain.Link) map[string]*models.Link {
	domainLinks := make(map[string]*models.Link)
	for key, link := range modelLinks {
		domainLinks[key] = &models.Link{
			Method: link.Method,
			Href:   link.Href,
		}
	}

	return domainLinks
}

func convertDomainPriceToModel(domainPrice *domain.Price) *models.Price {
	if domainPrice == nil {
		return nil
	}

	return &models.Price{
		PricePerNight:   convertDomainPricePerNightToModel(domainPrice.PricePerNight),
		Total:           domainPrice.Total,
		IsIncludeTax:    domainPrice.IsIncludeTax,
		Currency:        domainPrice.Currency,
		SaleScenario:    domainPrice.SaleScenario,
		PayAtHotel:      convertDomainPayAtHotelToModel(domainPrice.PayAtHotel),
		TotalPayAtHotel: domainPrice.TotalPayAtHotel,
		HasBreakfast:    domainPrice.HasBreakfast,
		HasExtraBed:     domainPrice.HasExtraBed,
		NonSmoking:      domainPrice.NonSmoking,
		Refundable:      domainPrice.Refundable,
	}
}

func convertDomainPayAtHotelToModel(domainPayAtHotel []*domain.PayAtHotel) []*models.PayAtHotel {
	if len(domainPayAtHotel) == 0 {
		return nil
	}

	var modelPayAtHotel []*models.PayAtHotel
	for _, p := range domainPayAtHotel {
		modelPayAtHotel = append(modelPayAtHotel, &models.PayAtHotel{
			Amount:      p.Amount,
			Description: p.Description,
			Currency:    p.Currency,
		})
	}

	return modelPayAtHotel
}

func convertDomainPricePerNightToModel(domainPricePerNight *domain.PricePerNight) *models.PricePerNight {
	if domainPricePerNight == nil {
		return nil
	}

	return &models.PricePerNight{
		DiscountPrice: domainPricePerNight.DiscountPrice,
		OriginalPrice: domainPricePerNight.OriginalPrice,
	}
}

func convertDomainCenterInfoToModel(domainCenterInfo *domain.CenterInfo) *models.CenterInfo {
	if domainCenterInfo == nil {
		return nil
	}

	return &models.CenterInfo{
		CenterName:       domainCenterInfo.CenterName,
		DistanceToCenter: domainCenterInfo.DistanceToCenter,
		Unit:             domainCenterInfo.Unit,
	}
}
