package converts

import (
	"github.com/pkg/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ToDomainHubPromotion(modelPromotion *models.HubPromotion) *domain.HubPromotion {
	if modelPromotion == nil {
		return nil
	}

	return &domain.HubPromotion{
		Code:        modelPromotion.Code,
		Name:        modelPromotion.Name,
		Remark:      modelPromotion.Remark,
		OfferType:   modelPromotion.OfferType,
		Frequency:   modelPromotion.Frequency,
		PersonCount: modelPromotion.PersonCount,
	}
}

func ToDomainHubPromotions(modelPromotion []*models.HubPromotion) []*domain.HubPromotion {
	result := make([]*domain.HubPromotion, 0, len(modelPromotion))
	for _, item := range modelPromotion {
		result = append(result, ToDomainHubPromotion(item))
	}

	return result
}

func FromDomainHubPromotion(domainPromotion *domain.HubPromotion) *models.HubPromotion {
	if domainPromotion == nil {
		return nil
	}

	return &models.HubPromotion{
		Code:        domainPromotion.Code,
		Name:        domainPromotion.Name,
		Remark:      domainPromotion.Remark,
		OfferType:   domainPromotion.OfferType,
		Frequency:   domainPromotion.Frequency,
		PersonCount: domainPromotion.PersonCount,
	}
}

func FromDomainHubPromotions(domainPromotions []*domain.HubPromotion) []*models.HubPromotion {
	result := make([]*models.HubPromotion, 0, len(domainPromotions))
	for _, item := range domainPromotions {
		result = append(result, FromDomainHubPromotion(item))
	}

	return result
}

func ToDomainHubRateDiscount(modelRateDiscount *models.HubRateDiscount) *domain.HubRateDiscount {
	if modelRateDiscount == nil {
		return nil
	}

	return &domain.HubRateDiscount{
		Amount:   modelRateDiscount.Amount,
		Currency: modelRateDiscount.Currency,
		Code:     modelRateDiscount.Code,
		Name:     modelRateDiscount.Name,
	}
}

func ToDomainHubRateDiscounts(modelRateDiscount []*models.HubRateDiscount) []*domain.HubRateDiscount {
	result := make([]*domain.HubRateDiscount, 0, len(modelRateDiscount))
	for _, item := range modelRateDiscount {
		result = append(result, ToDomainHubRateDiscount(item))
	}

	return result
}

func FromDomainHubRateDiscount(domainRateDiscount *domain.HubRateDiscount) *models.HubRateDiscount {
	if domainRateDiscount == nil {
		return nil
	}

	return &models.HubRateDiscount{
		Amount:   domainRateDiscount.Amount,
		Code:     domainRateDiscount.Code,
		Name:     domainRateDiscount.Name,
		Currency: domainRateDiscount.Currency,
	}
}

func FromDomainHubRateDiscounts(domainRateDiscounts []*domain.HubRateDiscount) []*models.HubRateDiscount {
	result := make([]*models.HubRateDiscount, 0, len(domainRateDiscounts))
	for _, item := range domainRateDiscounts {
		result = append(result, FromDomainHubRateDiscount(item))
	}

	return result
}

func ToDomainHubRate(modelRate *models.HubRate) *domain.HubRate {
	if modelRate == nil {
		return nil
	}

	return &domain.HubRate{
		RateAmount: modelRate.RateAmount,
		RateBasic:  modelRate.RateBasic,
		TaxAmount:  modelRate.TaxAmount,
		Currency:   modelRate.Currency,
	}
}

func ToDomainHubRates(modelRate []*models.HubRate) []*domain.HubRate {
	result := make([]*domain.HubRate, 0, len(modelRate))
	for _, item := range modelRate {
		result = append(result, ToDomainHubRate(item))
	}

	return result
}

func FromDomainHubRate(domainRate *domain.HubRate) *models.HubRate {
	if domainRate == nil {
		return nil
	}

	return &models.HubRate{
		RateAmount: domainRate.RateAmount,
		RateBasic:  domainRate.RateBasic,
		TaxAmount:  domainRate.TaxAmount,
		Currency:   domainRate.Currency,
	}
}

func FromDomainHubRates(domainRate []*domain.HubRate) []*models.HubRate {
	result := make([]*models.HubRate, 0, len(domainRate))
	for _, item := range domainRate {
		result = append(result, FromDomainHubRate(item))
	}

	return result
}

func ToDomainHubRateSupplement(modelRateSupplement *models.HubRateSupplement) *domain.HubRateSupplement {
	if modelRateSupplement == nil {
		return nil
	}

	return &domain.HubRateSupplement{
		Amount:    modelRateSupplement.Amount,
		Code:      modelRateSupplement.Code,
		From:      modelRateSupplement.From,
		Name:      modelRateSupplement.Name,
		Nights:    modelRateSupplement.Nights,
		PaxNumber: modelRateSupplement.PaxNumber,
		To:        modelRateSupplement.To,
	}
}

func FromDomainHubRateSupplement(domainRateSupplement *domain.HubRateSupplement) *models.HubRateSupplement {
	if domainRateSupplement == nil {
		return nil
	}

	return &models.HubRateSupplement{
		Amount:    domainRateSupplement.Amount,
		Code:      domainRateSupplement.Code,
		From:      domainRateSupplement.From,
		Name:      domainRateSupplement.Name,
		Nights:    domainRateSupplement.Nights,
		PaxNumber: domainRateSupplement.PaxNumber,
		To:        domainRateSupplement.To,
	}
}

func ToDomainHubRateSupplements(modelRateSupplements []*models.HubRateSupplement) []*domain.HubRateSupplement {
	result := make([]*domain.HubRateSupplement, 0, len(modelRateSupplements))
	for _, item := range modelRateSupplements {
		result = append(result, ToDomainHubRateSupplement(item))
	}

	return result
}

func FromDomainHubRateSupplements(domainRateSupplements []*domain.HubRateSupplement) []*models.HubRateSupplement {
	result := make([]*models.HubRateSupplement, 0, len(domainRateSupplements))
	for _, item := range domainRateSupplements {
		result = append(result, FromDomainHubRateSupplement(item))
	}

	return result
}

func ToDomainHubRateTax(modelRateTax *models.HubRateTax) *domain.HubRateTax {
	if modelRateTax == nil {
		return nil
	}

	return &domain.HubRateTax{
		Amount:      modelRateTax.Amount,
		Currency:    modelRateTax.Currency,
		Included:    modelRateTax.Included,
		Type:        modelRateTax.Type,
		Description: modelRateTax.Description,
	}
}

func FromDomainHubRateTax(domainRateTax *domain.HubRateTax) *models.HubRateTax {
	if domainRateTax == nil {
		return nil
	}

	return &models.HubRateTax{
		Amount:      domainRateTax.Amount,
		Currency:    domainRateTax.Currency,
		Included:    domainRateTax.Included,
		Type:        domainRateTax.Type,
		Description: domainRateTax.Description,
	}
}

func ToDomainHubRateTaxes(modelRateTaxes []*models.HubRateTax) []*domain.HubRateTax {
	result := make([]*domain.HubRateTax, 0, len(modelRateTaxes))
	for _, item := range modelRateTaxes {
		result = append(result, ToDomainHubRateTax(item))
	}

	return result
}

func FromDomainHubRateTaxes(domainRateTaxes []*domain.HubRateTax) []*models.HubRateTax {
	result := make([]*models.HubRateTax, 0, len(domainRateTaxes))
	for _, item := range domainRateTaxes {
		result = append(result, FromDomainHubRateTax(item))
	}

	return result
}

func ToDomainHubAmenity(modelAmenity *models.HubAmenity) *domain.HubAmenity {
	if modelAmenity == nil {
		return nil
	}

	return &domain.HubAmenity{
		ID:   modelAmenity.ID,
		Name: modelAmenity.Name,
	}
}

func FromDomainHubAmenity(domainAmenity *domain.HubAmenity) *models.HubAmenity {
	if domainAmenity == nil {
		return nil
	}

	return &models.HubAmenity{
		ID:   domainAmenity.ID,
		Name: domainAmenity.Name,
	}
}

func ToDomainHubAmenities(modelAmenities []*models.HubAmenity) []*domain.HubAmenity {
	result := make([]*domain.HubAmenity, 0, len(modelAmenities))
	for _, item := range modelAmenities {
		result = append(result, ToDomainHubAmenity(item))
	}

	return result
}

func FromDomainHubAmenities(domainAmenities []*domain.HubAmenity) []*models.HubAmenity {
	result := make([]*models.HubAmenity, 0, len(domainAmenities))
	for _, item := range domainAmenities {
		result = append(result, FromDomainHubAmenity(item))
	}

	return result
}

func ToDomainPayAtHotels(modelPayAtHotel []*models.PayAtHotel) []*domain.PayAtHotel {
	if len(modelPayAtHotel) == 0 {
		return nil
	}
	result := make([]*domain.PayAtHotel, 0, len(modelPayAtHotel))

	for _, item := range modelPayAtHotel {
		result = append(result, &domain.PayAtHotel{
			Amount:      item.Amount,
			Description: item.Description,
			Currency:    item.Currency,
		})
	}

	return result
}

func FromDomainPayAtHotels(domainPayAtHotel []*domain.PayAtHotel) []*models.PayAtHotel {
	if len(domainPayAtHotel) == 0 {
		return nil
	}
	result := make([]*models.PayAtHotel, 0, len(domainPayAtHotel))

	for _, item := range domainPayAtHotel {
		result = append(result, &models.PayAtHotel{
			Amount:      item.Amount,
			Description: item.Description,
			Currency:    item.Currency,
		})
	}

	return result
}

func ToDomainHubOccupancyRate(modelHubOccupancyRate *models.HubOccupancyRate) *domain.HubOccupancyRate {
	if modelHubOccupancyRate == nil {
		return nil
	}

	return &domain.HubOccupancyRate{
		OccupancyType:    modelHubOccupancyRate.OccupancyType,
		RoomQuantity:     modelHubOccupancyRate.RoomQuantity,
		TotalNightlyRate: ToDomainHubRates(modelHubOccupancyRate.TotalNightlyRate),
		RateTaxes:        ToDomainHubRateTaxes(modelHubOccupancyRate.RateTaxes),
		PayAtHotel:       ToDomainPayAtHotels(modelHubOccupancyRate.PayAtHotel),
		Surcharges:       modelHubOccupancyRate.Surcharges,
		RateDiscounts:    ToDomainHubRateDiscounts(modelHubOccupancyRate.RateDiscounts),
		MarketingFee:     modelHubOccupancyRate.MarketingFee,
	}
}

func FromDomainHubOccupancyRate(domainHubOccupancyRate *domain.HubOccupancyRate) *models.HubOccupancyRate {
	if domainHubOccupancyRate == nil {
		return nil
	}

	return &models.HubOccupancyRate{
		OccupancyType:    domainHubOccupancyRate.OccupancyType,
		RoomQuantity:     domainHubOccupancyRate.RoomQuantity,
		TotalNightlyRate: FromDomainHubRates(domainHubOccupancyRate.TotalNightlyRate),
		RateTaxes:        FromDomainHubRateTaxes(domainHubOccupancyRate.RateTaxes),
		PayAtHotel:       FromDomainPayAtHotels(domainHubOccupancyRate.PayAtHotel),
		Surcharges:       domainHubOccupancyRate.Surcharges,
		RateDiscounts:    FromDomainHubRateDiscounts(domainHubOccupancyRate.RateDiscounts),
		MarketingFee:     domainHubOccupancyRate.MarketingFee,
	}
}

func ToDomainHubOccupancyRates(modelHubOccupancyRates []*models.HubOccupancyRate) []*domain.HubOccupancyRate {
	result := make([]*domain.HubOccupancyRate, 0, len(modelHubOccupancyRates))
	for _, item := range modelHubOccupancyRates {
		result = append(result, ToDomainHubOccupancyRate(item))
	}

	return result
}

func FromDomainHubOccupancyRates(domainHubOccupancyRates []*domain.HubOccupancyRate) []*models.HubOccupancyRate {
	result := make([]*models.HubOccupancyRate, 0, len(domainHubOccupancyRates))
	for _, item := range domainHubOccupancyRates {
		result = append(result, FromDomainHubOccupancyRate(item))
	}

	return result
}

func ToDomainHubBedOption(modelBedOption *models.BedOption) *domain.BedOption {
	if modelBedOption == nil {
		return nil
	}

	return &domain.BedOption{
		OptionID:        modelBedOption.OptionID,
		Name:            modelBedOption.Name,
		Quantity:        modelBedOption.Quantity,
		PriceCheckToken: modelBedOption.PriceCheckToken,
		BedConfigs:      ToDomainBedConfigurations(modelBedOption.BedConfigs),
	}
}

func FromDomainHubBedOption(domainBedOption *domain.BedOption) *models.BedOption {
	if domainBedOption == nil {
		return nil
	}

	return &models.BedOption{
		OptionID:        domainBedOption.OptionID,
		Name:            domainBedOption.Name,
		Quantity:        domainBedOption.Quantity,
		PriceCheckToken: domainBedOption.PriceCheckToken,
		BedConfigs:      FromDomainBedConfigurations(domainBedOption.BedConfigs),
	}
}

func ToDomainHubBedOptions(modelBedOption []*models.BedOption) []*domain.BedOption {
	result := make([]*domain.BedOption, 0, len(modelBedOption))
	for _, item := range modelBedOption {
		result = append(result, ToDomainHubBedOption(item))
	}

	return result
}

func FromDomainHubBedOptions(domainBedOption []*domain.BedOption) []*models.BedOption {
	result := make([]*models.BedOption, 0, len(domainBedOption))
	for _, item := range domainBedOption {
		result = append(result, FromDomainHubBedOption(item))
	}

	return result
}

func FromDomainHubRateData(domainHubRateData *domain.HubRateData) *models.HubRateData {
	if domainHubRateData == nil {
		return nil
	}

	return &models.HubRateData{
		RateID:                 domainHubRateData.RateID,
		ProviderRateID:         domainHubRateData.ProviderRateID,
		Available:              domainHubRateData.Available,
		CancelPolicies:         FromDomainHubCancelPolicies(domainHubRateData.CancelPolicies),
		Commission:             domainHubRateData.Commission,
		CommissionPCT:          domainHubRateData.CommissionPCT,
		CommissionVAT:          domainHubRateData.CommissionVAT,
		Promotions:             FromDomainHubPromotions(domainHubRateData.Promotions),
		OccupancyRate:          FromDomainHubOccupancyRates(domainHubRateData.OccupancyRate),
		Currency:               domainHubRateData.Currency,
		TotalRateAmount:        domainHubRateData.TotalRateAmount,
		TotalRateBasic:         domainHubRateData.TotalRateBasic,
		TotalTaxAmount:         domainHubRateData.TotalTaxAmount,
		PayNow:                 domainHubRateData.PayNow,
		TotalPayAtHotel:        domainHubRateData.TotalPayAtHotel,
		PayAtHotelCurrency:     domainHubRateData.PayAtHotelCurrency,
		Amenities:              FromDomainHubAmenities(domainHubRateData.Amenities),
		Refundable:             domainHubRateData.Refundable,
		BedOptions:             FromDomainHubBedOptions(domainHubRateData.BedOptions),
		SaleScenario:           domainHubRateData.SaleScenario,
		HasBreakfast:           domainHubRateData.HasBreakfast,
		HasExtraBed:            domainHubRateData.HasExtraBed,
		NonSmoking:             domainHubRateData.NonSmoking,
		HiddenFeeAmount:        domainHubRateData.HiddenFeeAmount,
		DiscountAmount:         domainHubRateData.DiscountAmount,
		AppliedHiddenFee:       domainHubRateData.AppliedHiddenFee,
		AppliedDiscount:        domainHubRateData.AppliedDiscount,
		NonrefundableDateRange: FromDomainHubNonrefundableDateRanges(domainHubRateData.NonrefundableDateRange),
		FakeNonRefund:          domainHubRateData.FakeNonRefund,
		IsSoldOut:              domainHubRateData.IsSoldOut,
		RouteRateID:            domainHubRateData.RouteRateID,
		AdditionalData:         FromDomainAdditionalData(domainHubRateData.AdditionalData),
	}
}

func ToDomainHubRateData(modelHubRateData *models.HubRateData) *domain.HubRateData {
	if modelHubRateData == nil {
		return nil
	}

	return &domain.HubRateData{
		RateID:                 modelHubRateData.RateID,
		ProviderRateID:         modelHubRateData.ProviderRateID,
		Available:              modelHubRateData.Available,
		CancelPolicies:         ToDomainHubCancelPolicies(modelHubRateData.CancelPolicies),
		Commission:             modelHubRateData.Commission,
		CommissionPCT:          modelHubRateData.CommissionPCT,
		CommissionVAT:          modelHubRateData.CommissionVAT,
		Promotions:             ToDomainHubPromotions(modelHubRateData.Promotions),
		OccupancyRate:          ToDomainHubOccupancyRates(modelHubRateData.OccupancyRate),
		Currency:               modelHubRateData.Currency,
		TotalRateAmount:        modelHubRateData.TotalRateAmount,
		TotalRateBasic:         modelHubRateData.TotalRateBasic,
		TotalTaxAmount:         modelHubRateData.TotalTaxAmount,
		PayNow:                 modelHubRateData.PayNow,
		TotalPayAtHotel:        modelHubRateData.TotalPayAtHotel,
		PayAtHotelCurrency:     modelHubRateData.PayAtHotelCurrency,
		BedOptions:             ToDomainHubBedOptions(modelHubRateData.BedOptions),
		Amenities:              ToDomainHubAmenities(modelHubRateData.Amenities),
		Refundable:             modelHubRateData.Refundable,
		SaleScenario:           modelHubRateData.SaleScenario,
		HasBreakfast:           modelHubRateData.HasBreakfast,
		HasExtraBed:            modelHubRateData.HasExtraBed,
		NonSmoking:             modelHubRateData.NonSmoking,
		HiddenFeeAmount:        modelHubRateData.HiddenFeeAmount,
		DiscountAmount:         modelHubRateData.DiscountAmount,
		AppliedHiddenFee:       modelHubRateData.AppliedHiddenFee,
		AppliedDiscount:        modelHubRateData.AppliedDiscount,
		NonrefundableDateRange: ToDomainHubNonrefundableDateRanges(modelHubRateData.NonrefundableDateRange),
		FakeNonRefund:          modelHubRateData.FakeNonRefund,
		IsSoldOut:              modelHubRateData.IsSoldOut,
		RouteRateID:            modelHubRateData.RouteRateID,
		AdditionalData:         ToDomainAdditionalData(modelHubRateData.AdditionalData),
	}
}

func ToDomainHubRateDatas(modelHubRateData []*models.HubRateData) []*domain.HubRateData {
	result := make([]*domain.HubRateData, 0, len(modelHubRateData))
	for _, item := range modelHubRateData {
		result = append(result, ToDomainHubRateData(item))
	}

	return result
}

func FromDomainHubRateDatas(domainHubRateData []*domain.HubRateData) []*models.HubRateData {
	result := make([]*models.HubRateData, 0, len(domainHubRateData))
	for _, item := range domainHubRateData {
		result = append(result, FromDomainHubRateData(item))
	}

	return result
}

func ToDomainHubNonrefundableDateRange(modelNonrefundableDateRange *models.HubNonrefundableDateRange) *domain.HubNonrefundableDateRange {
	if modelNonrefundableDateRange == nil {
		return nil
	}

	return &domain.HubNonrefundableDateRange{
		StartDate: modelNonrefundableDateRange.StartDate,
		EndDate:   modelNonrefundableDateRange.EndDate,
	}
}

func FromDomainHubNonrefundableDateRange(domainNonrefundableDateRange *domain.HubNonrefundableDateRange) *models.HubNonrefundableDateRange {
	if domainNonrefundableDateRange == nil {
		return nil
	}

	return &models.HubNonrefundableDateRange{
		StartDate: domainNonrefundableDateRange.StartDate,
		EndDate:   domainNonrefundableDateRange.EndDate,
	}
}

func ToDomainHubNonrefundableDateRanges(modelNonrefundableDateRanges []*models.HubNonrefundableDateRange) []*domain.HubNonrefundableDateRange {
	result := make([]*domain.HubNonrefundableDateRange, 0, len(modelNonrefundableDateRanges))
	for _, item := range modelNonrefundableDateRanges {
		result = append(result, ToDomainHubNonrefundableDateRange(item))
	}

	return result
}

func FromDomainHubNonrefundableDateRanges(domainNonrefundableDateRanges []*domain.HubNonrefundableDateRange) []*models.HubNonrefundableDateRange {
	result := make([]*models.HubNonrefundableDateRange, 0, len(domainNonrefundableDateRanges))
	for _, item := range domainNonrefundableDateRanges {
		result = append(result, FromDomainHubNonrefundableDateRange(item))
	}

	return result
}

func ToDomainHubCancelPolicy(modelCancelPolicy *models.HubCancelPolicy) *domain.HubCancelPolicy {
	return &domain.HubCancelPolicy{
		StartDate:              modelCancelPolicy.StartDate,
		EndDate:                modelCancelPolicy.EndDate,
		Currency:               modelCancelPolicy.Currency,
		NonrefundableDateRange: ToDomainHubNonrefundableDateRanges(modelCancelPolicy.NonrefundableDateRange),
		PenaltyInfo: domain.HubCancelPolicyPenaltyInfo{
			Amount:         modelCancelPolicy.PenaltyInfo.Amount,
			Percent:        modelCancelPolicy.PenaltyInfo.Percent,
			NumberOfNights: modelCancelPolicy.PenaltyInfo.NumberOfNights,
		},
		Refundable:       modelCancelPolicy.Refundable,
		PartialRefund:    modelCancelPolicy.PartialRefund,
		RawPenaltyAmount: modelCancelPolicy.RawPenaltyAmount,
		PenaltyAmount:    modelCancelPolicy.PenaltyAmount,
	}
}

func FromDomainHubCancelPolicy(domainCancelPolicy *domain.HubCancelPolicy) *models.HubCancelPolicy {
	return &models.HubCancelPolicy{
		StartDate:              domainCancelPolicy.StartDate,
		EndDate:                domainCancelPolicy.EndDate,
		Currency:               domainCancelPolicy.Currency,
		NonrefundableDateRange: FromDomainHubNonrefundableDateRanges(domainCancelPolicy.NonrefundableDateRange),
		PenaltyInfo: models.HubCancelPolicyPenaltyInfo{
			Amount:         domainCancelPolicy.PenaltyInfo.Amount,
			Percent:        domainCancelPolicy.PenaltyInfo.Percent,
			NumberOfNights: domainCancelPolicy.PenaltyInfo.NumberOfNights,
		},
		Refundable:       domainCancelPolicy.Refundable,
		PartialRefund:    domainCancelPolicy.PartialRefund,
		RawPenaltyAmount: domainCancelPolicy.RawPenaltyAmount,
		PenaltyAmount:    domainCancelPolicy.PenaltyAmount,
	}
}

func ToDomainHubCancelPolicies(modelCancelPolicies []*models.HubCancelPolicy) []*domain.HubCancelPolicy {
	result := make([]*domain.HubCancelPolicy, 0, len(modelCancelPolicies))
	for _, item := range modelCancelPolicies {
		result = append(result, ToDomainHubCancelPolicy(item))
	}

	return result
}

func FromDomainHubCancelPolicies(domainCancelPolicies []*domain.HubCancelPolicy) []*models.HubCancelPolicy {
	result := make([]*models.HubCancelPolicy, 0, len(domainCancelPolicies))
	for _, item := range domainCancelPolicies {
		result = append(result, FromDomainHubCancelPolicy(item))
	}

	return result
}

func ToDomainHubRoom(modelHubRoom *models.HubRoom) *domain.HubRoom {
	if modelHubRoom == nil {
		return nil
	}

	return &domain.HubRoom{
		RoomID:            modelHubRoom.RoomID,
		Name:              modelHubRoom.Name,
		NameEn:            modelHubRoom.NameEn,
		Provider:          modelHubRoom.Provider,
		RateData:          ToDomainHubRateDatas(modelHubRoom.RateData),
		ExchangedRateData: ToDomainHubRateDatas(modelHubRoom.ExchangedRateData),
		ProviderRoomID:    modelHubRoom.ProviderRoomID,
		Descriptions:      ToDomainRoomDescriptions(modelHubRoom.Descriptions),
		Amenities:         ToDomainAmenities(modelHubRoom.Amenities),
		Images:            ToDomainImages(modelHubRoom.Images),
		BedGroups:         ToDomainBedGroups(modelHubRoom.BedGroups),
		Area:              ToDomainArea(modelHubRoom.Area),
		Occupancy:         ToDomainOccupancy(modelHubRoom.Occupancy),
		Views:             ToDomainViews(modelHubRoom.Views),
		OldProviderRoomID: modelHubRoom.OldProviderRoomID,
	}
}

func ToDomainAdditionalData(in *models.AdditionalData) *domain.AdditionalData {
	if in == nil {
		return nil
	}

	return &domain.AdditionalData{
		AgodaCheckAvaibilityInfo: ToDomainAgodaCheckAvailabilityInfo(in.AgodaCheckAvaibilityInfo),
	}
}

func ToDomainAgodaCheckAvailabilityInfo(in *models.AgodaCheckAvaibilityInfo) *domain.AgodaCheckAvaibilityInfo {
	if in == nil {
		return nil
	}

	return &domain.AgodaCheckAvaibilityInfo{
		Rate:         ToDomainRate(in.Rate),
		Surcharges:   ToDomainSurcharges(in.Surcharges),
		PaymentModel: in.PaymentModel,
	}
}

func ToDomainRate(in *models.AgodaRate) *domain.AgodaRate {
	if in == nil {
		return nil
	}

	return &domain.AgodaRate{
		Inclusive: in.Inclusive,
	}
}

func ToDomainSurcharges(in []*models.Surcharge) []*domain.Surcharge {
	result := make([]*domain.Surcharge, 0, len(in))

	for _, i := range in {
		result = append(result, ToDomainSurcharge(i))
	}

	return result
}

func ToDomainSurcharge(in *models.Surcharge) *domain.Surcharge {
	if in == nil {
		return nil
	}

	return &domain.Surcharge{
		ID:   in.ID,
		Rate: ToDomainRate(in.Rate),
	}
}

func FromDomainHubHubRoom(domainHubRoom *domain.HubRoom) *models.HubRoom {
	if domainHubRoom == nil {
		return nil
	}

	return &models.HubRoom{
		RoomID:            domainHubRoom.RoomID,
		Provider:          domainHubRoom.Provider,
		Name:              domainHubRoom.Name,
		NameEn:            domainHubRoom.NameEn,
		RateData:          FromDomainHubRateDatas(domainHubRoom.RateData),
		ExchangedRateData: FromDomainHubRateDatas(domainHubRoom.ExchangedRateData),
		ProviderRoomID:    domainHubRoom.ProviderRoomID,
		Descriptions:      FromDomainRoomDescriptions(domainHubRoom.Descriptions),
		Amenities:         FromDomainAmenities(domainHubRoom.Amenities),
		Images:            FromDomainImages(domainHubRoom.Images),
		BedGroups:         FromDomainBedGroups(domainHubRoom.BedGroups),
		Area:              FromDomainArea(domainHubRoom.Area),
		Occupancy:         FromDomainOccupancy(domainHubRoom.Occupancy),
		Views:             FromDomainViews(domainHubRoom.Views),
		OldProviderRoomID: domainHubRoom.OldProviderRoomID,
	}
}

func FromDomainAdditionalData(in *domain.AdditionalData) *models.AdditionalData {
	if in == nil {
		return nil
	}

	return &models.AdditionalData{
		AgodaCheckAvaibilityInfo: FromDomainAgodaCheckAvailabilityInfo(in.AgodaCheckAvaibilityInfo),
	}
}

func FromDomainAgodaCheckAvailabilityInfo(in *domain.AgodaCheckAvaibilityInfo) *models.AgodaCheckAvaibilityInfo {
	if in == nil {
		return nil
	}

	return &models.AgodaCheckAvaibilityInfo{
		Rate:         FromDomainRate(in.Rate),
		Surcharges:   FromDomainSurchages(in.Surcharges),
		PaymentModel: in.PaymentModel,
	}
}

func FromDomainRate(in *domain.AgodaRate) *models.AgodaRate {
	if in == nil {
		return nil
	}

	return &models.AgodaRate{
		Inclusive: in.Inclusive,
	}
}

func FromDomainSurchages(in []*domain.Surcharge) []*models.Surcharge {
	result := make([]*models.Surcharge, 0, len(in))

	for _, i := range in {
		result = append(result, FromDomainSurchage(i))
	}

	return result
}

func FromDomainSurchage(in *domain.Surcharge) *models.Surcharge {
	if in == nil {
		return nil
	}

	return &models.Surcharge{
		ID:   in.ID,
		Rate: FromDomainRate(in.Rate),
	}
}

func ToDomainHubRooms(modelHubRooms []*models.HubRoom) []*domain.HubRoom {
	result := make([]*domain.HubRoom, 0, len(modelHubRooms))
	for _, item := range modelHubRooms {
		result = append(result, ToDomainHubRoom(item))
	}

	return result
}

func FromDomainHubHubRooms(domainHubRooms []*domain.HubRoom) []*models.HubRoom {
	result := make([]*models.HubRoom, 0, len(domainHubRooms))
	for _, item := range domainHubRooms {
		result = append(result, FromDomainHubHubRoom(item))
	}

	return result
}

func ToDomainHubHotelReview(modelHotelReview *models.HubHotelReview) *domain.HubHotelReview {
	if modelHotelReview == nil {
		return nil
	}

	return &domain.HubHotelReview{
		Rate:        modelHotelReview.Rate,
		ReviewCount: modelHotelReview.ReviewCount,
	}
}

func FromDomainHubHotelReview(domainHotelReview *domain.HubHotelReview) *models.HubHotelReview {
	if domainHotelReview == nil {
		return nil
	}

	return &models.HubHotelReview{
		Rate:        domainHotelReview.Rate,
		ReviewCount: domainHotelReview.ReviewCount,
	}
}

func ToDomainHubHotelReviews(modelHotelReviews []*models.HubHotelReview) []*domain.HubHotelReview {
	result := make([]*domain.HubHotelReview, 0, len(modelHotelReviews))
	for _, item := range modelHotelReviews {
		result = append(result, ToDomainHubHotelReview(item))
	}

	return result
}

func FromDomainHubHotelReviews(domainHotelReviews []*domain.HubHotelReview) []*models.HubHotelReview {
	result := make([]*models.HubHotelReview, 0, len(domainHotelReviews))
	for _, item := range domainHotelReviews {
		result = append(result, FromDomainHubHotelReview(item))
	}

	return result
}

func FromDomainHubHotel(domainHubHotel *domain.HubHotel) *models.HubHotel {
	if domainHubHotel == nil {
		return nil
	}

	return &models.HubHotel{
		Address:               FromDomainAddress(domainHubHotel.Address),
		HotelID:               domainHubHotel.HotelID,
		ProviderHotelID:       domainHubHotel.ProviderHotelID,
		OldProviderID:         domainHubHotel.OldProviderID,
		Name:                  domainHubHotel.Name,
		NameEn:                domainHubHotel.NameEn,
		VAT:                   domainHubHotel.VAT,
		ListRooms:             FromDomainHubHubRooms(domainHubHotel.ListRooms),
		Currency:              domainHubHotel.Currency,
		Rating:                domainHubHotel.Rating,
		HotelType:             domainHubHotel.HotelType,
		CheckInTime:           domainHubHotel.CheckInTime,
		CheckOutTime:          domainHubHotel.CheckOutTime,
		ApplicableNationality: FromDomainApplicableNationalities(domainHubHotel.ApplicableNationality),
	}
}

func FromDomainApplicableNationalities(in []*domain.ApplicableNationality) []*models.ApplicableNationality {
	result := make([]*models.ApplicableNationality, 0, len(in))
	for _, item := range in {
		result = append(result, FromDomainApplicableNationality(item))
	}

	return result
}

func FromDomainApplicableNationality(in *domain.ApplicableNationality) *models.ApplicableNationality {
	if in == nil {
		return nil
	}

	return &models.ApplicableNationality{
		ID:               in.ID,
		Name:             in.Name,
		Excluded:         in.Excluded,
		Nationalities:    in.Nationalities,
		NationalityPrice: FromDomainNationalityPrice(in.NationalityPrice),
	}
}

func FromDomainNationalityPrice(in *domain.NationalityPrice) *models.NationalityPrice {
	if in == nil {
		return nil
	}

	return &models.NationalityPrice{
		TotalRate:        in.TotalRate,
		ExtraBed:         in.ExtraBed,
		RateExtraConfigs: FromDomainRateExtraConfigs(in.RateExtraConfigs),
	}
}

func FromDomainRateExtraConfigs(in []domain.RateExtraConfig) []models.RateExtraConfig {
	result := make([]models.RateExtraConfig, 0, len(in))
	for _, item := range in {
		result = append(result, FromDomainRateExtraConfig(item))
	}

	return result
}

func FromDomainRateExtraConfig(in domain.RateExtraConfig) models.RateExtraConfig {
	return models.RateExtraConfig{
		DayOfWeek:      in.DayOfWeek,
		Dates:          in.Dates,
		TotalRateExtra: in.TotalRateExtra,
	}
}

func ToDomainHubHubHotel(modelHubHotel *models.HubHotel) *domain.HubHotel {
	if modelHubHotel == nil {
		return nil
	}

	return &domain.HubHotel{
		Address:               ToDomainAddress(modelHubHotel.Address),
		HotelID:               modelHubHotel.HotelID,
		ProviderHotelID:       modelHubHotel.ProviderHotelID,
		OldProviderID:         modelHubHotel.OldProviderID,
		Name:                  modelHubHotel.Name,
		NameEn:                modelHubHotel.NameEn,
		VAT:                   modelHubHotel.VAT,
		ListRooms:             ToDomainHubRooms(modelHubHotel.ListRooms),
		Currency:              modelHubHotel.Currency,
		Rating:                modelHubHotel.Rating,
		HotelType:             modelHubHotel.HotelType,
		CheckInTime:           modelHubHotel.CheckInTime,
		CheckOutTime:          modelHubHotel.CheckOutTime,
		ApplicableNationality: ToDomainApplicableNationalities(modelHubHotel.ApplicableNationality),
	}
}

func ToDomainApplicableNationalities(in []*models.ApplicableNationality) []*domain.ApplicableNationality {
	result := make([]*domain.ApplicableNationality, 0, len(in))
	for _, item := range in {
		result = append(result, ToDomainApplicableNationality(item))
	}

	return result
}

func ToDomainApplicableNationality(in *models.ApplicableNationality) *domain.ApplicableNationality {
	if in == nil {
		return nil
	}

	return &domain.ApplicableNationality{
		ID:               in.ID,
		Name:             in.Name,
		Excluded:         in.Excluded,
		Nationalities:    in.Nationalities,
		NationalityPrice: ToDomainNationalityPrice(in.NationalityPrice),
	}
}

func ToDomainNationalityPrice(in *models.NationalityPrice) *domain.NationalityPrice {
	if in == nil {
		return nil
	}

	return &domain.NationalityPrice{
		TotalRate:        in.TotalRate,
		ExtraBed:         in.ExtraBed,
		RateExtraConfigs: ToDomainRateExtraConfigs(in.RateExtraConfigs),
	}
}

func ToDomainRateExtraConfigs(in []models.RateExtraConfig) []domain.RateExtraConfig {
	result := make([]domain.RateExtraConfig, 0, len(in))
	for _, item := range in {
		result = append(result, ToDomainRateExtraConfig(item))
	}

	return result
}

func ToDomainRateExtraConfig(in models.RateExtraConfig) domain.RateExtraConfig {
	return domain.RateExtraConfig{
		DayOfWeek:      in.DayOfWeek,
		Dates:          in.Dates,
		TotalRateExtra: in.TotalRateExtra,
	}
}

func FromDomainHubHubHotels(domainHubHotels []*domain.HubHotel) []*models.HubHotel {
	result := make([]*models.HubHotel, 0, len(domainHubHotels))
	for _, item := range domainHubHotels {
		result = append(result, FromDomainHubHotel(item))
	}

	return result
}

func ToDomainHubHubHotels(modelHubHotels []*models.HubHotel) []*domain.HubHotel {
	result := make([]*domain.HubHotel, 0, len(modelHubHotels))
	for _, item := range modelHubHotels {
		result = append(result, ToDomainHubHubHotel(item))
	}

	return result
}

func ToDomainHubSearchOccupancies(modelOccupancies []*models.HubSearchOccupancy) []*domain.HubSearchOccupancy {
	domainOccupancies := []*domain.HubSearchOccupancy{}
	if len(modelOccupancies) == 0 {
		return domainOccupancies
	}

	for _, item := range modelOccupancies {
		if item == nil {
			continue
		}

		tempOccupancy := &domain.HubSearchOccupancy{
			OccupancyIndex: item.OccupancyIndex,
			Adults:         item.Adults,
			Rooms:          item.Rooms,
		}

		if item.Children != nil {
			tempOccupancy.Children = &domain.HubSearchChildren{
				Number: item.Children.Number,
				Age:    item.Children.Age,
			}
		}

		domainOccupancies = append(domainOccupancies, tempOccupancy)
	}

	return domainOccupancies
}

func FromDomainHubSearchOccupancies(ins []*domain.HubSearchOccupancy) []*models.HubSearchOccupancy {
	if ins == nil {
		return nil
	}

	out := []*models.HubSearchOccupancy{}

	for _, item := range ins {
		if item == nil {
			continue
		}

		out = append(out, &models.HubSearchOccupancy{
			OccupancyIndex: item.OccupancyIndex,
			Adults:         item.Adults,
			Rooms:          item.Rooms,
			Children: func() *models.HubSearchChildren {
				if item.Children != nil {
					return &models.HubSearchChildren{
						Number: item.Children.Number,
						Age:    item.Children.Age,
					}
				}
				return nil
			}(),
		})
	}

	return out
}

func FromDomainHubSearchResult(domainSearchResult *domain.HotelSearchResult) (*models.HotelSearchResult, error) {
	objID, err := primitive.ObjectIDFromHex(domainSearchResult.ID)
	if err != nil {
		return nil, errors.Wrap(err, "id ObjectIDFromHex")
	}

	return &models.HotelSearchResult{
		Base:               models.Base{ID: objID, CreatedAt: domainSearchResult.CreatedAt},
		SearchKey:          domainSearchResult.SearchKey,
		Provider:           domainSearchResult.Provider,
		SaleScenario:       domainSearchResult.SaleScenario,
		SaleEnv:            domainSearchResult.SaleEnv,
		ExpiredAt:          domainSearchResult.ExpireAt,
		HotelSearchRequest: FromDomainHotelSearchRequest(domainSearchResult.HotelSearchRequest),
		Hotels:             FromDomainHubHubHotels(domainSearchResult.Hotels),
		AgodaSearchID:      domainSearchResult.AgodaSearchID,
	}, nil
}

func FromDomainHotelSearchRequest(input *domain.CacheCheckAvailabilityRequest) *models.HotelSearchRequest {
	if input == nil {
		return nil
	}

	return &models.HotelSearchRequest{
		Stay: models.HubSearchStay{
			CheckIn:   input.Stay.CheckIn,
			CheckOut:  input.Stay.CheckOut,
			DayCount:  input.Stay.DayCount,
			RoomCount: input.Stay.RoomCount,
		},
		Occupancies:         FromDomainHubSearchOccupancies(input.Occupancies),
		CountryCode:         input.CountryCode,
		Language:            input.Language,
		Currency:            input.Currency,
		EndUserIPAddress:    input.EndUserIPAddress,
		EndUserBrowserAgent: input.EndUserBrowserAgent,
		DefaultLanguage:     input.DefaultLanguage,
		DetailRate:          input.DetailRate,
	}
}

func ToDomainHubSearchResult(modelSearchResult *models.HotelSearchResult) *domain.HotelSearchResult {
	return &domain.HotelSearchResult{
		ID:                 modelSearchResult.ID.Hex(),
		CreatedAt:          modelSearchResult.CreatedAt,
		SearchKey:          modelSearchResult.SearchKey,
		Provider:           modelSearchResult.Provider,
		ExpireAt:           modelSearchResult.ExpiredAt,
		Hotels:             ToDomainHubHubHotels(modelSearchResult.Hotels),
		HotelSearchRequest: ToDomainHotelSearchRequest(modelSearchResult.HotelSearchRequest),
		SaleScenario:       modelSearchResult.SaleScenario,
		SaleEnv:            modelSearchResult.SaleEnv,
		AgodaSearchID:      modelSearchResult.AgodaSearchID,
	}
}

func ToDomainHotelSearchRequest(input *models.HotelSearchRequest) *domain.CacheCheckAvailabilityRequest {
	if input == nil {
		return nil
	}

	return &domain.CacheCheckAvailabilityRequest{
		Stay: domain.HubSearchStay{
			CheckIn:   input.Stay.CheckIn,
			CheckOut:  input.Stay.CheckOut,
			DayCount:  input.Stay.DayCount,
			RoomCount: input.Stay.RoomCount,
		},
		Occupancies:         ToDomainHubSearchOccupancies(input.Occupancies),
		CountryCode:         input.CountryCode,
		Language:            input.Language,
		Currency:            input.Currency,
		EndUserIPAddress:    input.EndUserIPAddress,
		EndUserBrowserAgent: input.EndUserBrowserAgent,
		DefaultLanguage:     input.DefaultLanguage,
		DetailRate:          input.DetailRate,
	}
}

func FromDomainHubSearchResults(domainSearchResult []*domain.HotelSearchResult) ([]*models.HotelSearchResult, error) {
	result := make([]*models.HotelSearchResult, 0, len(domainSearchResult))

	for _, item := range domainSearchResult {
		parseSearchResult, err := FromDomainHubSearchResult(item)
		if err != nil {
			return nil, err
		}

		result = append(result, parseSearchResult)
	}

	return result, nil
}

func ToDomainHubSearchResults(modelSearchResult []*models.HotelSearchResult) []*domain.HotelSearchResult {
	result := make([]*domain.HotelSearchResult, 0, len(modelSearchResult))
	for _, item := range modelSearchResult {
		result = append(result, ToDomainHubSearchResult(item))
	}

	return result
}
