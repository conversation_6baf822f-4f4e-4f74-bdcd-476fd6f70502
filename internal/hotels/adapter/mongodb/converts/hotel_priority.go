package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainHotelPriority(in *models.HotelPriority) *domain.HotelPriority {
	if in == nil {
		return nil
	}

	return &domain.HotelPriority{
		HotelID:         in.HotelID,
		Name:            in.Name,
		ContentPriority: in.ContentPriority,
	}
}

func ToDomainHotelPriorities(in []*models.HotelPriority) []*domain.HotelPriority {
	result := make([]*domain.HotelPriority, 0, len(in))

	for _, item := range in {
		result = append(result, ToDomainHotelPriority(item))
	}

	return result
}
