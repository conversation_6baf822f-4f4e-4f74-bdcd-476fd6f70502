package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainContentBedOption(in *models.BedOptionContent) *domain.BedOptionContent {
	if in == nil {
		return nil
	}

	return &domain.BedOptionContent{
		BedID:    in.BedID,
		Name:     in.Name,
		Language: in.Language,
	}
}

func ToDomainContentBedOptions(in []*models.BedOptionContent) []*domain.BedOptionContent {
	result := make([]*domain.BedOptionContent, 0, len(in))

	for _, item := range in {
		result = append(result, ToDomainContentBedOption(item))
	}

	return result
}
