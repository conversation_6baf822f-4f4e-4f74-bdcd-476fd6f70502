package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainCurrencyExchanges(ins []*models.CurrencyExchange) []*domain.CurrencyExchange {
	out := make([]*domain.CurrencyExchange, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainCurrencyExchange(item))
	}

	return out
}

func ToDomainCurrencyExchange(in *models.CurrencyExchange) *domain.CurrencyExchange {
	if in == nil {
		return nil
	}

	return &domain.CurrencyExchange{
		Base: ToDomainBase(in.Base),
		From: in.From,
		To:   in.To,
		Rate: in.Rate,
	}
}

func FromDomainCurrencyExchange(in *domain.CurrencyExchange) *models.CurrencyExchange {
	if in == nil {
		return nil
	}

	return &models.CurrencyExchange{
		Base: FromDomainBase(in.Base),
		From: in.From,
		To:   in.To,
		Rate: in.Rate,
	}
}
