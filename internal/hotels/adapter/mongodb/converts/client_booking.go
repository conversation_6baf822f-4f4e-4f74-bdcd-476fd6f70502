package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainClientBooking(in *domain.ClientBooking) *models.ClientBooking {
	if in == nil {
		return nil
	}

	result := &models.ClientBooking{
		PartnershipID: in.PartnershipID,
		HubOrderCode:  in.HubOrderCode,
		Paid:          in.Paid,
	}
	return result
}

func ToDomainClientBooking(in *models.ClientBooking) *domain.ClientBooking {
	if in == nil {
		return nil
	}

	result := &domain.ClientBooking{
		ID:            in.ID.Hex(),
		PartnershipID: in.PartnershipID,
		HubOrderCode:  in.HubOrderCode,
		Paid:          in.Paid,
	}

	return result
}

func FromDomainClientBookings(ins []*domain.ClientBooking) []*models.ClientBooking {
	out := make([]*models.ClientBooking, 0, len(ins))

	for _, item := range ins {
		out = append(out, FromDomainClientBooking(item))
	}

	return out
}

func ToDomainClientBookings(ins []*models.ClientBooking) []*domain.ClientBooking {
	out := make([]*domain.ClientBooking, 0, len(ins))

	for _, item := range ins {
		out = append(out, ToDomainClientBooking(item))
	}

	return out
}
