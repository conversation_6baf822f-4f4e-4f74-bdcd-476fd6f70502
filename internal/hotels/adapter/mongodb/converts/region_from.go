package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func FromDomainRegions(in []*domain.Region) []*models.Region {
	result := make([]*models.Region, 0, len(in))
	for _, item := range in {
		result = append(result, FromDomainRegion(item))
	}

	return result
}

// FromDomainRegion converts domain.Region to models.Region.
func FromDomainRegion(domain *domain.Region) *models.Region {
	if domain == nil {
		return nil
	}

	return &models.Region{
		Base:                   FromDomainBase(domain.Base),
		RegionID:               domain.RegionID,
		Type:                   domain.Type,
		Name:                   domain.Name,
		FullName:               domain.FullName,
		Descriptor:             domain.Descriptor,
		IATAAirportCode:        domain.IATAAirportCode,
		IATAAirportMetroCode:   domain.IATAAirportMetroCode,
		CountryCode:            domain.CountryCode,
		CountrySubdivisionCode: domain.CountrySubdivisionCode,
		Coordinates:            FromDomainRegionCoordinates(domain.Coordinates),
		Associations:           domain.Associations,
		Ancestors:              FromDomainAncestors(domain.Ancestors),
		Descendants:            domain.Descendants,
		PropertyIDs:            domain.PropertyIDs,
		PropertyIDsExpanded:    domain.PropertyIDsExpanded,
		Categories:             domain.Categories,
		Tags:                   domain.Tags,
		Language:               domain.Language,
		Version:                domain.Version,
	}
}

// FromDomainRegionCoordinates converts domain.RegionCoordinates to models.RegionCoordinates.
func FromDomainRegionCoordinates(domain *domain.RegionCoordinates) *models.RegionCoordinates {
	if domain == nil {
		return nil
	}

	res := &models.RegionCoordinates{
		CenterLongitude: domain.CenterLongitude,
		CenterLatitude:  domain.CenterLatitude,
	}

	if domain.BoundingPolygonIDRef != "" {
		objectID, _ := primitive.ObjectIDFromHex(domain.BoundingPolygonIDRef)
		res.BoundingPolygonIDRef = objectID
	}

	return res
}

// FromDomainAncestor converts domain.Ancestor to models.Ancestor.
func FromDomainAncestor(domain *domain.Ancestor) *models.Ancestor {
	if domain == nil {
		return nil
	}

	return &models.Ancestor{
		ID:   domain.ID,
		Type: domain.Type,
	}
}

// FromDomainAncestors converts a slice of domain.Ancestor to a slice of models.Ancestor.
func FromDomainAncestors(domains []*domain.Ancestor) []*models.Ancestor {
	if domains == nil {
		return nil
	}

	modelAncestors := make([]*models.Ancestor, len(domains))
	for i, domain := range domains {
		modelAncestors[i] = FromDomainAncestor(domain)
	}

	return modelAncestors
}
