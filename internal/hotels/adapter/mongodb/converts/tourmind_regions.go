package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToTourmindRegion(in *models.TourmindRegion) *domain.TourmindRegion {
	return &domain.TourmindRegion{
		CountryCode: in.CountryCode,
		SeqNum:      in.SeqNum,
		CompletedAt: in.CompletedAt,
	}
}

func FromTourmindRegion(in *domain.TourmindRegion) *models.TourmindRegion {
	return &models.TourmindRegion{
		CountryCode: in.CountryCode,
		SeqNum:      in.SeqNum,
		CompletedAt: in.CompletedAt,
	}
}

func ToDomainTourmindRegions(in []*models.TourmindRegion) []*domain.TourmindRegion {
	var result []*domain.TourmindRegion
	for _, v := range in {
		result = append(result, ToTourmindRegion(v))
	}

	return result
}

func FromDomainTourmindRegions(in []*domain.TourmindRegion) []*models.TourmindRegion {
	var result []*models.TourmindRegion
	for _, v := range in {
		result = append(result, FromTourmindRegion(v))
	}

	return result
}
