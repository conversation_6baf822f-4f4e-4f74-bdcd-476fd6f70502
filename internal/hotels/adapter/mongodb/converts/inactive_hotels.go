package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainInActiveHotel(inActiveHotel *models.InActiveHotel) *domain.InActiveHotel {
	return &domain.InActiveHotel{
		HotelID:   inActiveHotel.HotelID,
		Providers: inActiveHotel.Providers,
		Note:      inActiveHotel.Note,
	}
}

func FromDomainInActiveHotel(inActiveHotel *domain.InActiveHotel) *models.InActiveHotel {
	return &models.InActiveHotel{
		HotelID:   inActiveHotel.HotelID,
		Providers: inActiveHotel.Providers,
		Note:      inActiveHotel.Note,
	}
}

func ToDomainInActiveHotels(inActiveHotels []*models.InActiveHotel) []*domain.InActiveHotel {
	domainInActiveHotels := make([]*domain.InActiveHotel, len(inActiveHotels))
	for i, inActiveHotel := range inActiveHotels {
		domainInActiveHotels[i] = ToDomainInActiveHotel(inActiveHotel)
	}
	return domainInActiveHotels
}