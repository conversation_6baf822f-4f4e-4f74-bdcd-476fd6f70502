package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToDomainContentAmenity(in *models.AmenityContent) *domain.AmenityContent {
	if in == nil {
		return nil
	}

	return &domain.AmenityContent{
		AmenityID: in.AmenityID,
		Name:      in.Name,
		Language:  in.Language,
	}
}

func ToDomainContentAmenities(in []*models.AmenityContent) []*domain.AmenityContent {
	result := make([]*domain.AmenityContent, 0, len(in))

	for _, item := range in {
		result = append(result, ToDomainContentAmenity(item))
	}

	return result
}
