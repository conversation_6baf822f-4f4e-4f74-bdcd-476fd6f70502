package models

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type RequestPlace struct {
	Base        `bson:",inline"`
	PlaceID     string                 `bson:"id"`
	Name        string                 `bson:"name"`
	Type        enum.PlaceType         `bson:"type"`
	Lang        string                 `bson:"language"`
	Location    *Coordinates           `bson:"location"`
	Address     string                 `bson:"address"`
	CountryCode string                 `bson:"country_code"`
	Source      commonEnum.PlaceSource `bson:"source"`
}
