package models

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

const InActiveHotelCollection = "inactive_hotels"
const InActiveHotelIndexHotelID = "__hotel_id__"

type InActiveHotel struct {
	Base      `bson:"inline" json:"base"`
	HotelID   string                     `bson:"hotel_id"`
	Providers []commonEnum.HotelProvider `bson:"providers"`
	Note      string                     `bson:"note"`
}
