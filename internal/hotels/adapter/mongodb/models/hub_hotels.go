package models

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type HubRateDiscount struct {
	Amount   float64 `bson:"amount"`
	Currency string  `bson:"currency,omitempty"`
	Code     string  `bson:"code,omitempty"`
	Name     string  `bson:"name"`
}

type HubPromotion struct {
	Code        string `bson:"code,omitempty"`
	Name        string `bson:"name"`
	Remark      string `bson:"remark,omitempty"`
	OfferType   string `bson:"offer_type,omitempty"`
	Frequency   string `bson:"frequency,omitempty"`
	PersonCount string `bson:"person_count"`
}

type PayAtHotel struct {
	Amount      float64 `bson:"amount"`
	Description string  `bson:"description"`
	Currency    string  `bson:"currency"`
}

type HubOccupancyRate struct {
	OccupancyType    string             `bson:"occupancy_type"`
	RoomQuantity     uint               `bson:"room_quantity"`
	TotalNightlyRate []*HubRate         `bson:"total_nightly_rate"`
	RateTaxes        []*HubRateTax      `bson:"rate_taxes"`
	PayAtHotel       []*PayAtHotel      `bson:"pay_at_hotel"`
	Surcharges       float64            `bson:"surcharges"`
	RateDiscounts    []*HubRateDiscount `bson:"rate_discounts"`
	MarketingFee     float64            `bson:"marketing_fee"`
}

type HubRate struct {
	RateAmount float64 `bson:"rate_amount"`
	RateBasic  float64 `bson:"rate_basic"`
	TaxAmount  float64 `bson:"tax_amount"`
	Currency   string  `bson:"currency"`
}

type HubRateSupplement struct {
	Amount    float64 `bson:"amount"`
	Code      string  `bson:"code"`
	From      string  `bson:"from"`
	Name      string  `bson:"name"`
	Nights    uint    `bson:"nights"`
	PaxNumber uint    `bson:"pax_number"`
	To        string  `bson:"to"`
}

type HubRateTax struct {
	Amount      *float64     `bson:"amount"`
	Currency    string       `bson:"currency"`
	Included    bool         `bson:"included"`
	Type        enum.TaxType `bson:"type"`
	Description string       `bson:"description"`
}

type HubAmenity struct {
	ID   string `bson:"id"`
	Name string `bson:"name"`
}

type BedOption struct {
	OptionID        string              `bson:"option_id"`
	Name            string              `bson:"name"`
	Quantity        uint                `bson:"quantity"`
	PriceCheckToken string              `bson:"price_check_token"`
	BedConfigs      []*BedConfiguration `bson:"bed_configs"`
}

type HubRateData struct {
	RateID                 string                       `bson:"rate_id,omitempty"`
	ProviderRateID         string                       `bson:"provider_rate_id,omitempty"`
	Available              string                       `bson:"available,omitempty"`
	CancelPolicies         []*HubCancelPolicy           `bson:"cancellation_policies,omitempty"`
	Commission             float64                      `bson:"commission,omitempty"`
	CommissionPCT          float64                      `bson:"commission_pct,omitempty"`
	CommissionVAT          float64                      `bson:"commission_vat,omitempty"`
	Promotions             []*HubPromotion              `bson:"promotions,omitempty"`
	OccupancyRate          []*HubOccupancyRate          `bson:"occupancy_rate"`
	Currency               string                       `bson:"currency"`
	TotalRateAmount        float64                      `bson:"total_rate_amount"`
	TotalRateBasic         float64                      `bson:"total_rate_basic"`
	TotalTaxAmount         float64                      `bson:"total_tax_amount"`
	PayNow                 float64                      `bson:"pay_now"`
	TotalPayAtHotel        float64                      `bson:"total_pay_at_hotel"`
	PayAtHotelCurrency     string                       `bson:"pay_at_hotel_currency"`
	BedOptions             []*BedOption                 `bson:"bed_options"`
	Amenities              []*HubAmenity                `bson:"amenities"`
	Refundable             bool                         `bson:"refundable"`
	SaleScenario           []string                     `bson:"sale_scenario"`
	HasBreakfast           bool                         `bson:"has_breakfast"`
	HasExtraBed            bool                         `bson:"has_extra_bed"`
	NonSmoking             bool                         `bson:"non_smoking"`
	HiddenFeeAmount        float64                      `bson:"hidden_fee_amount"`
	DiscountAmount         float64                      `bson:"discount_amount"`
	AppliedHiddenFee       interface{}                  `bson:"applied_hidden_fee"`
	AppliedDiscount        interface{}                  `bson:"applied_discount"`
	NonrefundableDateRange []*HubNonrefundableDateRange `bson:"nonrefundable_date_ranges"`
	FakeNonRefund          bool                         `bson:"fake_nonrefund"`
	IsSoldOut              bool                         `bson:"is_sold_out"`
	RouteRateID            string                       `bson:"route_rate_id,omitempty"`
	AdditionalData         *AdditionalData              `bson:"additional_data,omitempty"`
}

type HubNonrefundableDateRange struct {
	StartDate string `bson:"start_date"`
	EndDate   string `bson:"end_date"`
}

type HubCancelPolicyPenaltyInfo struct {
	Amount         float64 `bson:"amount,omitempty"`
	Percent        float64 `bson:"percent,omitempty"`
	NumberOfNights string  `bson:"number_of_nights,omitempty"`
}

type HubCancelPolicy struct {
	StartDate              string                       `bson:"start_date"`
	EndDate                string                       `bson:"end_date"`
	Currency               string                       `bson:"currency"`
	NonrefundableDateRange []*HubNonrefundableDateRange `bson:"nonrefundable_date_ranges"`
	PenaltyInfo            HubCancelPolicyPenaltyInfo   `bson:"penalty_info"`
	PartialRefund          bool                         `bson:"partial_refund"`
	Refundable             bool                         `bson:"refundable"`
	RawPenaltyAmount       float64                      `bson:"raw_penalty_amount"`
	PenaltyAmount          float64                      `bson:"penalty_amount"`
}

type HubRoom struct {
	RoomID            string                   `bson:"room_id"`
	ProviderRoomID    string                   `bson:"provider_room_id"`
	Provider          commonEnum.HotelProvider `bson:"provider"`
	Name              string                   `bson:"name"`
	NameEn            string                   `bson:"name_en"`
	RateData          []*HubRateData           `bson:"rate_data,omitempty"`
	ExchangedRateData []*HubRateData           `bson:"exchanged_rate_data,omitempty"`
	Descriptions      *RoomDescriptions        `json:"descriptions,omitempty"`
	Amenities         []*Amenity               `json:"amenities,omitempty"`
	Images            []*Image                 `json:"images,omitempty"`
	BedGroups         []*BedGroup              `json:"bed_groups,omitempty"`
	Area              *Area                    `json:"area,omitempty"`
	Occupancy         *Occupancy               `json:"occupancy,omitempty"`
	Views             []*View                  `json:"views,omitempty"`
	OldProviderRoomID string                   `bson:"old_provider_room_id,omitempty"`
}

type HubHotelReview struct {
	Rate        float64 `bson:"rate"`
	ReviewCount int64   `bson:"review_count"`
}

type HubHotel struct {
	Address               *Address                 `bson:"address"`
	HotelID               string                   `bson:"hotel_id"`
	ProviderHotelID       string                   `bson:"provider_hotel_id"`
	OldProviderID         string                   `bson:"old_provider_id,omitempty"`
	Rating                float64                  `bson:"rating"`
	HotelType             string                   `bson:"hotel_type"`
	Name                  string                   `bson:"name"`
	NameEn                string                   `bson:"name_en"`
	VAT                   bool                     `bson:"vat"`
	ListRooms             []*HubRoom               `bson:"rooms"`
	Currency              string                   `bson:"currency"`
	CheckInTime           string                   `bson:"check_in_time"`
	CheckOutTime          string                   `bson:"check_out_time"`
	ApplicableNationality []*ApplicableNationality `bson:"applicable_nationality" `
}

type ApplicableNationality struct {
	ID               int32             `bson:"id,omitempty"`
	Name             string            `bson:"name,omitempty"`
	Excluded         bool              `bson:"excluded,omitempty"`
	Nationalities    []string          `bson:"nationalities,omitempty"`
	NationalityPrice *NationalityPrice `bson:"nationality_price,omitempty"`
}

type NationalityPrice struct {
	TotalRate        float64           `bson:"total_rate,omitempty"`
	ExtraBed         int32             `bson:"extra_bed,omitempty"`
	RateExtraConfigs []RateExtraConfig `bson:"rate_extra_configs,omitempty"`
}

type RateExtraConfig struct {
	DayOfWeek      []int    `bson:"day_of_week,omitempty"`
	Dates          []string `bson:"dates,omitempty"`
	TotalRateExtra float64  `bson:"total_rate_extra"`
}

type AdditionalData struct {
	AgodaCheckAvaibilityInfo *AgodaCheckAvaibilityInfo `bson:"agoda_check_availability_info,omitempty"`
}

type AgodaCheckAvaibilityInfo struct {
	Rate         *AgodaRate   `bson:"rate,omitempty"`
	Surcharges   []*Surcharge `bson:"surcharges,omitempty"`
	PaymentModel string       `bson:"payment_model,omitempty"`
}

type AgodaRate struct {
	Inclusive float64 `bson:"inclusive,omitempty"`
}

type Surcharge struct {
	ID   int64      `bson:"id,omitempty"`
	Rate *AgodaRate `bson:"rate,omitempty"`
}
