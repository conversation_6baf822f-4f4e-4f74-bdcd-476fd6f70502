package models

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type HolderDetail struct {
	OccupancyIndex uint   `bson:"occupancy_index"`
	GivenName      string `bson:"given_name"`
	Surname        string `bson:"surname"`
	Email          string `bson:"email"`
	SpecialRequest string `bson:"special_request"`
}

type FareDataIssuing struct {
	Amount       float64 `bson:"amount"`
	AmountString string  `bson:"amount_string"`
	Currency     string  `bson:"currency"`
	Type         string  `bson:"type"`
}

type HubHolderInfo struct {
	PhoneNumber  string          `bson:"phone_number"`
	PhoneCode    string          `bson:"phone_code"`
	HolderDetail []*HolderDetail `bson:"hotel_detail"`
}

type BookingRequest struct {
	SearchKey   string `bson:"search_key"`
	HotelID     string `bson:"hotel_id"`
	RoomID      string `bson:"room_id"`
	RateID      string `bson:"rate_id"`
	OfficeID    string `bson:"-"`
	BedOptionID string `bson:"bed_option_id"`
	PNRCode     string `bson:"pnr_code"`
}

type RoomBedOption struct {
	OptionID   string              `bson:"option_id"`
	Name       string              `bson:"name"`
	Quantity   uint                `bson:"quantity"`
	Type       string              `bson:"type"`
	BedConfigs []*BedConfiguration `bson:"bed_configs"`
}
type HubOrderRoomItem struct {
	RoomID              string                   `bson:"room_id"`
	ProviderRoomID      string                   `bson:"provider_room_id"`
	OldProviderRoomID   string                   `bson:"old_provider_room_id,omitempty"`
	Name                string                   `bson:"name"`
	NameEn              string                   `bson:"name_en"`
	RateData            []*HubRateData           `bson:"rate_data,omitempty"`
	Provider            commonEnum.HotelProvider `bson:"provider"`
	ConfirmationID      string                   `bson:"confirmation_id,omitempty"`
	HotelConfirmationID string                   `bson:"hotel_confirmation_id,omitempty"`
	BookingStatus       enum.BookingStatus       `bson:"booking_status"`
	OccupancyType       string                   `bson:"occupancy_type,omitempty"`
	OccupancyIndex      uint                     `bson:"occupancy_index,omitempty"`
	GivenName           string                   `bson:"given_name,omitempty"`
	Surname             string                   `bson:"surname,omitempty"`
	Email               string                   `bson:"email,omitempty"`
	SpecialRequest      string                   `bson:"special_request,omitempty"`
	BedOption           *RoomBedOption           `bson:"bed_option"`
}

type HubOrderHotelItem struct {
	HotelID         string              `bson:"hotel_id"`
	ProviderHotelID string              `bson:"provider_hotel_id"`
	OldProviderID   string              `bson:"old_provider_id,omitempty"`
	Name            string              `bson:"name"`
	NameEn          string              `bson:"name_en"`
	VAT             bool                `bson:"vat"`
	ListRooms       []*HubOrderRoomItem `bson:"rooms"`
	Currency        string              `bson:"currency"`
	Address         *Address            `bson:"address"`
	CheckInTime     string              `bson:"check_in_time"`
	CheckOutTime    string              `bson:"check_out_time"`
}

type OrderHiddenFee struct {
	HiddenFeeConfig   *HiddenServiceFee `bson:"hidden_fee_config"`
	TotalChargeAmount float64           `bson:"total_charge_amount"`
}
type Fee struct {
	HiddenFee *OrderHiddenFee `bson:"hidden_fee"`
}

type RefundData struct {
	ProviderRefundAmount float64 `bson:"provider_refund_amount"`
	RefundAmount         float64 `bson:"refund_amount"`
	PenaltyAmount        float64 `bson:"penalty_amount"`
	Currency             string  `bson:"currency"`
}

type HubHotelOrder struct {
	Base                             `bson:",inline"`
	SessionID                        string                   `bson:"session_id"`
	NewSessionID                     string                   `bson:"new_session_id,omitempty"`
	OrderCode                        string                   `bson:"order_code"`
	ReservationCode                  string                   `bson:"reservation_code"`
	ProviderBookingStatus            string                   `bson:"provider_booking_status"`
	OfficeID                         string                   `bson:"office_id"`
	AgentCode                        string                   `bson:"agent_code"`
	PartnershipID                    string                   `bson:"partnership_id"` // Partnership ID để phân biệt các partnership trong SUB Hub
	Hotel                            *HubOrderHotelItem       `bson:"hotel"`
	RateData                         *HubRateData             `bson:"rate_data"`
	OriginalRateDataCf               *HubRateData             `bson:"original_rate_data_cf"`
	ExchangedRateDataCfRaw           *HubRateData             `bson:"exchanged_rate_data_cf_raw"`
	ExchangedRateDataCf              *HubRateData             `bson:"exchanged_rate_data_cf"`
	RequestCurrency                  string                   `bson:"request_currency"`
	RequestCurrencyRateDataCf        *HubRateData             `bson:"request_currency_rate_data_cf"`
	RequestCurrencyRateApply         *CurrencyExchange        `bson:"request_currency_rate_apply"`
	Fee                              *Fee                     `bson:"fee"`
	ExchangeRateApply                *CurrencyExchange        `bson:"exchange_rate_apply"`
	RequestHolder                    *HubHolderInfo           `bson:"request_holder"`
	Provider                         commonEnum.HotelProvider `bson:"provider"`
	OldProvider                      commonEnum.HotelProvider `bson:"old_provider,omitempty"`
	LastConfirmDate                  int64                    `bson:"last_confirm_date"`
	BookingRequest                   *BookingRequest          `bson:"booking_request"`
	OrderPaymentID                   string                   `bson:"order_payment_id"`
	LastTransactionID                string                   `bson:"last_transaction_id"`
	CustomerIP                       string                   `bson:"customer_ip"`
	HubOrderStatus                   enum.HubOrderStatus      `bson:"status"`
	BookingStatus                    enum.BookingStatus       `bson:"booking_status"`
	PendingDeadline                  int64                    `bson:"pending_deadline"`
	CancelDeadline                   int64                    `bson:"cancel_deadline"`
	ManualIssuing                    bool                     `bson:"manual_issuing"`
	HotelSearchRequest               *HotelSearchRequest      `bson:"hotel_search_request"`
	FareDataIssuing                  *FareDataIssuing         `bson:"fare_data_issuing"`
	PendingStartAt                   int64                    `bson:"pending_start_at"`
	RefundData                       *RefundData              `bson:"refund_data"`
	ExchangedRefundData              *RefundData              `bson:"exchange_refund_data"`
	RequestCurrencyRefundData        *RefundData              `bson:"request_currency_refund_data"`
	Refunded                         bool                     `bson:"refunded"`
	CancelingStartAt                 int64                    `bson:"canceling_start_at"`
	SkipScanCheckCancel              bool                     `bson:"skip_scan_check_cancel"`
	SearchKey                        string                   `bson:"search_key"`
	CancelReason                     string                   `bson:"cancel_reason,omitempty"`
	TABookingID                      string                   `bson:"ta_booking_id"`
	IsInvoice                        bool                     `bson:"is_invoice"`
	InvoicingInformation             *InvoicingInformation    `bson:"invoicing_information,omitempty"`
	PendingHotelConfirmationDeadline int64                    `bson:"pending_hotel_confirmation_deadline"`
	ClientOrderCode                  string                   `bson:"client_order_code"`
	UnknownPending                   bool                     `bson:"unknown_pending"`
	BookingAt                        int64                    `bson:"booking_at"`
}

type InvoicingInformationCompany struct {
	Name    string `bson:"name"`
	TaxCode string `bson:"tax_code"`
	Address string `bson:"address"`
}

type InvoicingInformationReceiver struct {
	Name        string `bson:"name"`
	PhoneCode   string `bson:"phone_code"`
	PhoneNumber string `bson:"phone_number"`
	Email       string `bson:"email"`
	Address     string `bson:"address"`
	Note        string `bson:"note"`
}

type InvoicingInformation struct {
	Company  *InvoicingInformationCompany  `bson:"company"`
	Receiver *InvoicingInformationReceiver `bson:"receiver"`
}
