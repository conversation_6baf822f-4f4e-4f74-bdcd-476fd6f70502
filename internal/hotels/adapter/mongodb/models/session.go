package models

type ExpediaSessionInfo struct {
	RetrieveBookingSession string `bson:"retrieve_booking_session"`
	HoldBookingSession     string `bson:"hold_booking_session"`
	CancelBookingSession   string `bson:"cancel_booking_session"`
}

type TourmindSessionInfo struct {
	RateCode  string `bson:"rate_code"`
	HotelCode int    `bson:"hotel_code"`
}

type BasicSessionInfo struct {
	BookingKey string `bson:"booking_key"`
}

type DidaSessionInfo struct {
	ReferenceNo            string `bson:"reference_no"`
	CancelBookingConfirmID string `bson:"cancel_booking_confirm_id,omitempty"`
}
type HubSessionInfo struct {
	SessionID string `bson:"session_id"`
}

type AgodaSessionInfo struct {
	SearchID     int64  `bson:"search_id,omitempty"`
	PaymentModel string `bson:"payment_model,omitempty"`
}

type HotelSession struct {
	Base                `bson:",inline"`
	SessionID           string               `bson:"session_id"`
	OfficeID            string               `bson:"office_id"`
	ExpiredAt           int64                `bson:"expired_at"`
	ExpediaSessionInfo  *ExpediaSessionInfo  `bson:"expedia_session_info,omitempty"`
	TourmindSessionInfo *TourmindSessionInfo `bson:"tourmind_session_info,omitempty"`
	BasicSessionInfo    *BasicSessionInfo    `bson:"basic_session_info,omitempty"`
	DidaSessionInfo     *DidaSessionInfo     `bson:"dida_session_info,omitempty"`
	HubSessionInfo      *HubSessionInfo      `bson:"hub_session_info,omitempty"`
	AgodaSessionInfo    *AgodaSessionInfo    `bson:"agoda_session_info,omitempty"`
}
