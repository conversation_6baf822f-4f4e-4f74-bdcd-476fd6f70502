package models
import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type AreaCacheHotelInfo struct {
	HotelID       string                              `bson:"hotel_id"`
	HotelDistance float64                             `bson:"distance"`
	ProviderIds   map[commonEnum.HotelProvider]string `bson:"provider_id"`
}

type HotelAreaCacheItem struct {
	Base     `bson:",inline"`
	Hotels   []AreaCacheHotelInfo `bson:"hotel_ids"`
	AreaType string               `bson:"area_type"`
	AreaID   string               `bson:"area_id"`
}
