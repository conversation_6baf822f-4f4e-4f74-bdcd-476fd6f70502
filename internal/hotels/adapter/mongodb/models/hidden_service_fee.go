package models

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type HiddenServiceFeeConfig struct {
	LocationType enum.HotelLocationType `bson:"location_type"`
	CountryCode  []string               `bson:"country_code"`
	Rating       []float64              `bson:"rating"`
	HotelType    []string               `bson:"hotel_type"`
}

type HiddenServiceFee struct {
	Base      `bson:",inline"`
	OfficeID  string                   `bson:"office_id"`
	Type      enum.HiddenFeeType       `bson:"type"`
	Config    *HiddenServiceFeeConfig  `bson:"config"`
	Provider  commonEnum.HotelProvider `bson:"provider"`
	HotelID   string                   `bson:"hotel_id"`
	HotelName string                   `bson:"hotel_name"`
	Amount    float64                  `bson:"amount"`
	Percent   float64                  `bson:"percent"`
}
