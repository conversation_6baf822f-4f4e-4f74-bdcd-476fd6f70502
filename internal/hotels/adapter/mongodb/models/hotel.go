package models

import (
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	HotelCollectionHotelIDLanguageVersionIndex = "__hotel_id__language__version__"
	HotelCollectionCountryCodeVersionIndex     = "__country_code__version__"
	HotelCollectionVersionIndex                = "version_1"
	HotelCollectionCoordinateGeo               = "__version_language_location_coordinates_2dsphere__"
)

// Root struct.
type Hotel struct {
	Base                           `bson:",inline"`
	Active                         bool                                `bson:"active,omitempty"`
	MultiUnit                      bool                                `bson:"multi_unit,omitempty"`
	PaymentRegistrationRecommended bool                                `bson:"payment_registration_recommended,omitempty"`
	Rank                           float64                             `bson:"rank,omitempty"`
	Address                        *Address                            `bson:"address,omitempty"`
	Ratings                        *Ratings                            `bson:"ratings,omitempty"`
	Location                       *Location                           `bson:"location,omitempty"`
	RoomReferences                 []*RoomRefInfo                      `bson:"room_references,omitempty"`
	Category                       *Category                           `bson:"category,omitempty"`
	BusinessModel                  *BusinessModel                      `bson:"business_model,omitempty"`
	CheckIn                        *CheckIn                            `bson:"check_in,omitempty"`
	Checkout                       *Checkout                           `bson:"checkout,omitempty"`
	Fees                           *Fees                               `bson:"fees,omitempty"`
	Policies                       *Policies                           `bson:"policies,omitempty"`
	Attributes                     *Attributes                         `bson:"attributes,omitempty"`
	OnsitePayments                 *OnsitePayments                     `bson:"onsite_payments,omitempty"`
	Dates                          *Dates                              `bson:"dates,omitempty"`
	Descriptions                   *Descriptions                       `bson:"descriptions,omitempty"`
	Chain                          *Chain                              `bson:"chain,omitempty"`
	Brand                          *Brand                              `bson:"brand,omitempty"`
	SpokenLanguages                map[string]*Language                `bson:"spoken_languages,omitempty"`
	ProviderIds                    map[commonEnum.HotelProvider]string `bson:"provider_ids,omitempty"`
	Language                       string                              `bson:"language,omitempty"`
	HotelID                        string                              `bson:"hotel_id,omitempty"`
	Name                           string                              `bson:"name,omitempty"`
	Phone                          string                              `bson:"phone,omitempty"`
	Fax                            string                              `bson:"fax,omitempty"`
	SupplySource                   string                              `bson:"supply_source,omitempty"`
	Amenities                      []*Amenity                          `bson:"amenities,omitempty"`
	Images                         []*Image                            `bson:"images,omitempty"`
	Rates                          []*ContentRate                      `bson:"rates,omitempty"`
	Statistics                     []*Statistic                        `bson:"statistics,omitempty"`
	Airports                       *Airports                           `bson:"airports,omitempty"`
	AllInclusive                   *AllInclusive                       `bson:"all_inclusive,omitempty"`
	VacationRentalDetails          *VacationRentalDetails              `bson:"vacation_rental_details,omitempty"`
	TaxID                          string                              `bson:"tax_id,omitempty"`
	RegistryNumber                 string                              `bson:"registry_number,omitempty"`
	Themes                         []*Theme                            `bson:"themes,omitempty"`
	Version                        string                              `bson:"version,omitempty"`
	Distance                       float64                             `bson:"distance"`
	VerotechID                     string                              `bson:"verotech_id,omitempty"`
}

type RoomRefInfo struct {
	ID          primitive.ObjectID                  `bson:"_id,omitempty"`
	ProviderIds map[commonEnum.HotelProvider]string `bson:"provider_ids,omitempty"`
	RoomID      string                              `bson:"room_id,omitempty"`
}

type Airports struct {
	Preferred *PreferredAirport `bson:"preferred,omitempty"`
}

type PreferredAirport struct {
	IataAirportCode string `bson:"iata_airport_code,omitempty"`
}

type AllInclusive struct {
	AllRatePlans  bool   `bson:"all_rate_plans,omitempty"`
	SomeRatePlans bool   `bson:"some_rate_plans,omitempty"`
	Details       string `bson:"details,omitempty"`
}

// Address struct.
type Address struct {
	Line1               string `bson:"line_1,omitempty"`
	City                string `bson:"city,omitempty"`
	StateProvinceName   string `bson:"state_province_name,omitempty"`
	PostalCode          string `bson:"postal_code,omitempty"`
	CountryCode         string `bson:"country_code,omitempty"`
	ObfuscationRequired bool   `bson:"obfuscation_required,omitempty"`
}

// Ratings struct.
type Ratings struct {
	Property *PropertyRating `bson:"property,omitempty"`
	Guest    *GuestRating    `bson:"guest,omitempty"`
}

type PropertyRating struct {
	Rating string `bson:"rating,omitempty"`
	Type   string `bson:"type,omitempty"`
}

type GuestRating struct {
	Count                 int    `bson:"count,omitempty"`
	Overall               string `bson:"overall,omitempty"`
	Cleanliness           string `bson:"cleanliness,omitempty"`
	Service               string `bson:"service,omitempty"`
	Comfort               string `bson:"comfort,omitempty"`
	Condition             string `bson:"condition,omitempty"`
	Location              string `bson:"location,omitempty"`
	Neighborhood          string `bson:"neighborhood,omitempty"`
	Quality               string `bson:"quality,omitempty"`
	Value                 string `bson:"value,omitempty"`
	Amenities             string `bson:"amenities,omitempty"`
	RecommendationPercent string `bson:"recommendation_percent,omitempty"`
}

// Location struct.
type Location struct {
	Coordinates           *Coordinates `bson:"coordinates,omitempty"`
	ObfuscatedCoordinates *Coordinates `bson:"obfuscated_coordinates,omitempty"`
	ObfuscationRequired   bool         `bson:"obfuscation_required,omitempty"`
}

type Coordinates struct {
	Longitude float64 `bson:"longitude,omitempty"`
	Latitude  float64 `bson:"latitude,omitempty"`
}

// Category struct.
type Category struct {
	ID   string `bson:"id,omitempty"`
	Name string `bson:"name,omitempty"`
}

// BusinessModel struct.
type BusinessModel struct {
	ExpediaCollect  bool `bson:"expedia_collect,omitempty"`
	PropertyCollect bool `bson:"property_collect,omitempty"`
}

// CheckIn struct.
type CheckIn struct {
	TwentyFourHour      string  `bson:"24_hour,omitempty"`
	BeginTime           string  `bson:"begin_time,omitempty"`
	EndTime             string  `bson:"end_time,omitempty"`
	Instructions        string  `bson:"instructions,omitempty"`
	SpecialInstructions string  `bson:"special_instructions,omitempty"`
	MinAge              float64 `bson:"min_age,omitempty"`
}

// Checkout struct.
type Checkout struct {
	Time string `bson:"time,omitempty"`
}

// Fees struct.
type Fees struct {
	Mandatory       string `bson:"mandatory,omitempty"`
	Optional        string `bson:"optional,omitempty"`
	TravelerService string `bson:"traveler_service,omitempty"`
}

// Policies struct.
type Policies struct {
	KnowBeforeYouGo string `bson:"know_before_you_go,omitempty"`
}

// Attributes struct.
type Attributes struct {
	Pets    []*Attribute `bson:"pets,omitempty"`
	General []*Attribute `bson:"general,omitempty"`
}

type Attribute struct {
	ID    string `bson:"id,omitempty"`
	Name  string `bson:"name,omitempty"`
	Value string `bson:"value,omitempty,omitempty"`
}

// Amenity struct.
type Amenity struct {
	ID         string   `bson:"id,omitempty"`
	Name       string   `bson:"name,omitempty"`
	Categories []string `bson:"categories,omitempty,omitempty"`
	Value      string   `bson:"value,omitempty,omitempty"`
	GroupName  string   `bson:"group_name,omitempty"`
}

// Image struct.
type Image struct {
	HeroImage bool             `bson:"hero_image,omitempty"`
	Category  float64          `bson:"category,omitempty"`
	Links     map[string]*Link `bson:"links,omitempty"`
	Caption   string           `bson:"caption,omitempty"`
}

type Link struct {
	Method string `bson:"method,omitempty"`
	Href   string `bson:"href,omitempty"`
}

// OnsitePayments struct.
type OnsitePayments struct {
	Currency string         `bson:"currency,omitempty"`
	Types    []*PaymentType `bson:"types,omitempty"`
}

type PaymentType struct {
	ID   string `bson:"id,omitempty"`
	Name string `bson:"name,omitempty"`
}

// ContentRate struct.
type ContentRate struct {
	ID        string     `bson:"id,omitempty"`
	Amenities []*Amenity `bson:"amenities,omitempty"`
}

// Dates struct.
type Dates struct {
	Added   time.Time `bson:"added,omitempty"`
	Updated time.Time `bson:"updated,omitempty"`
}

// Descriptions struct.
type Descriptions struct {
	Amenities         string `bson:"amenities,omitempty"`
	Dining            string `bson:"dining,omitempty"`
	Renovations       string `bson:"renovations,omitempty"`
	NationalRatings   string `bson:"national_ratings,omitempty"`
	BusinessAmenities string `bson:"business_amenities,omitempty"`
	Rooms             string `bson:"rooms,omitempty"`
	Attractions       string `bson:"attractions,omitempty"`
	Location          string `bson:"location,omitempty"`
	Headline          string `bson:"headline,omitempty"`
}

// Statistic struct.
type Statistic struct {
	ID    string `bson:"id,omitempty"`
	Name  string `bson:"name,omitempty"`
	Value string `bson:"value,omitempty"`
}

// Chain and Brand struct.
type Chain struct {
	ID   string `bson:"id,omitempty"`
	Name string `bson:"name,omitempty"`
}

type Brand struct {
	ID   string `bson:"id,omitempty"`
	Name string `bson:"name,omitempty"`
}

// Language struct.
type Language struct {
	ID   string `bson:"id,omitempty"`
	Name string `bson:"name,omitempty"`
}

type Theme struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type VacationRentalDetails struct {
	RegistryNumber     string                   `bson:"registry_number"`
	PrivateHost        bool                     `bson:"private_host"`
	PropertyManager    *PropertyManager         `bson:"property_manager"`
	RentalAgreement    *RentalAgreement         `bson:"rental_agreement"`
	HouseRules         []string                 `bson:"house_rules"`
	EnhancedHouseRules map[string]*EnhancedRule `bson:"enhanced_house_rules"`
	Amenities          map[string]*Amenity      `bson:"amenities"`
	VrboSrpID          string                   `bson:"vrbo_srp_id"`
	ListingID          string                   `bson:"listing_id"`
	ListingNumber      string                   `bson:"listing_number"`
	ListingSource      string                   `bson:"listing_source"`
	ListingUnit        string                   `bson:"listing_unit"`
	IPMName            string                   `bson:"ipm_name"`
	UnitConfigurations map[string][]*UnitConfig `bson:"unit_configurations"`
}

// PropertyManager represents the property manager's information.
type PropertyManager struct {
	Name  string           `bson:"name"`
	Links map[string]*Link `bson:"links"`
}

// RentalAgreement represents the rental agreement information.
type RentalAgreement struct {
	Links map[string]*Link `bson:"links"`
}

// EnhancedRule represents enhanced house rules.
type EnhancedRule struct {
	Rule                  string   `bson:"rule"`
	AdditionalInformation []string `bson:"additional_information"`
}

// UnitConfig represents the configuration of a vacation rental unit.
type UnitConfig struct {
	Type        string `bson:"type"`
	Description string `bson:"description"`
	Quantity    int    `bson:"quantity"`
	FreeText    string `bson:"free_text"`
}
