package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/base"
	baseWalletPb "gitlab.deepgate.io/apps/api/gen/go/wallet"
	pb "gitlab.deepgate.io/apps/api/gen/go/wallet/backend"
	"gitlab.deepgate.io/apps/common/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToTransactionHisReq(t *domain.TransactionHistoryReq) *pb.GetTransactionsHistoryReq {
	if t == nil {
		return nil
	}

	transactionType := commonEnum.TransactionTypeNone

	if t.Refund != nil {
		if *t.Refund {
			transactionType = commonEnum.TransactionTypeRefund
		} else {
			transactionType = commonEnum.TransactionTypePay
		}
	}

	return &pb.GetTransactionsHistoryReq{
		From:            t.From,
		To:              t.To,
		Source:          baseWalletPb.TransactionSource(commonEnum.TransactionSourceHubHotel),
		SourceRef:       t.BookingCode,
		PaymentMethod:   base.PaymentMethod(t.PaymentMethod),
		TransactionType: baseWalletPb.TransactionType(transactionType),
	}
}

func ToTransactionDomain(t *pb.Transaction) *domain.Transaction {
	transaction := &domain.Transaction{
		TransactionID: t.Id,
		Type:          commonEnum.TransactionType(t.Type),
		Amount:        t.Amount,
		CreatedAt:     t.Time,
		PaymentMethod: &domain.PaymentMethod{
			Method: commonEnum.PaymentMethod(t.Metadata.PaymentMethod),
		},
		OrderCode: t.SourceRef,
		Currency:  constants.CurrencyCodeVND,
	}

	return transaction
}

func ToTransactionsDomain(transactions []*pb.Transaction) []*domain.Transaction {
	txs := make([]*domain.Transaction, 0, len(transactions))

	for _, t := range transactions {
		txs = append(txs, ToTransactionDomain(t))
	}

	return txs
}
