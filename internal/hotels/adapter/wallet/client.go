package wallet

import (
	"context"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/wallet"
	walletPb "gitlab.deepgate.io/apps/api/gen/go/wallet"
	walletAdminPb "gitlab.deepgate.io/apps/api/gen/go/wallet/admin"
	walletBackendPb "gitlab.deepgate.io/apps/api/gen/go/wallet/backend"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"google.golang.org/grpc/metadata"
)

type walletClient struct {
	cfg *config.Schema
}

type WalletClient interface {
	GetBalance(ctx context.Context, userID, currencyCode string) (float64, error)
	GetTransactionHistory(ctx context.Context, userID string, filter *domain.TransactionHistoryReq) ([]*domain.Transaction, *commonDomain.Pagination, error)
	RequestRefund(ctx context.Context, partnershipID, userID, transactionID string, shopInfo *domain.PartnerShopInfo, provider string, refundAmount float64) (*domain.TransactionInfo, error)
	AggregateHubTransaction(ctx context.Context, transactionID string, order *domain.HubHotelOrder, shopInfo *domain.PartnerShopInfo) (string, error)
}

func NewWalletClient(cfg *config.Schema) WalletClient {
	return &walletClient{cfg: cfg}
}

func (c *walletClient) RequestRefund(ctx context.Context, partnershipID, userID, transactionID string, shopInfo *domain.PartnerShopInfo, provider string, refundAmount float64) (*domain.TransactionInfo, error) {
	if shopInfo == nil {
		return nil, errors.Wrap(commonError.ErrInvalidInput, "shopInfo nil")
	}

	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.WalletServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, partnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := walletBackendPb.NewWalletServiceClient(conn)

	req := &walletBackendPb.RequestRefundReq{
		TransactionId: transactionID,
		UserId:        userID,
		PartialAmount: refundAmount,
		AdditionData: &walletBackendPb.AdditionData{
			HubMetadata: &wallet.HubMetadata{
				AgentCode: shopInfo.Code,
				AgentName: shopInfo.Name,
				Provider:  provider,
			},
		},
	}

	res, err := client.RequestRefund(newCtx, req)
	if err != nil {
		return nil, errors.Wrap(err, "client.RequestRefund")
	}

	return converts.ToDomainTransactionInfo(res), nil
}

func (c *walletClient) GetBalance(ctx context.Context, userID, currencyCode string) (float64, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.WalletServiceEndpoint)
	if err != nil {
		return 0, errors.Wrap(err, "Cannot connect wallet service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := walletBackendPb.NewWalletServiceClient(conn)
	req := &walletBackendPb.GetWalletBalanceReq{
		UserId:                userID,
		PartnershipId:         c.cfg.HubPartnershipID,
		ConvertToCurrencyCode: currencyCode,
	}

	r, err := client.GetWalletBalance(
		newCtx,
		req,
	)
	if err != nil {
		return 0, errors.Wrap(err, "Wallet GetWalletBalance failed")
	}

	return r.Balance, nil
}

func (c *walletClient) GetTransactionHistory(ctx context.Context, userID string, filter *domain.TransactionHistoryReq) ([]*domain.Transaction, *commonDomain.Pagination, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.WalletServiceEndpoint)
	if err != nil {
		return nil, nil, errors.Wrap(err, "Cannot connect wallet service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := walletBackendPb.NewTransactionServiceClient(conn)

	req := converts.ToTransactionHisReq(filter)
	req.UserId = userID

	if filter.Pagination != nil {
		req.Pagination = &base.PaginationReq{
			PageLimit:  uint32(filter.Pagination.PageLimit),
			PageNumber: uint32(filter.Pagination.PageNumber),
		}
	}

	r, err := client.GetTransactionsHistory(
		newCtx,
		req,
	)
	if err != nil {
		return nil, nil, errors.Wrap(err, "Wallet GetTransactionHistory failed")
	}

	response := converts.ToTransactionsDomain(r.Transactions)

	return response, &commonDomain.Pagination{
		PageLimit:   int64(r.Pagination.PageLimit),
		PageCurrent: int64(r.Pagination.PageCurrent),
		TotalPage:   int64(r.Pagination.TotalPage),
		TotalRecord: r.Pagination.TotalRecord,
	}, nil
}

func (c *walletClient) AggregateHubTransaction(ctx context.Context, transactionID string, order *domain.HubHotelOrder, shopInfo *domain.PartnerShopInfo) (string, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.WalletServiceEndpoint)
	if err != nil {
		return "", errors.Wrap(err, "Cannot connect wallet service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, c.cfg.HubPartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	client := walletAdminPb.NewAdminServiceClient(conn)

	var priceBreakdown *walletPb.PriceBreakdown
	if order.ExchangedRateDataCf != nil {
		priceBreakdown = &walletPb.PriceBreakdown{
			BasePrice: order.ExchangedRateDataCf.PayNow,
			HiddenFee: order.ExchangedRateDataCf.HiddenFeeAmount,
			Discount:  order.ExchangedRateDataCf.DiscountAmount,
		}
	}

	req := &walletAdminPb.AggregateHubTransactionReq{
		TransactionId: transactionID,
		PartnerShopId: shopInfo.ID,
		HubMetadata: &wallet.HubMetadata{
			Provider:  commonEnum.HotelProviderName[order.Provider],
			AgentCode: shopInfo.Code,
			AgentName: shopInfo.Name,
		},
		ReferenceCode:  order.OrderCode,
		ProductType:    base.ProductType_Hotel,
		Source:         int64(commonEnum.TransactionSourceHubHotel),
		SourceRef:      order.ID,
		PriceBreakdown: priceBreakdown,
	}

	res, err := client.AggregateHubTransaction(
		newCtx,
		req,
	)
	if err != nil {
		return "", errors.Wrap(err, "Wallet AggregateHubTransaction failed")
	}

	if res == nil {
		return "", errors.New("Wallet AggregateHubTransaction failed")
	}

	if res.ErrorCode != "" {
		return "", errors.New(res.ErrorCode)
	}

	return res.NewTransactionId, nil
}
