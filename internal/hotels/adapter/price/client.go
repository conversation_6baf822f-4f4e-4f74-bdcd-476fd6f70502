package price

import (
	"context"
	"sync"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/api/gen/go/price"
	pricePb "gitlab.deepgate.io/apps/api/gen/go/price/backend"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"google.golang.org/grpc/metadata"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/price/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type PriceClient interface {
	CalculateHotelSearchPrices(ctx context.Context, req *domain.CalculateHotelSearchPricesReq) error
	CalculateHotelDetailPrices(ctx context.Context, req *domain.CalculateHotelDetailPricesReq) error
	CalculateHotelRateDataPrices(ctx context.Context, req *domain.CalculateHotelRateDataPricesReq) error
}

type priceClient struct {
	cfg *config.Schema
}

func NewPriceClient(cfg *config.Schema) PriceClient {
	return &priceClient{
		cfg: cfg,
	}
}

func (s priceClient) CalculateHotelSearchPrices(ctx context.Context, req *domain.CalculateHotelSearchPricesReq) error {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.PriceServiceEndpoint)
	if err != nil {
		return errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, req.PartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)
	client := pricePb.NewHiddenFeeServiceClient(conn)

	protoHotelSummaries, domainHotelSummaries := converts.FromDomainHotelSummaryMap(req.HotelSummaries)
	for key, value := range protoHotelSummaries {
		log.Info("CalculateHotelSearchPrices", log.Any("key", key), log.Any("value", value.Price))
	}

	res, err := client.CalculateHotelSearchPrices(newCtx, &pricePb.CalculateHotelSearchPricesReq{
		RequestContext: &price.PricingRequestContext{
			SourceService: "HOTEL_HUB",
		},
		Multiplier:     req.Multiplier,
		OfficeId:       req.OfficeID,
		HotelSummaries: protoHotelSummaries,
		Provider:       converts.ToPriceHotelProvider(req.Provider),
	})
	if err != nil {
		return errors.Wrap(err, "CalculateHotelSearchPrices")
	}

	if res == nil {
		return nil
	}

	if !res.IsSuccess {
		return errors.New(res.ErrorCode)
	}

	if len(res.HotelSummaries) > 0 {
		// for key, value := range res.HotelSummaries {
		// 	log.Info("CalculateHotelSearchPrices res", log.Any("key", key), log.Any("value", value.Price))
		// }

		req.HotelSummaries = updateHotelPrices(domainHotelSummaries, res.HotelSummaries)
	}

	return nil
}

func (s priceClient) CalculateHotelDetailPrices(ctx context.Context, req *domain.CalculateHotelDetailPricesReq) error {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.PriceServiceEndpoint)
	if err != nil {
		return errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, req.PartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)
	client := pricePb.NewHiddenFeeServiceClient(conn)

	protoHubHotel, domainHubRoom := converts.FromDomainHubHotelDetail(req.HubHotel)

	log.Info("CalculateHotelDetailPrices req", log.Any("OfficeID", req.OfficeID), log.Any("Provider", req.Provider))

	res, err := client.CalculateHotelDetailPrices(newCtx, &pricePb.CalculateHotelDetailPricesReq{
		RequestContext: &price.PricingRequestContext{
			SourceService: "HOTEL_HUB",
		},
		Multiplier:  req.Multiplier,
		OfficeId:    req.OfficeID,
		HotelDetail: protoHubHotel,
		Provider:    converts.ToPriceHotelProvider(req.Provider),
	})
	if err != nil {
		return errors.Wrap(err, "CalculateHotelDetailPrices")
	}

	if res == nil {
		return nil
	}

	if !res.IsSuccess {
		return errors.New(res.ErrorCode)
	}

	if res.HotelDetail != nil {
		updateHotelDetail(res.HotelDetail, domainHubRoom)
	}

	return nil
}

func (s priceClient) CalculateHotelRateDataPrices(ctx context.Context, req *domain.CalculateHotelRateDataPricesReq) error {
	conn, err := tracingGRPC.DialWithTrace(ctx, s.cfg.PriceServiceEndpoint)
	if err != nil {
		return errors.Wrap(err, "DialWithTrace")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(s.cfg.InternalSecretToken, req.PartnershipID)
	newCtx := metadata.NewOutgoingContext(ctx, md)
	client := pricePb.NewHiddenFeeServiceClient(conn)

	protoHubRateData := converts.FromDomainHubRateData(req.HubRateData)
	log.Info("CalculateHotelRateDataPrices req", log.Any("OfficeID", req.OfficeID), log.Any("Provider", req.Provider))

	res, err := client.CalculateHotelRateDataPrices(newCtx, &pricePb.CalculateHotelRateDataPricesReq{
		RequestContext: &price.PricingRequestContext{
			SourceService: "HOTEL_HUB",
		},
		Multiplier:   req.Multiplier,
		OfficeId:     req.OfficeID,
		RateData:     protoHubRateData,
		Rating:       req.Rating,
		CountryCode:  req.CountryCode,
		CategoryType: req.CategoryType,
		HotelId:      req.HotelID,
		Provider:     converts.ToPriceHotelProvider(req.Provider),
	})
	if err != nil {
		return errors.Wrap(err, "CalculateHotelRateDataPrices")
	}

	if res == nil {
		return nil
	}

	if !res.IsSuccess {
		return errors.New(res.ErrorCode)
	}

	if res.RateData != nil {
		updateHubRateData(res.RateData, req.HubRateData)
	}

	return nil
}

func applyHiddenFeeToCancelPolicies(policies []*domain.HubCancelPolicy, payNow float64) {
	for _, policy := range policies {
		if !policy.Refundable {
			policy.PenaltyAmount = payNow
		}
	}
}

func syncPriceServicePolicy(psPolicies []*price.CancelPolicy, dPolicies []*domain.HubCancelPolicy, hiddenFee, discount float64) {
	pricePolicyRes := converts.ToDomainHubCancelPolicies(psPolicies)
	pricePolicyMap := map[string]*domain.HubCancelPolicy{}
	isError := false

	for _, item := range pricePolicyRes {
		if item == nil || item.Key == "" {
			log.Error("syncPriceServicePolicy price-service missing policy key", log.Any("item", item))
			isError = true

			continue
		}

		pricePolicyMap[item.Key] = item
	}

	for _, item := range dPolicies {
		if item.Key == "" {
			item.Key = item.GenKey()
		}

		priceRes := pricePolicyMap[item.Key]
		if priceRes != nil {
			item.PenaltyAmount = priceRes.PenaltyAmount
		} else {
			isError = true
		}
	}

	// handle Calc manual if missing key or error
	// TODO Need update logic calc when change in price-service
	if isError {
		log.Info("syncPriceServicePolicy price-service err. Calc penalty_amount manual")

		for _, item := range dPolicies {
			item.PenaltyAmount += hiddenFee + discount
		}
	}
}

func updateHubRateData(priceHubRateData *price.RateData, domainHubRateData *domain.HubRateData) {
	domainHubRateData.OccupancyRate = converts.ToDomainHubOccupancyRates(priceHubRateData.OccupancyRate, domainHubRateData.OccupancyRate)
	domainHubRateData.PayNow = priceHubRateData.PayNow
	domainHubRateData.TotalRateBasic = priceHubRateData.TotalRateBasic
	domainHubRateData.TotalTaxAmount = priceHubRateData.TotalTaxAmount
	domainHubRateData.TotalRateAmount = priceHubRateData.TotalRateBasic + priceHubRateData.TotalTaxAmount
	domainHubRateData.DiscountAmount = priceHubRateData.DiscountAmount
	domainHubRateData.HiddenFeeAmount = priceHubRateData.HiddenFeeAmount
	domainHubRateData.AppliedHiddenFee = priceHubRateData.AppliedHiddenFee
	domainHubRateData.AppliedDiscount = priceHubRateData.AppliedDiscount
	syncPriceServicePolicy(priceHubRateData.CancelPolicy, domainHubRateData.CancelPolicies, domainHubRateData.HiddenFeeAmount, domainHubRateData.DiscountAmount)
	applyHiddenFeeToCancelPolicies(domainHubRateData.CancelPolicies, priceHubRateData.PayNow)
}

func updateHotelDetail(priceHotelDetail *price.HotelDetail, domainHubRoom map[string]*domain.HubRoom) {
	for key, room := range priceHotelDetail.Rooms {
		if hubRoom, ok := domainHubRoom[key]; ok {
			for _, rateData := range hubRoom.RateData {
				priceRate := room.Rates[rateData.RateID]
				rateData.OccupancyRate = converts.ToDomainHubOccupancyRates(priceRate.OccupancyRate, rateData.OccupancyRate)
				rateData.HiddenFeeAmount = priceRate.HiddenFeeAmount
				rateData.DiscountAmount = priceRate.DiscountAmount
				syncPriceServicePolicy(priceRate.CancelPolicy, rateData.CancelPolicies, rateData.HiddenFeeAmount, rateData.DiscountAmount)
				applyHiddenFeeToCancelPolicies(rateData.CancelPolicies, priceRate.PayNow)

				rateData.PayNow = priceRate.PayNow
				rateData.TotalRateBasic = priceRate.TotalRateBasic
				rateData.TotalTaxAmount = priceRate.TotalTaxAmount
				rateData.TotalRateAmount = priceRate.TotalRateBasic + priceRate.TotalTaxAmount

				rateData.AppliedHiddenFee = priceRate.AppliedHiddenFee
				rateData.AppliedDiscount = priceRate.AppliedDiscount
			}
		}
	}
}

func updateHotelPrices(hotelSummaries map[string]*domain.HotelSummary, priceData map[string]*price.HotelSummary) []*domain.HotelSummary {
	var wg sync.WaitGroup
	results := sync.Map{}

	numWorkers := 5
	jobs := make(chan string, len(priceData))

	for i := 0; i < numWorkers; i++ {
		wg.Add(1)

		go func() {
			defer wg.Done()

			for id := range jobs {
				if hotel, ok := hotelSummaries[id]; ok {
					if priceHotel, ok := priceData[id]; ok && priceHotel.Price != nil {
						hotel.Price.Total = priceHotel.Price.TotalPrice
						hotel.Price.HiddenFee = priceHotel.Price.HiddenFeeAmount
						hotel.Price.PricePerNight = &domain.PricePerNight{
							DiscountPrice: priceHotel.Price.DiscountPrice,
							OriginalPrice: priceHotel.Price.OriginalPrice,
						}
						hotel.Price.AppliedDiscount = priceHotel.Price.AppliedDiscount
						hotel.Price.AppliedHiddenFee = priceHotel.Price.AppliedHiddenFee
					}

					results.Store(id, hotel)
				}
			}
		}()
	}

	for id := range priceData {
		jobs <- id
	}

	close(jobs)

	wg.Wait()
	var hotels []*domain.HotelSummary
	results.Range(func(_, value interface{}) bool {
		hotels = append(hotels, value.(*domain.HotelSummary))
		return true
	})

	return hotels
}
