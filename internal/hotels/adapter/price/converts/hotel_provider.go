package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/price"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func ToPriceHotelProvider(in commonEnum.HotelProvider) price.HotelProvider {
	mappedValue := price.HotelProvider(in)

	if _, exists := commonEnum.HotelProviderName[in]; !exists {
		return price.HotelProvider_HotelProviderAll
	}

	return mappedValue
}

func ToEnumHotelProvider(in price.HotelProvider) commonEnum.HotelProvider {
	mappedValue := commonEnum.HotelProvider(in)

	if str := commonEnum.HotelProviderName[mappedValue]; str == "" {
		return commonEnum.HotelProviderNone
	}

	return mappedValue
}
