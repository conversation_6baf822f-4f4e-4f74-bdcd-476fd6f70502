package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/price"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func ToDomainHiddenServiceFee(fee *price.HiddenFee) *domain.HiddenServiceFee {
	if fee == nil {
		return nil
	}

	return &domain.HiddenServiceFee{
		OfficeID: fee.OfficeId,
		Type:     enum.HiddenFeeType(fee.Type),
		Config: &domain.HiddenServiceFeeConfig{
			LocationType: enum.HotelLocationType(fee.Location),
			CountryCode:  fee.CountryCode,
			Rating:       fee.Rating,
			HotelType:    fee.AccommodationType,
		},
		Provider:  ToEnumHotelProvider(fee.Provider),
		HotelID:   fee.HotelId,
		HotelName: fee.HotelName,
		Amount:    fee.Amount,
		Percent:   fee.Percent,
	}
}

func ToDomainHiddenServiceFees(fees []*price.HiddenFee) []*domain.HiddenServiceFee {
	if len(fees) == 0 {
		return nil
	}

	result := make([]*domain.HiddenServiceFee, 0, len(fees))
	for _, fee := range fees {
		result = append(result, ToDomainHiddenServiceFee(fee))
	}

	return result
}

func FromDomainCancelPolicies(info []*domain.HubCancelPolicy) []*price.CancelPolicy {
	if len(info) == 0 {
		return nil
	}

	var result []*price.CancelPolicy
	for _, item := range info {
		result = append(result, FromDomainCancelPolicy(item))
	}

	return result
}

func FromDomainCancelPolicy(info *domain.HubCancelPolicy) *price.CancelPolicy {
	if info == nil {
		return nil
	}

	key := info.GenKey()
	if key == "" {
		log.Error("FromDomainCancelPolicy GenKey err")
		return nil
	}

	res := &price.CancelPolicy{
		Amount:                  info.PenaltyAmount,
		StartDate:               info.StartDate,
		EndDate:                 info.EndDate,
		Currency:                info.Currency,
		NonrefundableDateRanges: FromDomainNonRefundableDateRanges(info.NonrefundableDateRange),
		CancelKey:               key,
	}

	return res
}

// HOTEL SUMMARY.
func FromDomainHotelSummaryMap(info []*domain.HotelSummary) (map[string]*price.HotelSummary, map[string]*domain.HotelSummary) {
	res := make(map[string]*price.HotelSummary)
	hotelSummaries := make(map[string]*domain.HotelSummary)

	for _, item := range info {
		res[item.ID] = FromDomainHotelSummary(item)
		hotelSummaries[item.ID] = item
	}

	return res, hotelSummaries
}

func FromDomainHotelSummary(info *domain.HotelSummary) *price.HotelSummary {
	if info == nil {
		return nil
	}

	return &price.HotelSummary{
		Id:           info.ID,
		Rating:       info.Rating,
		CountryCode:  info.CountryCode,
		CategoryType: info.CategoryType,
		Price:        FromDomainHotelSummaryPrice(info.Price),
	}
}

func FromDomainHotelSummaryPrice(info *domain.Price) *price.HotelSummaryPrice {
	if info == nil {
		return nil
	}

	result := &price.HotelSummaryPrice{
		TotalPrice: info.Total,
		Currency:   info.Currency,
	}

	if info.PricePerNight != nil {
		result.DiscountPrice = info.PricePerNight.DiscountPrice
		result.OriginalPrice = info.PricePerNight.OriginalPrice
	}

	return result
}

// HOTEL DETAIL

// func FromDomainHubHotel(info []*domain.HubHotel) (map[string]*price.HotelDetail, map[string]*domain.HubHotel) {
// 	if info == nil {
// 		return nil, nil
// 	}

// 	res := make(map[string]*price.HotelDetail)
// 	hubHotels := make(map[string]*domain.HubHotel)
// 	for _, item := range info {
// 		res[item.HotelID] = FromDomainHubHotelDetail(item)
// 		hubHotels[item.HotelID] = item
// 	}
// 	return res, hubHotels
// }

func FromDomainHubHotelDetail(info *domain.HubHotel) (*price.HotelDetail, map[string]*domain.HubRoom) {
	if info == nil {
		return nil, nil
	}

	rooms, hubRooms := FromDomainHubHotelRooms(info.ListRooms)
	result := &price.HotelDetail{
		Id:           info.HotelID,
		Rating:       info.Rating,
		CategoryType: info.HotelType,
		Provider:     ToPriceHotelProvider(commonEnum.HotelProviderValue[info.ProviderHotelID]),
		Rooms:        rooms,
	}

	if info.Address != nil {
		result.CountryCode = info.Address.CountryCode
	}

	return result, hubRooms
}

func FromDomainHubHotelRooms(info []*domain.HubRoom) (map[string]*price.HotelRoom, map[string]*domain.HubRoom) {
	if len(info) == 0 {
		return nil, nil
	}
	result := make(map[string]*price.HotelRoom)
	hubRooms := make(map[string]*domain.HubRoom)

	for _, item := range info {
		result[item.RoomID] = FromDomainHubHotelRoom(item)
		hubRooms[item.RoomID] = item
	}

	return result, hubRooms
}

func FromDomainHubHotelRoom(info *domain.HubRoom) *price.HotelRoom {
	if info == nil {
		return nil
	}

	return &price.HotelRoom{
		RoomId: info.RoomID,
		Rates:  FromDomainHubHotelRoomRates(info.RateData),
	}
}

func FromDomainHubHotelRoomRates(info []*domain.HubRateData) map[string]*price.RateData {
	if len(info) == 0 {
		return nil
	}
	result := make(map[string]*price.RateData)

	for _, item := range info {
		if item != nil {
			result[item.RateID] = FromDomainHubRateData(item)
		}
	}

	return result
}

func FromDomainHubRateData(info *domain.HubRateData) *price.RateData {
	if info == nil {
		return nil
	}

	return &price.RateData{
		RateId:        info.RateID,
		OccupancyRate: FromDomainHubRateDataOccupancyRates(info.OccupancyRate),
		CancelPolicy:  FromDomainCancelPolicies(info.CancelPolicies),
		PayNow:        info.PayNow,
	}
}

func FromDomainNonRefundableDateRanges(info []*domain.HubNonrefundableDateRange) []*price.NonrefundableDateRange {
	if len(info) == 0 {
		return nil
	}

	var result []*price.NonrefundableDateRange
	for _, item := range info {
		result = append(result, &price.NonrefundableDateRange{
			StartDate: item.StartDate,
			EndDate:   item.EndDate,
		})
	}

	return result
}

func FromDomainHubRateDataOccupancyRates(info []*domain.HubOccupancyRate) []*price.OccupancyRate {
	if len(info) == 0 {
		return nil
	}

	var result []*price.OccupancyRate
	for _, item := range info {
		result = append(result, FromDomainHubRateDataOccupancyRate(item))
	}

	return result
}

func FromDomainHubRateDataOccupancyRate(info *domain.HubOccupancyRate) *price.OccupancyRate {
	if info == nil {
		return nil
	}

	return &price.OccupancyRate{
		OccupancyType:    info.OccupancyType,
		RoomQuantity:     uint32(info.RoomQuantity),
		TotalNightlyRate: FromDomainHubRates(info.TotalNightlyRate),
		RateTaxes:        FromDomainHubRateTaxes(info.RateTaxes),
		Surcharges:       info.Surcharges,
		PayAtHotel:       FromDomainPayAtHotels(info.PayAtHotel),
		RateDiscounts:    FromDomainHubRateDiscounts(info.RateDiscounts),
	}
}

func FromDomainHubRateDiscounts(info []*domain.HubRateDiscount) []*price.RateDiscount {
	if len(info) == 0 {
		return nil
	}

	var result []*price.RateDiscount
	for _, item := range info {
		result = append(result, FromDomainHubRateDiscount(item))
	}

	return result
}

func FromDomainHubRateDiscount(info *domain.HubRateDiscount) *price.RateDiscount {
	if info == nil {
		return nil
	}

	return &price.RateDiscount{
		Amount:   info.Amount,
		Code:     info.Code,
		Name:     info.Name,
		Currency: info.Currency,
	}
}

func FromDomainPayAtHotels(info []*domain.PayAtHotel) []*price.PayAtHotel {
	if len(info) == 0 {
		return nil
	}

	var result []*price.PayAtHotel
	for _, item := range info {
		result = append(result, FromDomainPayAtHotel(item))
	}

	return result
}

func FromDomainPayAtHotel(info *domain.PayAtHotel) *price.PayAtHotel {
	if info == nil {
		return nil
	}

	return &price.PayAtHotel{
		Amount:      info.Amount,
		Description: info.Description,
		Currency:    info.Currency,
	}
}

func FromDomainHubRateTaxes(info []*domain.HubRateTax) []*price.RateTax {
	if len(info) == 0 {
		return nil
	}

	var result []*price.RateTax
	for _, item := range info {
		result = append(result, FromDomainHubRateTax(item))
	}

	return result
}

func FromDomainHubRateTax(info *domain.HubRateTax) *price.RateTax {
	if info == nil {
		return nil
	}
	res := &price.RateTax{
		Currency:    info.Currency,
		Included:    info.Included,
		Type:        uint32(info.Type),
		Description: info.Description,
	}

	if info.Amount != nil {
		res.Amount = *info.Amount
	}

	return res
}

func FromDomainHubRates(info []*domain.HubRate) []*price.Rate {
	if len(info) == 0 {
		return nil
	}

	var result []*price.Rate
	for _, item := range info {
		result = append(result, FromDomainHubRate(item))
	}

	return result
}

func FromDomainHubRate(info *domain.HubRate) *price.Rate {
	if info == nil {
		return nil
	}

	return &price.Rate{
		RateAmount: info.RateAmount,
		RateBasic:  info.RateBasic,
		TaxAmount:  info.TaxAmount,
		Currency:   info.Currency,
	}
}
