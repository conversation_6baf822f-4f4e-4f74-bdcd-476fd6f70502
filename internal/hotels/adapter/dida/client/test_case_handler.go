package client

import (
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	didaConstants "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

// and returns a simulated error response if it does.
func CheckPriceSearchTestCase(req *entities.PriceSearchReq) (*entities.PriceSearchRes, bool) {
	if req == nil || req.TestingHashKey == "" {
		return nil, false
	}

	didaErrorCode, exists := constants.TestCaseMap[commonEnum.HotelProviderDida][constants.ActionCreateBooking][req.TestingHashKey]
	if !exists {
		log.Warn("Unknown test case key for PriceSearch", log.String("key", req.TestingHash<PERSON>ey))
		return nil, false
	}

	// Return a simulated error response
	log.Info("Simulating error for PriceSearch", log.String("error_code", string(didaErrorCode)))

	return &entities.PriceSearchRes{
		Error: &entities.Error{
			Code: didaConstants.DidaErrorCode(didaErrorCode),
		},
	}, true
}

// and returns a simulated error response if it does.
func CheckPriceConfirmTestCase(req *entities.PriceConfirmReq) (*entities.PriceConfirmRes, bool) {
	if req == nil || req.TestingHashKey == "" {
		return nil, false
	}

	didaErrorCode, exists := constants.TestCaseMap[commonEnum.HotelProviderDida][constants.ActionCreateBooking][req.TestingHashKey]
	if !exists {
		log.Warn("Unknown test case key for PriceSearch", log.String("key", req.TestingHashKey))
		return nil, false
	}

	// Return a simulated error response
	log.Info("Simulating error for PriceConfirm", log.String("error_code", string(didaErrorCode)))

	return &entities.PriceConfirmRes{
		Error: &entities.Error{
			Code:    didaConstants.DidaErrorCode(didaErrorCode),
			Message: string(didaErrorCode),
		},
	}, true
}

// and returns a simulated error response if it does.
func CheckBookingConfirmTestCase(req *entities.BookingConfirmReq) (*entities.BookingConfirmRes, bool) {
	if req == nil || req.TestingHashKey == "" {
		return nil, false
	}

	didaErrorCode, exists := constants.TestCaseMap[commonEnum.HotelProviderDida][constants.ActionCreateBooking][req.TestingHashKey]
	if !exists {
		log.Warn("Unknown test case key for PriceSearch", log.String("key", req.TestingHashKey))
		return nil, false
	}

	// Return a simulated error response
	log.Info("Simulating error for BookingConfirm", log.String("error_code", string(didaErrorCode)))

	return &entities.BookingConfirmRes{
		Error: &entities.Error{
			Code:    didaConstants.DidaErrorCode(didaErrorCode),
			Message: string(didaErrorCode),
		},
	}, true
}

// and returns a simulated error response if it does.
func CheckBookingSearchTestCase(req *entities.BookingSearchReq) (*entities.BookingSearchRes, bool) {
	if req == nil || req.TestingHashKey == "" {
		return nil, false
	}

	didaErrorCode, exists := constants.TestCaseMap[commonEnum.HotelProviderDida][constants.ActionOrderBookingFinishStatus][req.TestingHashKey]
	if !exists {
		log.Warn("Unknown test case key for PriceSearch", log.String("key", req.TestingHashKey))
		return nil, false
	}

	// Return a simulated error response
	log.Info("Simulating error for BookingSearch", log.String("error_code", string(didaErrorCode)))

	return &entities.BookingSearchRes{
		Error: &entities.Error{
			Code:    didaConstants.DidaErrorCode(didaErrorCode),
			Message: string(didaErrorCode),
		},
	}, true
}

// and returns a simulated error response if it does.
func CheckPreCancelBookingTestCase(req *entities.PreCancelBookingReq) (*entities.PreCancelBookingRes, bool) {
	if req == nil || req.TestingHashKey == "" {
		return nil, false
	}

	didaErrorCode, exists := constants.TestCaseMap[commonEnum.HotelProviderDida][constants.ActionCreateBooking][req.TestingHashKey]
	if !exists {
		log.Warn("Unknown test case key for PriceSearch", log.String("key", req.TestingHashKey))
		return nil, false
	}

	// Return a simulated error response
	log.Info("Simulating error for PreCancelBooking", log.String("error_code", string(didaErrorCode)))

	return &entities.PreCancelBookingRes{
		Error: &entities.Error{
			Code:    didaConstants.DidaErrorCode(didaErrorCode),
			Message: string(didaErrorCode),
		},
	}, true
}

// and returns a simulated error response if it does.
func CheckCancelBookingTestCase(req *entities.CancelBookingReq) (*entities.CancelBookingRes, bool) {
	if req == nil || req.TestingHashKey == "" {
		return nil, false
	}

	didaErrorCode, exists := constants.TestCaseMap[commonEnum.HotelProviderDida][constants.ActionCreateBooking][req.TestingHashKey]
	if !exists {
		log.Warn("Unknown test case key for PriceSearch", log.String("key", req.TestingHashKey))
		return nil, false
	}
	// Return a simulated error response
	log.Info("Simulating error for CancelBooking", log.String("error_code", string(didaErrorCode)))

	return &entities.CancelBookingRes{
		Error: &entities.Error{
			Code: didaConstants.DidaErrorCode(didaErrorCode),
		},
	}, true
}
