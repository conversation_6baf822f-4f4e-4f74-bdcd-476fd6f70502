// Package client provides a client for interacting with the Dida hotel booking API.
package client

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/url"
	"reflect"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

const (
	// HTTP status codes.
	successfulCode = http.StatusOK

	// HTTP methods.
	methodGet  = http.MethodGet
	methodPost = http.MethodPost

	// API paths.
	pathPriceSearch      = "rate/pricesearch"
	pathPriceConfirm     = "rate/priceconfirm"
	pathBookingConfirm   = "booking/hotelbookingconfirm"
	pathBookingSearch    = "booking/hotelbookingsearch"
	pathPreCancelBooking = "booking/hotelbookingcancel"
	pathCancelBooking    = "booking/hotelbookingcancelconfirm"
)

type DidaClient interface {
	PriceSearch(ctx context.Context, req *entities.PriceSearchReq, tracingID string) (res *entities.PriceSearchRes, err error)
	PriceConfirm(ctx context.Context, req *entities.PriceConfirmReq, tracingID string) (res *entities.PriceConfirmRes, err error)
	BookingConfirm(ctx context.Context, req *entities.BookingConfirmReq, tracingID string) (res *entities.BookingConfirmRes, err error)
	BookingSearch(ctx context.Context, req *entities.BookingSearchReq, tracingID string) (res *entities.BookingSearchRes, err error)
	PreCancelBooking(ctx context.Context, req *entities.PreCancelBookingReq, tracingID string) (res *entities.PreCancelBookingRes, err error)
	CancelBooking(ctx context.Context, req *entities.CancelBookingReq, tracingID string) (res *entities.CancelBookingRes, err error)
}

type didaClient struct {
	env         string
	baseURL     string
	username    string
	password    string
	requestRepo repositories.RequestRepository
}

func NewDidaClient(cfg *config.Schema, repo repositories.RequestRepository) DidaClient {
	return &didaClient{
		env:         cfg.Env,
		baseURL:     cfg.DidaAPIURL,
		username:    cfg.DidaBasicAuthUsename,
		password:    cfg.DidaBasicAuthPassword,
		requestRepo: repo,
	}
}

func (c *didaClient) getRequestHeader() *entities.Header {
	return &entities.Header{
		ClientID:   c.username,
		LicenseKey: c.password,
	}
}

func getHeader() map[string]string {
	m := make(map[string]string)
	m["Content-Type"] = "application/json"

	return m
}

func (c *didaClient) hideClientInfoRequest(body interface{}) {
	stype := reflect.ValueOf(body).Elem()
	field := stype.FieldByName("Header")
	field.Set(reflect.Zero(field.Type()))
}

// PriceSearch sends a price search request to the Dida API.
// It returns price information for hotels based on the provided search criteria.
func (c *didaClient) PriceSearch(ctx context.Context, req *entities.PriceSearchReq, tracingID string) (res *entities.PriceSearchRes, err error) {
	if req == nil {
		err = domain.ErrInvalidValue
		return
	}

	req.Header = c.getRequestHeader()

	res = &entities.PriceSearchRes{}

	if err = c.doRequest(ctx, methodPost, pathPriceSearch, getHeader(), req, tracingID, res); err != nil {
		log.Error("DidaClient PriceSearch doRequest", log.Any("Error:", err))
		err = errors.Wrap(err, "PriceSearch")

		return
	}

	return
}

// PriceConfirm sends a price confirmation request to the Dida API.
// It confirms the price for a specific hotel and rate plan.
func (c *didaClient) PriceConfirm(ctx context.Context, req *entities.PriceConfirmReq, tracingID string) (res *entities.PriceConfirmRes, err error) {
	if req == nil {
		err = domain.ErrInvalidValue
		return
	}

	req.Header = c.getRequestHeader()

	res = &entities.PriceConfirmRes{}

	if err = c.doRequest(ctx, methodPost, pathPriceConfirm, getHeader(), req, tracingID, res); err != nil {
		log.Error("DidaClient PriceConfirm doRequest", log.Any("Error:", err))
		err = errors.Wrap(err, "PriceConfirm")

		return
	}

	return
}

// BookingConfirm sends a booking confirmation request to the Dida API.
// It confirms a booking with the provided details.
func (c *didaClient) BookingConfirm(ctx context.Context, req *entities.BookingConfirmReq, tracingID string) (res *entities.BookingConfirmRes, err error) {
	if req == nil {
		err = domain.ErrInvalidValue
		return
	}

	// Check if this is a test case request
	if testRes, isTestCase := CheckBookingConfirmTestCase(req); isTestCase {
		return testRes, nil
	}

	req.Header = c.getRequestHeader()

	res = &entities.BookingConfirmRes{}

	if err = c.doRequest(ctx, methodPost, pathBookingConfirm, getHeader(), req, tracingID, res); err != nil {
		log.Error("DidaClient BookingConfirm doRequest", log.Any("Error:", err))
		err = errors.Wrap(err, "BookingConfirm")

		return
	}

	return
}

// BookingSearch sends a booking search request to the Dida API.
// It searches for bookings based on the provided criteria.
func (c *didaClient) BookingSearch(ctx context.Context, req *entities.BookingSearchReq, tracingID string) (res *entities.BookingSearchRes, err error) {
	if req == nil {
		err = domain.ErrInvalidValue
		return
	}

	// Check if this is a test case request
	if testRes, isTestCase := CheckBookingSearchTestCase(req); isTestCase {
		return testRes, nil
	}

	req.Header = c.getRequestHeader()

	res = &entities.BookingSearchRes{}

	if err = c.doRequest(ctx, methodPost, pathBookingSearch, getHeader(), req, tracingID, res); err != nil {
		log.Error("DidaClient BookingSearch doRequest", log.Any("Error:", err))
		err = errors.Wrap(err, "BookingSearch")

		return
	}

	return
}

// PreCancelBooking sends a pre-cancellation request to the Dida API.
// It initiates the cancellation process for a booking.
func (c *didaClient) PreCancelBooking(ctx context.Context, req *entities.PreCancelBookingReq, tracingID string) (res *entities.PreCancelBookingRes, err error) {
	if req == nil {
		err = domain.ErrInvalidValue
		return
	}

	// Check if this is a test case request
	if testRes, isTestCase := CheckPreCancelBookingTestCase(req); isTestCase {
		return testRes, nil
	}

	req.Header = c.getRequestHeader()

	res = &entities.PreCancelBookingRes{}

	if err = c.doRequest(ctx, methodPost, pathPreCancelBooking, getHeader(), req, tracingID, res); err != nil {
		log.Error("DidaClient PreCancelBooking doRequest", log.Any("Error:", err))
		err = errors.Wrap(err, "PreCancelBooking")

		return
	}

	return
}

// CancelBooking sends a cancellation confirmation request to the Dida API.
// It confirms the cancellation of a booking.
func (c *didaClient) CancelBooking(ctx context.Context, req *entities.CancelBookingReq, tracingID string) (res *entities.CancelBookingRes, err error) {
	if req == nil {
		err = domain.ErrInvalidValue
		return
	}

	// Check if this is a test case request
	if testRes, isTestCase := CheckCancelBookingTestCase(req); isTestCase {
		return testRes, nil
	}

	req.Header = c.getRequestHeader()

	res = &entities.CancelBookingRes{}

	if err = c.doRequest(ctx, methodPost, pathCancelBooking, getHeader(), req, tracingID, res); err != nil {
		log.Error("DidaClient CancelBooking doRequest", log.Any("Error:", err))
		err = errors.Wrap(err, "CancelBooking")

		return
	}

	return
}

// do executes an HTTP request and returns the response body, status code, and duration.
// It handles marshaling the request body, making the HTTP request, and reading the response.
func (c *didaClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	action string,
	body interface{},
) (_ []byte, _ int, duration int64, _ error) {
	beginAt := time.Now().UnixMilli()

	defer func() {
		duration = time.Now().UnixMilli() - beginAt
	}()

	jsonData, err := json.Marshal(body)
	if err != nil {
		log.Error("Dida client Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))

		return nil, 0, duration, err
	}

	var response *http.Response

	payload := bytes.NewBuffer(jsonData)

	response, err = tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode != successfulCode {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *didaClient) doRequest(
	ctx context.Context,
	method string,
	apiPath string,
	header map[string]string,
	body any,
	tracingID string,
	responseData any,
) error {
	var err error

	// Build the full URL
	fullPath, err := url.JoinPath(c.baseURL, apiPath)
	if err != nil {
		return errors.Wrap(err, "Parse base URL")
	}

	u, err := url.Parse(fullPath)
	if err != nil {
		return commonError.ErrSomethingOccurred
	}

	// Add query parameters
	q := u.Query()
	q.Set("$format", "json")
	u.RawQuery = q.Encode()

	finalUrl := u.String()

	// Execute the HTTP request
	response, statusCode, duration, err := c.do(ctx, header, finalUrl, method, apiPath, body)

	// Clone headers for logging
	headerClone := map[string]string{}
	for key, val := range header {
		headerClone[key] = val
	}

	// Use WaitGroup to ensure goroutine completes
	var wg sync.WaitGroup
	wg.Add(1)

	// Log request and response details
	go func(headerClone map[string]string, doErr error) {
		defer wg.Done()

		// Skip if repository is not available
		if c.requestRepo == nil {
			return
		}

		c.hideClientInfoRequest(body)

		path := finalUrl

		bCtx, cancel := context.WithTimeout(context.Background(), pkgConstants.RequestRepoCtxTimeout)
		defer cancel()

		requestID := uuid.NewString()

		err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     method,
			Body:       body,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     apiPath,
			Provider:   commonEnum.HotelProviderDida,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
		})
		if err != nil {
			log.Error("Dida requestRepo.Create error",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", apiPath),
				log.Any("req", body),
			)
		}
	}(headerClone, err)

	// Check for HTTP request errors
	if err != nil {
		// Wait for logging to complete before returning
		wg.Wait()
		return errors.Wrap(err, "c.do")
	}

	// Unmarshal response
	if err := json.Unmarshal(response, responseData); err != nil {
		// Wait for logging to complete before returning
		wg.Wait()
		return errors.Wrap(err, "Unmarshal")
	}

	// Wait for logging to complete before returning
	wg.Wait()

	return nil
}
