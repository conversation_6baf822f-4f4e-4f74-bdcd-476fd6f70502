//nolint:tagliatelle
package entities

import "math"

type PriceSearchReq struct {
	Header            *Header            `json:"Header"`
	HotelIDList       []int              `json:"HotelIDList"`
	CheckInDate       string             `json:"CheckInDate"`
	CheckOutDate      string             `json:"CheckOutDate"`
	IsRealTime        *IsRealTime        `json:"IsRealTime"`
	RealTimeOccupancy *RealTimeOccupancy `json:"RealTimeOccupancy"`
	IsNeedOnRequest   bool               `json:"IsNeedOnRequest"`
	Currency          string             `json:"Currency"`
	Nationality       string             `json:"Nationality"`
	TestingHashKey    string             `json:"-"`
}

type IsRealTime struct {
	Value     bool `json:"Value"`
	RoomCount int  `json:"RoomCount"`
}

type RealTimeOccupancy struct {
	AdultCount      int   `json:"AdultCount"`
	ChildCount      int   `json:"ChildCount"`
	ChildAgeDetails []int `json:"ChildAgeDetails"`
}

type PriceSearchRes struct {
	Error     *Error              `json:"Error"`
	Success   *PriceSearchSuccess `json:"Success"`
	AuditData *AuditData          `json:"AuditData"`
}

type PriceSearchSuccess struct {
	PriceDetails *PriceSearchDetails `json:"PriceDetails"`
}

type PriceSearchDetails struct {
	CheckInDate  string   `json:"CheckInDate"`
	CheckOutDate string   `json:"CheckOutDate"`
	HotelList    []*Hotel `json:"HotelList"`
}

type Hotel struct {
	HotelID                     int                           `json:"HotelID"`
	HotelName                   string                        `json:"HotelName"`
	Destination                 *Destination                  `json:"Destination"`
	TotalPrice                  float64                       `json:"TotalPrice"`
	TotalSupplement             float64                       `json:"TotalSupplement"`
	TotalPriceWithoutSupplement float64                       `json:"TotalPriceWithoutSupplement"`
	LowestPrice                 *LowestPrice                  `json:"LowestPrice"`
	RatePlanList                []*RatePlan                   `json:"RatePlanList"`
	LowestRatePlanInfo          *LowestRatePlanInfo           `json:"LowestRatePlanInfo"`
	CancellationPolicyList      []*RatePlanCancellationPolicy `json:"CancellationPolicyList"`
}

type Destination struct {
	CityCode string `json:"CityCode"`
}

type LowestPrice struct {
	Currency   string  `json:"Currency"`
	RatePlanID string  `json:"RatePlanID"`
	Value      float64 `json:"Value"`
}

type LowestRatePlanInfo struct {
	RatePlanID                     string                        `json:"RatePlanID"`
	BedType                        int                           `json:"BedType"`
	Breakfast                      int                           `json:"Breakfast"`
	MaxOccupancy                   int                           `json:"MaxOccupancy"`
	RatePlanName                   string                        `json:"RatePlanName"`
	StandardOccupancy              int                           `json:"StandardOccupancy"`
	RoomTypeID                     string                        `json:"RoomTypeID"`
	RatePlanCancellationPolicyList []*RatePlanCancellationPolicy `json:"RatePlanCancellationPolicyList"`
}

type RatePlan struct {
	RoomOccupancy                  *RoomOccupancyType            `json:"RoomOccupancy"`
	RoomTypeID                     int64                         `json:"RoomTypeID"`
	RoomName                       string                        `json:"RoomName"`
	RoomNameCN                     string                        `json:"RoomName_CN"`
	RatePlanID                     string                        `json:"RatePlanID"`
	RecommendIndex                 int                           `json:"RecommendIndex"`
	RatePlanName                   string                        `json:"RatePlanName"`
	BedType                        int                           `json:"BedType"`
	BreakfastType                  BreakfastType                 `json:"BreakfastType"`
	MaxOccupancy                   int                           `json:"MaxOccupancy"`
	InventoryCount                 int                           `json:"InventoryCount"`
	RoomStatus                     int                           `json:"RoomStatus"`
	Currency                       string                        `json:"Currency"`
	TotalPrice                     float64                       `json:"TotalPrice"`
	Supplement                     float64                       `json:"Supplement"`
	PriceWithoutSupplement         float64                       `json:"PriceWithoutSupplement"`
	PriceList                      []*HotelTypeRatePlanPriceInfo `json:"PriceList"`
	RatePlanCancellationPolicyList []*RatePlanCancellationPolicy `json:"RatePlanCancellationPolicyList"`
	StandardOccupancy              int                           `json:"StandardOccupancy"`
	IsOnRequest                    bool                          `json:"IsOnRequest"`
	IncludedFeeList                []*FeeListTypeFeeInfo         `json:"IncludedFeeList,omitempty"`
	ExcludedFeeList                []*FeeListTypeFeeInfo         `json:"ExcludedFeeList,omitempty"`
	MetaData                       string                        `json:"MetaData"`
}

func (r *RatePlan) GetTotalMealAmount() float64 {
	totalMealAmount := 0.0
	for _, price := range r.PriceList {
		totalMealAmount += float64(price.MealAmount)
	}

	return totalMealAmount
}

func (r *RatePlan) GetMinMealAmount() int {
	minMealAmount := math.MaxInt
	for _, price := range r.PriceList {
		if price.MealAmount < minMealAmount {
			minMealAmount = price.MealAmount
		}
	}

	return minMealAmount
}

type RoomOccupancyType struct {
	RoomNum         int   `json:"RoomNum"`
	AdultCount      int   `json:"AdultCount"`
	ChildCount      int   `json:"ChildCount"`
	ChildAgeDetails []int `json:"ChildAgeDetails"`
}

type HotelTypeRatePlanPriceInfo struct {
	Price          float64 `json:"Price"`
	InventoryCount int     `json:"InventoryCount"`
	StayDate       string  `json:"StayDate"`
	MealType       int     `json:"MealType"`
	MealAmount     int     `json:"MealAmount"`
}

type RatePlanCancellationPolicy struct {
	FromDate string  `json:"FromDate"`
	Amount   float64 `json:"Amount"`
}

type FeeListTypeFeeInfo struct {
	FeeTypeName string  `json:"FeeTypeName"`
	Currency    string  `json:"Currency"`
	Amount      float64 `json:"Amount"`
}

type AuditData struct {
	RequestClientIP string `json:"RequestClientIP"`
	SessionID       string `json:"SessionID"`
	RequestTime     string `json:"RequestTime"`
	ResponseTime    string `json:"ResponseTime"`
	ProcessTime     int    `json:"ProcessTime"`
}

type PriceConfirmReq struct {
	PreBook          bool               `json:"PreBook"`
	IDNeedOnRequest  bool               `json:"IDNeedOnRequest"`
	CheckInDate      string             `json:"CheckInDate"`
	CheckOutDate     string             `json:"CheckOutDate"`
	NumOfRooms       int                `json:"NumOfRooms"`
	HotelID          int                `json:"HotelID"`
	Header           *Header            `json:"Header"`
	OccupancyDetails []*OccupancyDetail `json:"OccupancyDetails"`
	Currency         string             `json:"Currency"`
	Nationality      string             `json:"Nationality"`
	RatePlanID       string             `json:"RatePlanID"`
	TestingHashKey   string             `json:"-"`
}

type OccupancyDetail struct {
	AdultCount      int   `json:"AdultCount"`
	ChildCount      int   `json:"ChildCount"`
	RoomNum         int   `json:"RoomNum"`
	ChildAgeDetails []int `json:"ChildAgeDetails"`
}

type PriceConfirmSuccess struct {
	PriceDetails PriceConfirmDetails `json:"PriceDetails"`
}

type PriceConfirmDetails struct {
	CheckInDate  string   `json:"CheckInDate"`
	CheckOutDate string   `json:"CheckOutDate"`
	ReferenceNo  string   `json:"ReferenceNo"`
	HotelList    []*Hotel `json:"HotelList"`
}

type PriceConfirmRes struct {
	Error     *Error               `json:"Error"`
	Success   *PriceConfirmSuccess `json:"Success"`
	AuditData *AuditData           `json:"AuditData"`
}
