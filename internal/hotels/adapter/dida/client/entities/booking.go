// Code generated ... DO NOT EDIT.

//nolint:tagliatelle
package entities

type BookingConfirmReq struct {
	Header          *Header  `json:"Header"`
	CheckInDate     string   `json:"CheckInDate"`
	CheckOutDate    string   `json:"CheckOutDate"`
	NumOfRooms      int      `json:"NumOfRooms"`
	GuestList       []*Guest `json:"GuestList"`
	Contact         *Contact `json:"Contact"`
	CustomerRequest string   `json:"CustomerRequest"`
	ClientReference string   `json:"ClientReference"`
	ReferenceNo     string   `json:"ReferenceNo"`
	TestingHashKey  string   `json:"-"`
}

type Guest struct {
	RoomNum   int          `json:"RoomNum"`
	GuestInfo []*GuestInfo `json:"GuestInfo"`
}

type GuestInfo struct {
	Name    *Name  `json:"Name"`
	IsAdult bool   `json:"IsAdult"`
	Gender  string `json:"Gender"`
	Phone   string `json:"Phone"`
	Address string `json:"Address"`
	Email   string `json:"Email"`
	Age     int    `json:"Age"`
}

type Name struct {
	Last  string `json:"Last"`
	First string `json:"First"`
}

type Contact struct {
	Name  *Name  `json:"Name"`
	Email string `json:"Email"`
	Phone string `json:"Phone"`
}

type BookingConfirmRes struct {
	Error     *Error          `json:"Error"`
	Success   *BookingSuccess `json:"Success"`
	AuditData *AuditData      `json:"AuditData"`
}

type BookingSuccess struct {
	BookingDetails *BookingDetails `json:"BookingDetails"`
}

type BookingDetails struct {
	BookingID        string        `json:"BookingID"`
	Status           BookingStatus `json:"Status"`
	CheckInDate      string        `json:"CheckInDate"`
	CheckOutDate     string        `json:"CheckOutDate"`
	OrderDate        string        `json:"OrderDate"`
	NumOfRooms       int           `json:"NumOfRooms"`
	TotalPrice       float64       `json:"TotalPrice"`
	GuestList        []*Guest      `json:"GuestList"`
	Contact          *Contact      `json:"Contact"`
	CustomerRequest  string        `json:"CustomerRequest"`
	ClientReference  string        `json:"ClientReference"`
	ConfirmationCode string        `json:"ConfirmationCode"`
	Hotel            *Hotel        `json:"Hotel"`
}

type BookingSearchReq struct {
	Header         *Header   `json:"Header"`
	SearchBy       *SearchBy `json:"SearchBy"`
	TestingHashKey string    `json:"-"`
}

type SearchBy struct {
	BookingInfo *BookingInfo `json:"BookingInfo"`
}

type BookingInfo struct {
	ClientReference string `json:"ClientReference"`
}
type BookingSearchSuccess struct {
	BookingDetailsList []*BookingDetails `json:"BookingDetailsList"`
}

type BookingSearchRes struct {
	Error     *Error                `json:"Error"`
	Success   *BookingSearchSuccess `json:"Success"`
	AuditData *AuditData            `json:"AuditData"`
}
