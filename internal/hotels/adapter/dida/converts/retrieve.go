package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func ToBookingSearchReq(orderCode string) *entities.BookingSearchReq {
	return &entities.BookingSearchReq{
		SearchBy: &entities.SearchBy{
			BookingInfo: &entities.BookingInfo{
				ClientReference: orderCode,
			},
		},
	}
}

func ToHubRetrieveConfirmationID(hubRoom *domain.HubOrderRoomItem, bookingStatus enum.BookingStatus, hotelConfirmationID, confirmationID string) *domain.HubRetrieveConfirmationID {
	if hubRoom == nil {
		return nil
	}

	return &domain.HubRetrieveConfirmationID{
		ProviderRoomID:      hubRoom.ProviderRoomID,
		ConfirmationID:      confirmationID,
		HotelConfirmationID: hotelConfirmationID,
		OccupancyType:       hubRoom.OccupancyType,
		GivenName:           hubRoom.GivenName,
		Surname:             hubRoom.Surname,
		BedOptionID:         hubRoom.BedOption.OptionID,
		BookStatus:          bookingStatus,
		Used:                false,
	}
}

func ToBookingStatusBooking(in entities.BookingStatus) enum.BookingStatus {
	switch in {
	case entities.BookingStatusFailed:
		return enum.BookingStatusFailed
	case entities.BookingStatusCanceled:
		return enum.BookingStatusCancel
	case entities.BookingStatusConfirmed:
		return enum.BookingStatusSuccess
	default:
		return enum.BookingStatusPending
	}
}
