package converts

import (
	"strconv"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func ToDidaSearchHotelReq(in *domain.SearchAdapterReq, env string) *entities.PriceSearchReq {
	if in == nil || in.HubRequest == nil || in.HubRequest.Stay.CheckIn == "" || in.HubRequest.Stay.CheckOut == "" {
		return nil
	}

	hotelIDs := helpers.ConvertHotelIDs(in.ProviderHotelIds)

	adultCount, childCount, childAges := helpers.ProcessOccupancies(in.HubRequest.Occupancies)

	result := &entities.PriceSearchReq{
		HotelIDList:       hotelIDs,
		CheckInDate:       in.HubRequest.Stay.CheckIn,
		CheckOutDate:      in.HubRequest.Stay.CheckOut,
		IsRealTime:        helpers.CreateRealTimeInfo(in.HubRequest.Stay.RoomCount),
		RealTimeOccupancy: helpers.CreateRealTimeOccupancy(adultCount, childCount, childAges),
		IsNeedOnRequest:   true,
		Currency:          helpers.GetDefaultCurrency(env),
		Nationality:       "VN",
	}

	return result
}

func ToHotelSummary(hotel *entities.Hotel) *domain.HotelSummary {
	if hotel == nil {
		return nil
	}

	hotelSummary := &domain.HotelSummary{
		ProviderHotelID: strconv.Itoa(hotel.HotelID),
		Provider:        commonEnum.HotelProviderDida,
		MatchKey:        commonEnum.HotelProviderToHash(commonEnum.HotelProviderDida),
		Name:            hotel.HotelName,
		Available:       len(hotel.RatePlanList) > 0,
	}

	return hotelSummary
}

func ToDomainPriceFromRatePlan(ratePlan *entities.RatePlan, dayCount, roomCount int) *domain.Price {
	if ratePlan == nil {
		return nil
	}

	var totalNightPrice float64
	for _, price := range ratePlan.PriceList {
		totalNightPrice += price.Price
	}

	var totalIncludedFees float64

	if ratePlan.IncludedFeeList != nil {
		for _, fee := range ratePlan.IncludedFeeList {
			totalIncludedFees += fee.Amount
		}
	}

	pricePerNight := max((totalNightPrice-totalIncludedFees)/float64(dayCount), 0)

	return &domain.Price{
		PricePerNight: &domain.PricePerNight{
			DiscountPrice: pricePerNight,
			OriginalPrice: pricePerNight,
		},
		Total:        ratePlan.TotalPrice * float64(roomCount),
		Currency:     ratePlan.Currency,
		IsIncludeTax: true,
	}
}

func ToDomainHotelSummaries(res *entities.PriceSearchRes, dayCount, roomCount int) []*domain.HotelSummary {
	if res == nil || res.Success == nil || res.Success.PriceDetails == nil {
		return nil
	}

	hotelList := res.Success.PriceDetails.HotelList
	result := make([]*domain.HotelSummary, 0, len(hotelList))

	for _, hotel := range hotelList {
		if hotel == nil {
			continue
		}

		hotelSummary := ToHotelSummary(hotel)
		if hotelSummary == nil {
			continue
		}

		lowestRatePlan := helpers.FindLowestRatePlan(hotel.RatePlanList)

		if lowestRatePlan != nil {
			hotelSummary.Price = ToDomainPriceFromRatePlan(lowestRatePlan, dayCount, roomCount)
			hotelSummary.RoomLeft = lowestRatePlan.InventoryCount
		}

		result = append(result, hotelSummary)
	}

	return result
}
