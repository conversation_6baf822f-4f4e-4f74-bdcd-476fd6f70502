package converts

import (
	"strconv"
	"time"

	"github.com/pkg/errors"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	static_timezone "gitlab.deepgate.io/apps/common/static_timezone"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	pkgHelpers "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

// ToDidaCheckAvailabilityReq chuyển đổi từ domain.CheckAvaiAdapterReq sang entities.PriceSearchReq.
func ToDidaCheckAvailabilityReq(req *domain.CheckAvaiAdapterReq, env string) (*entities.PriceSearchReq, error) {
	if req == nil || req.HubRequest == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	hubReq := req.HubRequest

	// Chuyển đổi hotel ID từ string sang int
	hotelID, err := strconv.Atoi(req.ProviderHotelID)
	if err != nil {
		return nil, errors.Wrap(err, "strconv.Atoi")
	}

	hotelIDs := []int{hotelID}

	// Xử lý thông tin occupancies
	adultCount, childCount, childAges := helpers.ProcessOccupancies(hubReq.Occupancies)

	// Số phòng từ occupancies
	roomCount := len(hubReq.Occupancies)

	result := &entities.PriceSearchReq{
		HotelIDList:  hotelIDs,
		CheckInDate:  hubReq.Stay.CheckIn,
		CheckOutDate: hubReq.Stay.CheckOut,
		IsRealTime: &entities.IsRealTime{
			Value:     true,
			RoomCount: roomCount,
		},
		RealTimeOccupancy: &entities.RealTimeOccupancy{
			AdultCount:      adultCount,
			ChildCount:      childCount,
			ChildAgeDetails: childAges,
		},
		IsNeedOnRequest: true,
		Currency:        helpers.GetDefaultCurrency(env),
		Nationality:     hubReq.CountryCode,
	}

	return result, nil
}

// ToDomainRooms chuyển đổi từ *entities.PriceSearchRes sang []*domain.HubRoom.
func ToDomainRooms(res *entities.PriceSearchRes, occupancies []*domain.HubSearchOccupancy, checkInDate, cityName, countryCode string, dayCount int) []*domain.HubRoom {
	if res == nil || res.Success == nil || res.Success.PriceDetails == nil || len(res.Success.PriceDetails.HotelList) == 0 {
		return nil
	}

	// Lấy hotel đầu tiên (vì chỉ có 1 khách sạn trong response)
	hotel := res.Success.PriceDetails.HotelList[0]
	if hotel == nil {
		return nil
	}

	// Nhóm các rate plan theo RoomTypeID
	roomTypeMap := make(map[int64][]*entities.RatePlan)

	for _, rate := range hotel.RatePlanList {
		if rate == nil {
			continue
		}

		roomTypeMap[rate.RoomTypeID] = append(roomTypeMap[rate.RoomTypeID], rate)
	}

	// Khởi tạo mảng kết quả
	result := make([]*domain.HubRoom, 0, len(roomTypeMap))

	// Timezone của địa điểm
	timezone := static_timezone.GetTimezone(cityName, countryCode)
	if timezone == "" {
		timezone = "Asia/Shanghai" // Mặc định GMT+8
	}

	// Xử lý từng phòng
	for roomTypeID, ratePlans := range roomTypeMap {
		if len(ratePlans) == 0 {
			continue
		}

		// Lấy RoomName từ rate đầu tiên theo yêu cầu
		firstRate := ratePlans[0]

		// Tạo HubRoom mới
		hubRoom := &domain.HubRoom{
			RoomID:         helpers.MapRoomID(strconv.Itoa(hotel.HotelID), strconv.FormatInt(roomTypeID, 10)),
			ProviderRoomID: helpers.MapRoomID(strconv.Itoa(hotel.HotelID), strconv.FormatInt(roomTypeID, 10)),
			Name:           firstRate.RoomName,
			Provider:       commonEnum.HotelProviderDida,
			RateData:       make([]*domain.HubRateData, 0, len(ratePlans)),
		}

		// Xử lý từng rate plan của room
		for _, ratePlan := range ratePlans {
			rateData := processRatePlan(ratePlan, occupancies, dayCount, checkInDate, timezone)
			if rateData != nil {
				hubRoom.RateData = append(hubRoom.RateData, rateData)
			}
		}

		if len(hubRoom.RateData) > 0 {
			result = append(result, hubRoom)
		}
	}

	return result
}

// processRatePlan xử lý một RatePlan để tạo HubRateData.
func processRatePlan(ratePlan *entities.RatePlan, occupancies []*domain.HubSearchOccupancy, dayCount int, checkInDate, timezone string) *domain.HubRateData {
	if ratePlan == nil {
		return nil
	}

	// Xử lý chính sách hủy phòng
	refundable, cancelPolicies := processCancellationPolicies(ratePlan, checkInDate, timezone, len(occupancies))

	// Tính tổng giá từ PriceList
	var totalNightlyRate float64
	for _, price := range ratePlan.PriceList {
		totalNightlyRate += price.Price
	}

	// Tính tổng thuế/phí đã bao gồm
	var totalIncludedFees float64

	if ratePlan.IncludedFeeList != nil {
		for _, fee := range ratePlan.IncludedFeeList {
			totalIncludedFees += fee.Amount
		}
	}

	// Tính giá cơ bản mỗi đêm
	rateBasic := (totalNightlyRate - totalIncludedFees) / float64(dayCount)
	if rateBasic < 0 {
		rateBasic = 0
	}

	bedOption := &domain.BedOption{
		OptionID: strconv.Itoa(ratePlan.BedType),
		Name:     constants.BedTypeMap[ratePlan.BedType].Name,
		Quantity: 1,
	}

	minMealAmount := ratePlan.GetMinMealAmount()
	hasBreakfast := ratePlan.BreakfastType == entities.BreakfastTypeHasBreakfast && minMealAmount > 0

	rateData := &domain.HubRateData{
		RateID:         ratePlan.RatePlanID,
		ProviderRateID: ratePlan.RatePlanID,
		Available:      strconv.Itoa(ratePlan.InventoryCount),
		OccupancyRate:  ToHubOccupancyRates(ratePlan, occupancies, dayCount),
		CancelPolicies: cancelPolicies,
		BedOptions:     []*domain.BedOption{bedOption},
		Currency:       ratePlan.Currency,
		// TotalRateAmount: ratePlan.TotalPrice,
		// TotalRateBasic:  rateBasic * float64(dayCount),
		// TotalTaxAmount:  totalIncludedFees,
		Amenities:    ToHubAmenities(ratePlan),
		PayNow:       ratePlan.TotalPrice,
		Refundable:   refundable,
		HasBreakfast: hasBreakfast,
		NonSmoking:   true,
	}

	totalPayAtHotel := 0.0

	for _, rate := range rateData.OccupancyRate {
		for _, payAtHotel := range rate.PayAtHotel {
			totalPayAtHotel += payAtHotel.Amount
		}
	}

	rateData.TotalPayAtHotel = pkgHelpers.CeilToPrecision(totalPayAtHotel, constants.DefaultPrecisionAmount)

	return rateData
}

// parseDate parses date string from multiple formats.
func parseDate(dateStr string, defaultLoc *time.Location) (time.Time, error) {
	if dateStr == "" {
		return time.Time{}, errors.New("empty date string")
	}

	// Try ISO8601 format first: "2025-04-21T00:00:00+08:00"
	t, err := time.Parse(time.RFC3339, dateStr)
	if err == nil {
		return t, nil
	}

	// Try simpler date format: "2006-01-02"
	t, err = time.Parse("2006-01-02", dateStr)
	if err == nil {
		return time.Date(
			t.Year(),
			t.Month(),
			t.Day(),
			0, 0, 0, 0,
			defaultLoc,
		), nil
	}

	return time.Time{}, errors.New("unsupported date format")
}

// formatDate formats time to ISO8601 with timezone.
func formatEndOfDate(t time.Time) string {
	endOfDay := time.Date(t.Year(), t.Month(), t.Day(), 23, 59, 59, int(time.Second-time.Nanosecond), t.Location())
	return endOfDay.Format(time.RFC3339)
}

// normalizeDate ensures the date string has timezone information
// If it doesn't, applies the defaultLoc timezone and formats to RFC3339.
func normalizeDate(dateStr string, defaultLoc, hotelLoc *time.Location) (string, error) {
	if dateStr == "" {
		return dateStr, nil
	}

	// Try to parse with RFC3339 first (has timezone)
	t, err := time.Parse(time.RFC3339, dateStr)
	if err != nil {
		// Already has timezone, just use as is
		policyDate, err := parseDate(dateStr, defaultLoc)
		if err != nil {
			return "", err
		}

		return policyDate.Format(time.RFC3339), nil
	}

	t = t.In(hotelLoc)

	return t.Format(time.RFC3339), nil
}

// isRefundable determines if the rate plan is refundable based on cancellation policies.
func isRefundable(policies []*entities.RatePlanCancellationPolicy, totalPrice float64, defaultLoc *time.Location) bool {
	now := time.Now().In(defaultLoc)

	for _, policy := range policies {
		// Non-refundable if policy has no FromDate and covers total price
		if policy.FromDate == "" && policy.Amount >= totalPrice {
			return false
		}

		// Try to parse the policy date with RFC3339 format to preserve timezone
		policyDate, err := time.Parse(time.RFC3339, policy.FromDate)
		if err == nil {
			// Use the timezone from the policy date
			policyLoc := policyDate.Location()
			// Convert now to the same timezone as policy date
			nowInPolicyTz := now.In(policyLoc)

			// Non-refundable if policy's date is before or equal to now and covers total price
			if (policyDate.Before(nowInPolicyTz) || policyDate.Equal(nowInPolicyTz)) && policy.Amount >= totalPrice {
				return false
			}

			continue
		}

		// Fallback to parseDate if RFC3339 parsing fails
		policyDate, err = parseDate(policy.FromDate, defaultLoc)
		if err != nil {
			continue // Skip invalid dates
		}

		// Non-refundable if policy's date is before or equal to now and covers total price
		if (policyDate.Before(now) || policyDate.Equal(now)) && policy.Amount >= totalPrice {
			return false
		}
	}

	return true
}

// createCancelPolicies creates domain cancel policies from rate plan policies.
func createCancelPolicies(policies []*entities.RatePlanCancellationPolicy, checkInTime time.Time, ratePlanCurrency string, defaultLoc, hotelLoc *time.Location, roomCount int, totalPrice float64) []*domain.HubCancelPolicy {
	if len(policies) == 0 {
		return nil
	}

	cancelPolicies := make([]*domain.HubCancelPolicy, 0, len(policies))
	endDateStr := formatEndOfDate(checkInTime)

	if len(policies) == 1 {
		// Single policy case
		startDate, _ := normalizeDate(policies[0].FromDate, defaultLoc, hotelLoc)
		cancelPolicies = append(cancelPolicies, &domain.HubCancelPolicy{
			StartDate:     startDate,
			EndDate:       endDateStr,
			PenaltyAmount: policies[0].Amount * float64(roomCount),
			Refundable:    policies[0].Amount != totalPrice,
			PenaltyInfo: domain.HubCancelPolicyPenaltyInfo{
				Amount: policies[0].Amount * float64(roomCount),
			},
			Currency: ratePlanCurrency,
		})
	} else {
		// Multiple policies case
		for i := 0; i < len(policies); i++ {
			var endDate string
			if i == len(policies)-1 {
				// Last policy ends at check-in date
				endDate = endDateStr
			} else {
				// End date is the start date of next policy
				endDate, _ = normalizeDate(policies[i+1].FromDate, defaultLoc, hotelLoc)
			}

			startDate, _ := normalizeDate(policies[i].FromDate, defaultLoc, hotelLoc)
			cancelPolicies = append(cancelPolicies, &domain.HubCancelPolicy{
				StartDate:     startDate,
				EndDate:       endDate,
				Refundable:    policies[i].Amount != totalPrice,
				PenaltyAmount: policies[i].Amount * float64(roomCount),
				PenaltyInfo: domain.HubCancelPolicyPenaltyInfo{
					Amount: policies[i].Amount * float64(roomCount),
				},
				Currency: ratePlanCurrency,
			})
		}
	}

	return cancelPolicies
}

func processCancellationPolicies(ratePlan *entities.RatePlan, checkInDate, timezone string, roomCount int) (bool, []*domain.HubCancelPolicy) {
	if ratePlan == nil || len(ratePlan.RatePlanCancellationPolicyList) == 0 {
		return true, nil // Default refundable if no policies
	}

	policies := ratePlan.RatePlanCancellationPolicyList
	totalPrice := ratePlan.TotalPrice

	// Default timezone +8 (Beijing timezone) if not specified
	defaultLoc, _ := time.LoadLocation("Asia/Shanghai") // UTC+8
	hotelLoc := defaultLoc
	// Try to parse the provided timezone
	if timezone != "" {
		if loc, err := time.LoadLocation(timezone); err == nil {
			hotelLoc = loc
		}
	}

	// Parse check-in date
	checkInTime, err := parseDate(checkInDate, hotelLoc)
	if err != nil {
		// Fallback to using the string directly if parsing fails
		return false, nil
	}

	refundable := isRefundable(policies, totalPrice, defaultLoc)
	cancelPolicies := createCancelPolicies(policies, checkInTime, ratePlan.Currency, defaultLoc, hotelLoc, roomCount, totalPrice)

	return refundable, cancelPolicies
}

// CalculateIncludedFeesPerNight calculates the sum of included fees per night.
func CalculateIncludedFeesPerNight(ratePlan *entities.RatePlan, dayCount int) float64 {
	if ratePlan == nil || ratePlan.IncludedFeeList == nil || len(ratePlan.IncludedFeeList) == 0 {
		return 0
	}

	var totalIncludedFees float64
	for _, fee := range ratePlan.IncludedFeeList {
		totalIncludedFees += fee.Amount
	}

	// Calculate per night
	if dayCount <= 0 {
		dayCount = 1 // Ensure no division by zero
	}

	return totalIncludedFees / float64(dayCount)
}

// ToDomainHubRatesFromPriceList converts RatePlan's PriceList to []*domain.HubRate.
func ToDomainHubRatesFromPriceList(ratePlan *entities.RatePlan, dayCount int) []*domain.HubRate {
	if ratePlan == nil || len(ratePlan.PriceList) == 0 {
		return nil
	}

	// Calculate included fees per night
	includedFeesPerNight := CalculateIncludedFeesPerNight(ratePlan, dayCount)

	// Create HubRate for each price in PriceList
	hubRates := make([]*domain.HubRate, 0, len(ratePlan.PriceList))

	for _, price := range ratePlan.PriceList {
		// Calculate rate basic by subtracting tax amount from price
		rateBasic := price.Price - includedFeesPerNight
		if rateBasic < 0 {
			rateBasic = 0
		}

		hubRate := &domain.HubRate{
			RateAmount: price.Price,
			RateBasic:  rateBasic,
			TaxAmount:  includedFeesPerNight,
			Currency:   ratePlan.Currency,
		}

		hubRates = append(hubRates, hubRate)
	}

	return hubRates
}

// ToDomainPayAtHotel converts ratePlan's ExcludedFeeList to []*domain.PayAtHotel.
func ToDomainPayAtHotel(ratePlan *entities.RatePlan) []*domain.PayAtHotel {
	if ratePlan == nil || ratePlan.ExcludedFeeList == nil || len(ratePlan.ExcludedFeeList) == 0 {
		return nil
	}

	result := make([]*domain.PayAtHotel, 0, len(ratePlan.ExcludedFeeList))

	for _, fee := range ratePlan.ExcludedFeeList {
		payAtHotel := &domain.PayAtHotel{
			Amount:      fee.Amount,
			Description: fee.FeeTypeName,
			Currency:    fee.Currency,
		}

		result = append(result, payAtHotel)
	}

	return result
}

// ToHubOccupancyRates converts a rate plan to occupancy rates based on price details.
func ToHubOccupancyRates(ratePlan *entities.RatePlan, occupancies []*domain.HubSearchOccupancy, dayCount int) []*domain.HubOccupancyRate {
	if ratePlan == nil || len(occupancies) == 0 {
		return nil
	}

	// Calculate total nightly rate from PriceList
	var totalNightlyRate float64
	for _, price := range ratePlan.PriceList {
		totalNightlyRate += price.Price
	}

	// Calculate taxes per night
	includedFeesPerNight := CalculateIncludedFeesPerNight(ratePlan, dayCount)

	// Calculate rate basic (excluding taxes)
	rateBasic := totalNightlyRate - includedFeesPerNight
	if rateBasic < 0 {
		rateBasic = 0
	}

	// Get pay at hotel data
	payAtHotelFees := ToDomainPayAtHotel(ratePlan)

	// Map to store occupancy types and their quantities
	occupancyTypeMap := make(map[string]uint)

	// Count occurrences of each occupancy type
	for _, occ := range occupancies {
		if occ == nil {
			continue
		}
		occType := occ.GenOccupancyType()
		occupancyTypeMap[occType]++
	}

	// Create occupancy rates for each unique type
	result := make([]*domain.HubOccupancyRate, 0, len(occupancyTypeMap))

	for occType, quantity := range occupancyTypeMap {
		occupancyRate := &domain.HubOccupancyRate{
			OccupancyType:    occType,
			RoomQuantity:     quantity,
			TotalNightlyRate: ToDomainHubRatesFromPriceList(ratePlan, dayCount),
			PayAtHotel:       payAtHotelFees,
		}
		result = append(result, occupancyRate)
	}

	return result
}

// ToHubAmenities converts a RatePlan to []*domain.HubAmenity based on breakfast information.
func ToHubAmenities(ratePlan *entities.RatePlan) []*domain.HubAmenity {
	if ratePlan == nil || ratePlan.BreakfastType != entities.BreakfastTypeHasBreakfast {
		return nil
	}

	// Get total meal amount
	minMealAmount := ratePlan.GetMinMealAmount()

	// If no meals or no guests, return nil
	if minMealAmount == 0 || minMealAmount > 9 {
		return nil
	}

	// Partial breakfast coverage
	guestWord := "guest"
	if minMealAmount > 1 {
		guestWord = "guests"
	}

	return []*domain.HubAmenity{
		{
			ID:   "breakfast_" + strconv.Itoa(minMealAmount),
			Name: "Free breakfast for " + strconv.Itoa(minMealAmount) + " " + guestWord,
		},
	}
}
