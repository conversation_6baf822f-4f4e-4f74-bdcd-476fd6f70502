package converts

import (
	"fmt"
	"strconv"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	static_timezone "gitlab.deepgate.io/apps/common/static_timezone"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	pkgHelpers "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func ToDidaPriceCheckReq(in *domain.CacheCheckAvailabilityRequest, hotelID, ratePlanID, env string) (*entities.PriceConfirmReq, error) {
	if in == nil {
		return nil, errors.ErrSomethingOccurred
	}

	numOfRoom := len(in.Occupancies)
	adultCount, childCount, childAges := helpers.ProcessOccupancies(in.Occupancies)

	occupancyDetails := make([]*entities.OccupancyDetail, 0, numOfRoom)
	for i := range numOfRoom {
		occupancyDetails = append(occupancyDetails, &entities.OccupancyDetail{
			RoomNum:         i + 1,
			AdultCount:      adultCount,
			ChildCount:      childCount,
			ChildAgeDetails: childAges,
		})
	}

	intHotelID, err := strconv.Atoi(hotelID)
	if err != nil {
		return nil, err
	}

	result := &entities.PriceConfirmReq{
		PreBook:          true,
		IDNeedOnRequest:  false,
		CheckInDate:      in.Stay.CheckIn,
		CheckOutDate:     in.Stay.CheckOut,
		NumOfRooms:       numOfRoom,
		HotelID:          intHotelID,
		OccupancyDetails: occupancyDetails,
		Nationality:      in.CountryCode,
		RatePlanID:       ratePlanID,
		Currency:         helpers.GetDefaultCurrency(env),
		Header:           &entities.Header{},
	}

	return result, nil
}

func ToHubRateData(in *entities.PriceConfirmSuccess, occupancies []*domain.HubSearchOccupancy, checkInDate, cityName, countryCode string) *domain.HubRateData {
	if in == nil {
		return nil
	}

	if len(in.PriceDetails.HotelList) == 0 {
		return nil
	}

	hotel := in.PriceDetails.HotelList[0]

	if len(hotel.RatePlanList) == 0 {
		return nil
	}

	// Timezone của địa điểm
	timezone := static_timezone.GetTimezone(cityName, countryCode)
	if timezone == "" {
		timezone = "Asia/Shanghai" // Mặc định GMT+8
	}
	refundable, hubCancellationPolicies := priceCheckProcessCancellationPolicy(hotel, checkInDate, timezone)

	bedOptions := []*domain.BedOption{}

	for _, rate := range hotel.RatePlanList {
		bedType, ok := constants.GetBedTypeByID(rate.BedType)
		if !ok {
			continue // Skip nếu không tìm thấy bed type
		}

		bedOptions = append(bedOptions, &domain.BedOption{
			OptionID: fmt.Sprintf("%d", bedType.ID),
			Quantity: uint(bedType.DefaultOccupancy),
			Name:     bedType.Name,
		})
	}

	ratePlan := hotel.RatePlanList[0]

	available := ratePlan.InventoryCount
	for _, rate := range hotel.RatePlanList {
		if available > rate.InventoryCount {
			available = rate.InventoryCount
		}
	}

	occupancyRates := make([]*domain.HubOccupancyRate, 0, len(hotel.RatePlanList))
	ratePlanMapRoomNum := make(map[int]*entities.RatePlan)

	for _, rate := range hotel.RatePlanList {
		if rate.RoomOccupancy != nil {
			//nolint:unconvert
			roomNum := int(rate.RoomOccupancy.RoomNum)
			if roomNum >= 0 {
				ratePlanMapRoomNum[roomNum] = rate
			}
		}
	}

	for _, occupancy := range occupancies {
		//nolint:gosec
		roomNum := int(occupancy.OccupancyIndex)
		if rate, ok := ratePlanMapRoomNum[roomNum]; ok {
			tempOccupancyRate := didaOccupancyRateMap(rate)
			tempOccupancyRate.OccupancyType = occupancy.GenOccupancyType()
			occupancyRates = append(occupancyRates, tempOccupancyRate)
		}
	}

	currency := ratePlan.Currency

	hubAmenities := make([]*domain.HubAmenity, 0, len(hotel.RatePlanList))
	for _, rate := range hotel.RatePlanList {
		hubAmenities = append(hubAmenities, ToHubAmenities(rate)...)
	}

	totalPayAtHotel := 0.0
	payAtHotelCurrency := ratePlan.Currency

	for _, rate := range occupancyRates {
		for _, payAtHotel := range rate.PayAtHotel {
			totalPayAtHotel += payAtHotel.Amount + 1
			payAtHotelCurrency = payAtHotel.Currency
		}
	}

	minMealAmount := ratePlan.GetMinMealAmount()
	hasBreakfast := ratePlan.BreakfastType == entities.BreakfastTypeHasBreakfast && minMealAmount > 0

	return &domain.HubRateData{
		RateID:             fmt.Sprintf("%d%s", commonEnum.HotelProviderDida, ratePlan.RatePlanID),
		ProviderRateID:     ratePlan.RatePlanID,
		Available:          fmt.Sprintf("%d", available),
		CancelPolicies:     hubCancellationPolicies,
		OccupancyRate:      occupancyRates,
		Currency:           currency,
		PayNow:             in.PriceDetails.HotelList[0].TotalPrice,
		TotalPayAtHotel:    pkgHelpers.CeilToPrecision(totalPayAtHotel, constants.DefaultPrecisionAmount),
		PayAtHotelCurrency: payAtHotelCurrency,
		BedOptions:         bedOptions,
		Amenities:          hubAmenities,
		Refundable:         refundable,
		HasBreakfast:       hasBreakfast,
		HasExtraBed:        false,
		NonSmoking:         true,
	}
}

func didaOccupancyRateMap(rate *entities.RatePlan) *domain.HubOccupancyRate {
	if rate == nil {
		return nil
	}

	// Tính tổng included fees per night tương tự CalculateIncludedFeesPerNight
	var includedFeesPerNight float64
	var totalIncludedFees float64

	for _, fee := range rate.IncludedFeeList {
		totalIncludedFees += fee.Amount
	}

	// Tính per night (giả sử dayCount = số lượng phần tử trong PriceList)
	dayCount := len(rate.PriceList)
	if dayCount > 0 {
		includedFeesPerNight = totalIncludedFees / float64(dayCount)
	}

	// Tạo HubRate cho mỗi price trong PriceList tương tự ToDomainHubRatesFromPriceList
	rates := make([]*domain.HubRate, 0, len(rate.PriceList))

	for _, price := range rate.PriceList {
		// Tính rate basic bằng cách trừ tax amount từ price
		rateBasic := price.Price - includedFeesPerNight
		if rateBasic < 0 {
			rateBasic = 0
		}

		rates = append(rates, &domain.HubRate{
			RateBasic: rateBasic,
			TaxAmount: includedFeesPerNight,
			Currency:  rate.Currency,
		})
	}

	// Chuyển đổi ExcludedFeeList thành PayAtHotel
	payAtHotelList := make([]*domain.PayAtHotel, 0, len(rate.ExcludedFeeList))
	for _, ex := range rate.ExcludedFeeList {
		payAtHotelList = append(payAtHotelList, &domain.PayAtHotel{
			Amount:      ex.Amount,
			Currency:    ex.Currency,
			Description: ex.FeeTypeName,
		})
	}

	// Tạo occupancy type string từ RoomOccupancy
	// occupancyType := fmt.Sprintf("%dadt", rate.RoomOccupancy.AdultCount)

	// // Thêm thông tin về trẻ em nếu có
	// if rate.RoomOccupancy.ChildCount > 0 && len(rate.RoomOccupancy.ChildAgeDetails) > 0 {
	// 	childAges := ""

	// 	for i, age := range rate.RoomOccupancy.ChildAgeDetails {
	// 		if i > 0 {
	// 			childAges += ","
	// 		}
	// 		childAges += fmt.Sprintf("%d", age)
	// 	}
	// 	occupancyType += fmt.Sprintf(",%dchd-%s", rate.RoomOccupancy.ChildCount, childAges)
	// }

	occupancyRate := &domain.HubOccupancyRate{
		RoomQuantity:     1,
		TotalNightlyRate: rates,
		PayAtHotel:       payAtHotelList,
		// OccupancyType:    occupancyType,
	}

	return occupancyRate
}

func priceCheckProcessCancellationPolicy(hotel *entities.Hotel, checkInDate, timezone string) (bool, []*domain.HubCancelPolicy) {
	if hotel == nil {
		return false, nil
	}

	cancellationPolicy := hotel.CancellationPolicyList
	if cancellationPolicy == nil {
		return false, nil
	}

	defaultLoc, _ := time.LoadLocation("Asia/Shanghai") // UTC+8
	hotelLoc := defaultLoc
	// Try to parse the provided timezone
	if timezone != "" {
		if loc, err := time.LoadLocation(timezone); err == nil {
			hotelLoc = loc
		}
	}

	// Parse check-in date
	checkInTime, err := parseDate(checkInDate, hotelLoc)
	if err != nil {
		// Fallback to using the string directly if parsing fails
		return false, nil
	}

	roomCount := 1

	currency := ""
	if len(hotel.RatePlanList) > 0 {
		currency = hotel.RatePlanList[0].Currency
	}

	refundable := isRefundable(cancellationPolicy, hotel.TotalPrice, hotelLoc)
	hubCancellationPolicy := createCancelPolicies(cancellationPolicy, checkInTime, currency, defaultLoc, hotelLoc, roomCount, hotel.TotalPrice)

	return refundable, hubCancellationPolicy
}
