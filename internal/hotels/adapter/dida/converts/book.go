package converts

import (
	"fmt"
	"strconv"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

// ToDidaBookingConfirmReq converts domain booking objects to Dida BookingConfirmReq.
func ToDidaBookingConfirmReq(
	req *domain.HubBookReq,
	session *domain.DidaSessionInfo,
	order *domain.HubHotelOrder,
	cfg *config.Schema,
) (*entities.BookingConfirmReq, error) {
	if req == nil || session == nil || order == nil || order.Hotel == nil ||
		order.HotelSearchRequest == nil || req.Holder == nil || len(req.Holder.HolderDetail) == 0 {
		return nil, domain.ErrInvalidValue
	}

	bookReq := &entities.BookingConfirmReq{
		ReferenceNo:     session.ReferenceNo,
		ClientReference: order.OrderCode,
		CheckInDate:     order.HotelSearchRequest.Stay.CheckIn,
		CheckOutDate:    order.HotelSearchRequest.Stay.CheckOut,
		NumOfRooms:      len(order.HotelSearchRequest.Occupancies),
		TestingHashKey:  req.TestingHashKey,
		CustomerRequest: req.Holder.HolderDetail[0].SpecialRequest,
	}

	bookReq.Contact = createContactInfo(cfg, req.Holder)

	// Create guest list
	guestList, err := createGuestList(req.Holder, order.HotelSearchRequest.Occupancies)
	if err != nil {
		return nil, err
	}
	bookReq.GuestList = guestList

	return bookReq, nil
}

// createContactInfo creates contact information from holder details.
func createContactInfo(cfg *config.Schema, holder *domain.HubHolderInfo) *entities.Contact {
	if cfg == nil {
		return nil
	}

	return &entities.Contact{
		Name: &entities.Name{
			First: cfg.ContactFirstName,
			Last:  cfg.ContactLastName,
		},
		Email: cfg.CompanyEmail,
		Phone: fmt.Sprintf("%s%s", holder.PhoneCode, holder.PhoneNumber),
	}
}

// createGuestList creates the guest list for all rooms based on occupancies.
func createGuestList(holder *domain.HubHolderInfo, occupancies []*domain.HubSearchOccupancy) ([]*entities.Guest, error) {
	if holder == nil || len(holder.HolderDetail) == 0 {
		return nil, domain.ErrInvalidValue
	}

	// Map holder details by occupancy index for quick lookup
	holderMap := make(map[int]*domain.HolderDetail)

	for _, holderDetail := range holder.HolderDetail {
		//nolint:gosec
		occIndex := int(holderDetail.OccupancyIndex)
		holderMap[occIndex] = holderDetail
	}

	guestList := make([]*entities.Guest, 0, len(occupancies))

	// Process each occupancy
	for i, occupancy := range occupancies {
		if occupancy == nil {
			continue
		}

		roomNum := i + 1
		occupancyIndex := i + 1

		// Find the holder for this room
		holderDetail, exists := holderMap[occupancyIndex]
		if !exists {
			// If no specific holder for this occupancy, use the first holder
			holderDetail = holder.HolderDetail[0]
		}

		// Create guest info for this room
		guestInfo, err := createGuestInfo(holderDetail, occupancy, holder.PhoneCode, holder.PhoneNumber)
		if err != nil {
			return nil, err
		}

		guest := &entities.Guest{
			RoomNum:   roomNum,
			GuestInfo: guestInfo,
		}

		guestList = append(guestList, guest)
	}

	return guestList, nil
}

// createGuestInfo creates guest information for a specific room occupancy.
func createGuestInfo(
	holderDetail *domain.HolderDetail,
	occupancy *domain.HubSearchOccupancy,
	phoneCode string,
	phoneNumber string,
) ([]*entities.GuestInfo, error) {
	if holderDetail == nil || occupancy == nil {
		return nil, domain.ErrInvalidValue
	}

	guestInfos := make([]*entities.GuestInfo, 0)

	// Primary guest (the holder of this room)
	primaryGuest := &entities.GuestInfo{
		Name: &entities.Name{
			First: holderDetail.GivenName,
			Last:  holderDetail.Surname,
		},
		IsAdult: true,
		Email:   holderDetail.Email,
		Phone:   fmt.Sprintf("%s%s", phoneCode, phoneNumber),
	}
	guestInfos = append(guestInfos, primaryGuest)

	return guestInfos, nil
}

// createHotelInfo creates hotel information for the booking request.
func createHotelInfo(hotel *domain.HubOrderHotelItem) (*entities.Hotel, error) {
	if hotel == nil || hotel.ProviderHotelID == "" {
		return nil, domain.ErrInvalidValue
	}

	hotelID, err := strconv.Atoi(hotel.ProviderHotelID)
	if err != nil {
		return nil, domain.ErrInvalidValue
	}

	return &entities.Hotel{
		HotelID: hotelID,
	}, nil
}

// IsBookingFailed checks if a booking has failed based on the error code in the response.
func IsBookingFailed(res *entities.BookingConfirmRes) (bool, string, enum.BookingStatus) {
	if res == nil {
		return true, "Response is nil", enum.BookingStatusPending
	}

	if res.Error == nil {
		// No error, booking might be successful
		return false, "", enum.BookingStatusSuccess
	}

	errorMessage := res.Error.Message
	if res.Error.Code == "" {
		return true, errorMessage, enum.BookingStatusFailed
	}

	// Use the centralized error codes from constants package
	if constants.IsFailedBookingCode(res.Error.Code) {
		return true, errorMessage, enum.BookingStatusFailed
	}

	// For error codes not in the map, consider them as pending by default
	return false, errorMessage, enum.BookingStatusPending
}

// ToHubBookingStatusFromFinalStatus converts Dida booking status to Hub booking status.
func ToHubBookingStatusFromFinalStatus(status entities.BookingStatus) enum.BookingStatus {
	switch status {
	case entities.BookingStatusConfirmed:
		return enum.BookingStatusSuccess
	case entities.BookingStatusCanceled:
		return enum.BookingStatusCancel
	default:
		return enum.BookingStatusPending
	}
}
