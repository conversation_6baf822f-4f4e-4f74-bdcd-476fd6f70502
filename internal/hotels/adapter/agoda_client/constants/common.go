package constants

import "time"

const (
	BookingStatusCharged             = "BookingCharged"
	BookingStatusReceived            = "BookingReceived"
	BookingStatusConfirmed           = "BookingConfirmed"
	BookingStatusRejected            = "BookingRejected"
	BookingStatusError               = "TechnicalError"
	BookingStatusDeparted            = "Departed"
	BookingStatusTest                = "BookingTest"
	BookingStatusAllotment           = "AllotmentAlert"
	BookingStatusCancelledByCustomer = "BookingCancelledByCustomer"
	BookingStatusCancelled           = "BookingCancelled"
)

const (
	SupplierReference = "Awaiting"
)

const (
	DefaultLanguage        = "vi-vn"
	DefaultCountry         = "VN"
	DefaultPrecisionAmount = 2
	DefaultCurrency        = "VND"
)

const (
	SurchargeExcluded  = "Excluded"
	SurchargeMandatory = "Mandatory"
)

const (
	MaxRetry      = 10
	RetryInterval = 30 * time.Second
	WaitTime      = 5
)

var FeaturesExtra = []string{
	"rateDetail",
	"benefitDetail",
	"dailyRate",
	"cancellationDetail",
	"content",
	"surchargeDetail",
	"taxDetail",
	"promotionDetail",
}
