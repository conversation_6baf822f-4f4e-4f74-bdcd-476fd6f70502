package models

type BookingReq struct {
	WaitTime       int64           `json:"waitTime,omitempty"`
	BookingDetails *BookingDetail  `json:"bookingDetails,omitempty"`
	CustomerDetail *CustomerDetail `json:"customerDetail,omitempty"`
	PaymentDetails *PaymentDetails `json:"paymentDetails,omitempty"`
	TestingHashKey string          `json:"testing_hash_key,omitempty"`
}

type BookingDetail struct {
	SearchID         int64     `json:"searchId,omitempty"`
	Tag              string    `json:"tag,omitempty"`
	AllowDuplication bool      `json:"allowDuplication,omitempty"`
	CheckIn          string    `json:"checkIn,omitempty"`
	CheckOut         string    `json:"checkOut,omitempty"`
	UserCountry      string    `json:"userCountry,omitempty"`
	Language         string    `json:"language,omitempty"`
	Property         *Property `json:"property,omitempty"`
}

type GuestDetail struct {
	Title              string `json:"title"`
	FirstName          string `json:"firstName"`
	LastName           string `json:"lastName"`
	CountryOfResidence string `json:"countryOfResidence"`
	Gender             string `json:"gender,omitempty"`
	Primary            bool   `json:"primary"`
}

type CustomerDetail struct {
	Language   string `json:"language"`
	Title      string `json:"title"`
	FirstName  string `json:"firstName"`
	LastName   string `json:"lastName"`
	Email      string `json:"email"`
	Phone      *Phone `json:"phone"`
	Newsletter bool   `json:"newsletter"`
}

type Phone struct {
	CountryCode string `json:"countryCode,omitempty"`
	AreaCode    string `json:"areaCode,omitempty"`
	Number      string `json:"number,omitempty"`
}

type PaymentDetails struct {
	CreditCardInfo *CreditCardInfo `json:"creditCardInfo,omitempty"`
}

type CreditCardInfo struct {
	CardType          string             `json:"cardType,omitempty"`
	Number            string             `json:"number,omitempty"`
	ExpiryDate        string             `json:"expiryDate,omitempty"`
	CVC               string             `json:"cvc,omitempty"`
	HolderName        string             `json:"holderName,omitempty"`
	CountryOfIssue    string             `json:"countryOfIssue,omitempty"`
	IssuingBank       string             `json:"issuingBank,omitempty"`
	Payment3DSRequest *Payment3DSRequest `json:"payment3DSRequest,omitempty"`
}

type Payment3DSRequest struct {
	AcceptHeader        string `json:"acceptHeader,omitempty"`
	UserAgent           string `json:"userAgent,omitempty"`
	SessionID           string `json:"sessionId,omitempty"`
	BankCallback3DS1URL string `json:"bankCallback3DS1Url,omitempty"`
	ClientIP            string `json:"clientIp,omitempty"`
}

type BookingRes struct {
	Status              string              `json:"status,omitempty"`
	BookingDetails      []*BookingDetailRes `json:"bookingDetails,omitempty"`
	ErrorMessage        *ErrorMessage       `json:"errorMessage,omitempty"`
	TestingHashKeyMatch bool                `json:"testing_hash_key_match,omitempty"`
}

type BookingDetailRes struct {
	ID           int64         `json:"id,omitempty"`
	ItineraryID  int64         `json:"itineraryID,omitempty"`
	SelfService  string        `json:"selfService,omitempty"`
	Processing   bool          `json:"processing,omitempty"`
	ErrorMessage *ErrorMessage `json:"errorMessage,omitempty"`
}

type RetrieveBookingReq struct {
	BookingIDs     []int64 `json:"bookingIds,omitempty"`
	TestingHashKey string  `json:"testing_hash_key,omitempty"`
}

type RetrieveBookingRes struct {
	Bookings            []*Booking `json:"bookings,omitempty"`
	TestingHashKeyMatch bool       `json:"testing_hash_key_match,omitempty"`
}

type Booking struct {
	BookingID               int64           `json:"bookingId,omitempty"`
	Tag                     string          `json:"tag,omitempty"`
	Status                  string          `json:"status,omitempty"`
	CheckIn                 string          `json:"checkIn,omitempty"`
	CheckOut                string          `json:"checkOut,omitempty"`
	NaturalDisaster         bool            `json:"naturalDisaster,omitempty"`
	Property                *PropertyDetail `json:"property,omitempty"`
	Room                    *RoomDetail     `json:"room,omitempty"`
	TotalRates              []*Payment      `json:"totalRates,omitempty"`
	TaxSurchargeInfo        string          `json:"taxSurchargeInfo,omitempty"`
	Payment                 *PaymentInfo    `json:"payment,omitempty"`
	AgodaPromotion          bool            `json:"agodaPromotion,omitempty"`
	GuestDetails            []*GuestDetail  `json:"guestDetails,omitempty"`
	SelfService             string          `json:"selfService,omitempty"`
	Source                  string          `json:"source,omitempty"`
	SupplierReference       string          `json:"supplierReference,omitempty"`
	CancellationPolicy      string          `json:"cancellationPolicy,omitempty"`
	HowToGetThere           string          `json:"howToGetThere,omitempty"`
	SpecialRequest          string          `json:"specialRequest,omitempty"`
	Occupancy               *Occupancy      `json:"occupancy,omitempty"`
	BookingDate             string          `json:"bookingDate,omitempty"`
	HotelConfirmationNumber string          `json:"hotelConfirmationNumber,omitempty"`

	// res List booking
	ID           int64  `json:"id,omitempty"`
	PropertyID   int    `json:"propertyId,omitempty"`
	PropertyName string `json:"propertyName,omitempty"`
	CityName     string `json:"cityName,omitempty"`
	Received     string `json:"received,omitempty"`
	LastModified string `json:"lastModified,omitempty"`
}

type PropertyDetail struct {
	PropertyName string `json:"propertyName,omitempty"`
	Country      string `json:"country,omitempty"`
	City         string `json:"city,omitempty"`
	AddressLine1 string `json:"addressLine1,omitempty"`
	AddressLine2 string `json:"addressLine2,omitempty"`
}

type RoomDetail struct {
	RoomType    string         `json:"roomType,omitempty"`
	RoomsBooked int64          `json:"roomsBooked,omitempty"`
	RatePlan    string         `json:"ratePlan,omitempty"`
	RateType    string         `json:"rateType,omitempty"`
	Benefits    []*RoomBenefit `json:"benefits,omitempty"`
}

type RoomBenefit struct {
	ID          string `json:"id,omitempty"`
	BenefitName string `json:"benefitName,omitempty"`
}

type RateSummary struct {
	Currency  string  `json:"currency,omitempty"`
	Exclusive float64 `json:"exclusive,omitempty"`
	Inclusive float64 `json:"inclusive,omitempty"`
	Tax       float64 `json:"tax,omitempty"`
	Fees      float64 `json:"fees,omitempty"`
}

type PaymentInfo struct {
	Rates       []*Payment `json:"rates,omitempty"`
	PaymentRate *Payment   `json:"paymentRate,omitempty"`
}

type Occupancy struct {
	NumberOfAdults   int `json:"numberOfAdults,omitempty"`
	NumberOfChildren int `json:"numberOfChildren,omitempty"`
}

type ListBookingReq struct {
	Tags           []string `json:"tags,omitempty"`
	TestingHashKey string   `json:"testing_hash_key,omitempty"`
}

type ListBookingRes struct {
	Bookings            []*Booking `json:"bookings,omitempty"`
	TestingHashKeyMatch bool       `json:"testing_hash_key_match,omitempty"`
}
