package models

type CancelBookingReq struct {
	BookingID int64 `json:"bookingId,omitempty"`
}

type CancelBookingRes struct {
	CancellationSummary *CancellationSummary `json:"cancellationSummary,omitempty"`
}

type CancellationSummary struct {
	BookingID          int64         `json:"bookingId,omitempty"`
	Reference          int64         `json:"reference,omitempty"`
	CancellationPolicy []*PolicyText `json:"cancellationPolicy,omitempty"`
	PaymentRate        []*Payment    `json:"paymentRate,omitempty"`
	RefundRate         []*Payment    `json:"refundRate,omitempty"`
}

type PolicyText struct {
	Language   string `json:"language,omitempty"`
	PolicyText string `json:"policyText,omitempty"`
}

type ConfirmCancelBookingReq struct {
	BookingID    int64    `json:"bookingId"`
	Reference    int64    `json:"reference"`
	RefundRate   *Payment `json:"refundRate"`
	CancelReason int64    `json:"cancelReason"`
}

type ConfirmCancelBookingRes struct {
	ErrorMessage *ErrorMessage `json:"errorMessage,omitempty"`
}
