package models

type PreCheckReq struct {
	PrecheckDetails *PrecheckDetails `json:"precheckDetails,omitempty"`
}

type PrecheckDetails struct {
	SearchID         int64     `json:"searchId,omitempty"`
	Tag              string    `json:"tag,omitempty"`
	AllowDuplication bool      `json:"allowDuplication,omitempty"`
	CheckIn          string    `json:"checkIn,omitempty"`
	CheckOut         string    `json:"checkOut,omitempty"`
	Property         *Property `json:"property,omitempty"`
	Language         string    `json:"language,omitempty"`
	UserCountry      string    `json:"userCountry,omitempty"`
	TestingHashKey   string    `json:"testing_hash_key,omitempty"`
}

type PreCheckRes struct {
	Status    int64           `json:"status,omitempty"`
	Message   string          `json:"message,omitempty"`
	ErrorList []*ErrorMessage `json:"errorList,omitempty"`
}

type ErrorMessage struct {
	HotelID int64  `json:"hotelId,omitempty"`
	UID     string `json:"uid,omitempty"`
	Code    int64  `json:"code,omitempty"`
	ID      string `json:"id,omitempty"`
	Message string `json:"message,omitempty"`
}
