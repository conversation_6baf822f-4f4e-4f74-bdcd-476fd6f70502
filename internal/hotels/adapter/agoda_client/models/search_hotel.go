package models

type SearchHotelReq struct {
	WaitTime int64     `json:"waitTime"`
	Criteria *Criteria `json:"criteria"`
	Features *Features `json:"features"`
}

type Criteria struct {
	PropertyIDs  []int  `json:"propertyIds"`
	CheckIn      string `json:"checkIn"`
	CheckOut     string `json:"checkOut"`
	Rooms        int    `json:"rooms"`
	Adults       int    `json:"adults"`
	Children     int    `json:"children"`
	ChildrenAges []int  `json:"childrenAges,omitempty"`
	Language     string `json:"language,omitempty"`
	Currency     string `json:"currency,omitempty"`
	UserCountry  string `json:"userCountry,omitempty"`
}

type Features struct {
	RatesPerProperty int64    `json:"ratesPerProperty,omitempty"`
	Extra            []string `json:"extra,omitempty"`
}

type SearchHotelRes struct {
	SearchID   int64       `json:"searchId,omitempty"`
	Properties []*Property `json:"properties,omitempty"`
}

type Property struct {
	PropertyID             int     `json:"propertyId,omitempty"`
	PropertyName           string  `json:"propertyName,omitempty"`
	TranslatedPropertyName string  `json:"translatedPropertyName,omitempty"`
	PropertyUTCOffset      string  `json:"propertyUtcOffset,omitempty"`
	Rooms                  []*Room `json:"rooms,omitempty"`
}

type Room struct {
	RoomID                int                 `json:"roomId,omitempty"`
	BlockID               string              `json:"blockId,omitempty"`
	RoomName              string              `json:"roomName,omitempty"`
	ParentRoomName        string              `json:"parentRoomName,omitempty"`
	TranslatedRoomName    string              `json:"translatedRoomName,omitempty"`
	BlockIDBackup         string              `json:"blockIdBackup,omitempty"`
	ParentRoomID          int                 `json:"parentRoomId,omitempty"`
	RatePlanID            int                 `json:"ratePlanId,omitempty"`
	FreeWifi              bool                `json:"freeWifi,omitempty"`
	RemainingRooms        int                 `json:"remainingRooms,omitempty"`
	NormalBedding         int                 `json:"normalBedding,omitempty"`
	ExtraBeds             int                 `json:"extraBeds,omitempty"`
	FreeBreakFast         bool                `json:"freeBreakFast,omitempty"`
	FreeCancellation      bool                `json:"freeCancellation,omitempty"`
	TotalPayment          *Payment            `json:"totalPayment,omitempty"`
	RoomTypeNotGuaranteed bool                `json:"roomTypeNotGuaranteed,omitempty"`
	PaymentModel          string              `json:"paymentModel,omitempty"`
	Rate                  *Payment            `json:"rate,omitempty"`
	PerRoomPerNightRate   *Payment            `json:"perRoomPerNightRate,omitempty"`
	DailyRate             []*Payment          `json:"dailyRate,omitempty"`
	PromotionDetail       *PromotionDetail    `json:"promotionDetail,omitempty"`
	Surcharges            []*Surcharge        `json:"surcharges,omitempty"`
	TaxBreakdown          []*TaxBreakdown     `json:"taxBreakdown,omitempty"`
	CancellationPolicy    *CancellationPolicy `json:"cancellationPolicy,omitempty"`
	Benefits              []*Benefit          `json:"benefits,omitempty"`
	Currency              string              `json:"currency,omitempty"`
	Count                 int                 `json:"count,omitempty"`
	Adults                int                 `json:"adults"`
	Children              int                 `json:"children"`
	ChildrenAges          []int               `json:"childrenAges,omitempty"`
	GuestDetails          []*GuestDetail      `json:"guestDetails,omitempty"`
	SpecialRequest        string              `json:"specialRequest,omitempty"`
}

type Payment struct {
	Currency       string           `json:"currency,omitempty"`
	Exclusive      float64          `json:"exclusive,omitempty"`
	Inclusive      float64          `json:"inclusive,omitempty"`
	Tax            float64          `json:"tax,omitempty"`
	Fees           float64          `json:"fees,omitempty"`
	TaxDueSupplier float64          `json:"taxDueSupplier,omitempty"`
	Method         string           `json:"method,omitempty"`
	Date           string           `json:"date,omitempty"`
	Surcharge      *SurchargeAmount `json:"surchage,omitempty"`
}

type SurchargeAmount struct {
	Exclusive float64 `json:"exclusive,omitempty"`
	Inclusive float64 `json:"inclusive,omitempty"`
	Tax       float64 `json:"tax,omitempty"`
	Fees      float64 `json:"fees,omitempty"`
	Margin    string  `json:"margin,omitempty"`
}

type PromotionDetail struct {
	PromotionID  int     `json:"promotionId,omitempty"`
	CodeEligible bool    `json:"codeEligible,omitempty"`
	Description  string  `json:"description,omitempty"`
	SavingAmount float64 `json:"savingAmount,omitempty"`
}

type Surcharge struct {
	ID     int64    `json:"id,omitempty"`
	Method string   `json:"method,omitempty"`
	Charge string   `json:"charge,omitempty"`
	Margin string   `json:"margin,omitempty"`
	Name   string   `json:"name,omitempty"`
	Rate   *Payment `json:"rate,omitempty"`
}

type TaxBreakdown struct {
	ID                       string  `json:"id,omitempty"`
	TypeValue                string  `json:"typeValue,omitempty"`
	TaxDescription           string  `json:"taxDescription,omitempty"`
	TranslatedTaxDescription string  `json:"translatedTaxDesciption,omitempty"`
	Method                   string  `json:"method,omitempty"`
	Currency                 string  `json:"currency,omitempty"`
	Base                     string  `json:"base,omitempty"`
	Taxable                  string  `json:"taxable,omitempty"`
	Percent                  float64 `json:"percent,omitempty"`
	Amount                   float64 `json:"amount,omitempty"`
}

type CancellationPolicy struct {
	Code                       string       `json:"code,omitempty"`
	CancellationText           string       `json:"cancellationText,omitempty"`
	TranslatedCancellationText string       `json:"translatedCancellationText,omitempty"`
	Parameter                  []*Parameter `json:"parameter,omitempty"`
	Date                       []*Date      `json:"date,omitempty"`
}

type Parameter struct {
	Days   int64  `json:"days,omitempty"`
	Charge string `json:"charge,omitempty"`
	Value  int64  `json:"value,omitempty"`
}

type Date struct {
	Onward string   `json:"onward,omitempty"`
	Before string   `json:"before,omitempty"`
	Rate   *Payment `json:"rate,omitempty"`
}

type Benefit struct {
	ID                    int64  `json:"id,omitempty"`
	BenefitName           string `json:"benefitName,omitempty"`
	TranslatedBenefitName string `json:"translatedBenefitName,omitempty"`
}
