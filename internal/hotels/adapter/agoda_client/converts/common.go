package converts

import (
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ConvertToIntSlice(strs []string) ([]int, error) {
	var result []int

	for _, s := range strs {
		num, err := strconv.ParseInt(s, 10, 0)
		if err != nil {
			return nil, err
		}

		result = append(result, int(num))
	}

	return result, nil
}

func ParseOffsetToSeconds(offset string) int {
	sign := 1

	if strings.HasPrefix(offset, "-") {
		sign = -1
		offset = offset[1:]
	} else if strings.HasPrefix(offset, "+") {
		offset = offset[1:]
	}

	parts := strings.Split(offset, ":")
	hour, _ := strconv.Atoi(parts[0])
	min := 0

	if len(parts) > 1 {
		min, _ = strconv.Atoi(parts[1])
	}

	return sign * (hour*3600 + min*60)
}

func AlmostEqual(a, b float64) bool {
	const epsilon = 0.01

	return math.Abs(a-b) < epsilon
}

func MapRoomID(hotelID, roomID string) string {
	return fmt.Sprintf("ag%s-%s", hotelID, roomID)
}

func ProcessOccupancies(rooms int, occupancies []*domain.HubSearchOccupancy) (adultCount int, childCount int, childAges []int) {
	if len(occupancies) == 0 {
		return 0, 0, nil
	}

	maxPeople := 0
	maxAdults := 0
	allChildAges := []int{}

	for _, occ := range occupancies {
		roomAdults := int(occ.Adults)
		roomChildren := 0
		if occ.Children != nil {
			roomChildren = int(occ.Children.Number)
			for _, age := range occ.Children.Age {
				allChildAges = append(allChildAges, int(age))
			}
		}

		totalPeople := roomAdults + roomChildren

		if totalPeople > maxPeople {
			maxPeople = totalPeople
		}
		if roomAdults > maxAdults {
			maxAdults = roomAdults
		}
	}

	// Suy ra số trẻ em
	childCount = maxPeople - maxAdults

	// Sắp xếp tất cả tuổi giảm dần
	sort.Slice(allChildAges, func(i, j int) bool {
		return allChildAges[i] > allChildAges[j]
	})

	// Lấy đúng số lượng cần
	if childCount > len(allChildAges) {
		childCount = len(allChildAges)
	}
	childAges = allChildAges[:childCount]

	// Nhân theo số phòng
	adultCount = maxAdults * rooms
	childCount = childCount * rooms
	agesFinal := []int{}
	for i := 0; i < rooms; i++ {
		agesFinal = append(agesFinal, childAges...)
	}
	childAges = agesFinal

	return adultCount, childCount, childAges
}
