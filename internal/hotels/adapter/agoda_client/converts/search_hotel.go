package converts

import (
	"strconv"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToAgodaSearchHotelReq(in *domain.SearchAdapterReq, currency string) *models.SearchHotelReq {
	if in == nil || in.HubRequest == nil || in.HubRequest.Stay.CheckIn == "" || in.HubRequest.Stay.CheckOut == "" {
		return nil
	}

	result := &models.SearchHotelReq{
		WaitTime: int64(in.Timeout),
		Criteria: ToCriteria(in.HubRequest, in.ProviderHotelIds, currency),
		Features: &models.Features{
			RatesPerProperty: 1,
			Extra:            constants.FeaturesExtra,
		},
	}

	return result
}

func ToCriteria(in *domain.HubSearchHotelRequest, providerHotelIds []string, currency string) *models.Criteria {
	propertyIDs, err := ConvertToIntSlice(providerHotelIds)
	if err != nil {
		log.Info("convert error", log.Any("convert error", err))

		return nil
	}

	adultCount, childCount, childAges := ProcessOccupancies(in.Stay.RoomCount, in.Occupancies)

	return &models.Criteria{
		PropertyIDs:  propertyIDs,
		CheckIn:      in.Stay.CheckIn,
		CheckOut:     in.Stay.CheckOut,
		Rooms:        in.Stay.RoomCount,
		Adults:       adultCount,
		Children:     childCount,
		ChildrenAges: childAges,
		Language:     constants.DefaultLanguage,
		Currency:     currency,
		UserCountry:  constants.DefaultCountry,
	}
}

func ToDomainHotelSummaries(res *models.SearchHotelRes, req *domain.HubSearchHotelRequest) []*domain.HotelSummary {
	if res == nil || res.Properties == nil {
		return nil
	}

	hotelList := res.Properties
	result := make([]*domain.HotelSummary, 0, len(hotelList))

	for _, hotel := range hotelList {
		if hotel == nil {
			continue
		}

		hotelSummary := ToHotelSummary(hotel, req)
		if hotelSummary == nil {
			continue
		}

		result = append(result, hotelSummary)
	}

	return result
}

func ToHotelSummary(hotel *models.Property, req *domain.HubSearchHotelRequest) *domain.HotelSummary {
	if hotel == nil {
		return nil
	}

	timezone := hotel.PropertyUTCOffset

	room := FindRoomWithLowestInclusive(hotel.Rooms)
	available := room.RemainingRooms > 0

	hotelSummary := &domain.HotelSummary{
		ProviderHotelID: strconv.Itoa(hotel.PropertyID),
		Provider:        commonEnum.HotelProviderAgoda,
		MatchKey:        commonEnum.HotelProviderToHash(commonEnum.HotelProviderAgoda),
		Name:            hotel.PropertyName,
		RoomLeft:        int(room.RemainingRooms),
		Available:       available,
		Price:           ToDomainPrice(room, req.Stay.DayCount, req.Stay.RoomCount, req.Stay.CheckOut, timezone),
	}

	return hotelSummary
}

func ToDomainPrice(room *models.Room, dayCount, roomCount int, checkOut, timezone string) *domain.Price {
	if room == nil {
		return nil
	}

	payAtHotel := ToDomainPayAtHotels(room.Surcharges)
	totalPayAtHotel := 0

	for _, p := range payAtHotel {
		totalPayAtHotel += int(p.Amount)
	}

	refundable, _ := ProcessCancellationPolicies(room.CancellationPolicy, room.DailyRate, checkOut, roomCount, timezone)

	return &domain.Price{
		Total:           room.TotalPayment.Inclusive,
		PayAtHotel:      payAtHotel,
		TotalPayAtHotel: float64(totalPayAtHotel),
		IsIncludeTax:    true,
		HasBreakfast:    room.FreeBreakFast,
		NonSmoking:      true,
		HasExtraBed:     CheckHasExtraBed(room.Benefits, room.ExtraBeds),
		Currency:        room.Rate.Currency,
		PricePerNight:   ToDomainPricePerNight(room.Rate),
		Refundable:      refundable,
	}
}

func ToDomainPayAtHotels(in []*models.Surcharge) []*domain.PayAtHotel {
	result := make([]*domain.PayAtHotel, 0, len(in))

	for _, i := range in {
		if i.Charge == constants.SurchargeExcluded {
			result = append(result, ToDomainPayAtHotel(i))
		}
	}

	return result
}

func ToDomainPayAtHotel(in *models.Surcharge) *domain.PayAtHotel {
	if in == nil {
		return nil
	}

	return &domain.PayAtHotel{
		Amount:      in.Rate.Inclusive,
		Description: in.Name,
		Currency:    in.Rate.Currency,
	}
}

func CheckHasExtraBed(benefits []*models.Benefit, extraBeds int) bool {
	if extraBeds > 0 {
		return true
	}

	for _, b := range benefits {
		if b != nil && b.ID == 35 {
			return true
		}
	}

	return false
}

func ToDomainPricePerNight(in *models.Payment) *domain.PricePerNight {
	if in == nil {
		return nil
	}

	return &domain.PricePerNight{
		DiscountPrice: in.Exclusive,
		OriginalPrice: in.Exclusive,
	}
}

func FindRoomWithLowestInclusive(roomList []*models.Room) *models.Room {
	if len(roomList) == 0 {
		return nil
	}

	var lowestRoom *models.Room

	for _, room := range roomList {
		if room == nil {
			continue
		}

		if lowestRoom == nil || room.TotalPayment.Inclusive < lowestRoom.TotalPayment.Inclusive {
			lowestRoom = room
		}
	}

	return lowestRoom
}
