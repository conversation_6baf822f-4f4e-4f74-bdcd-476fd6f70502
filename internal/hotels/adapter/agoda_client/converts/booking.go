package converts

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

func ToBookingReq(req *domain.HubBookReq, order *domain.HubHotelOrder, session *domain.AgodaSessionInfo, cfg *config.Schema) *models.BookingReq {
	if req == nil || order == nil || session == nil || cfg == nil {
		return nil
	}

	payment, err := BuildPaymentDetails(cfg)
	if err != nil {
		log.Info("payment err", log.Any("can not get payment info", cfg))
		return nil
	}

	cusDetail, err := BuildCustomerDetails(cfg)
	if err != nil {
		log.Info("customer detail err", log.Any("can not get customer detail", cfg))
		return nil
	}

	return &models.BookingReq{
		WaitTime:       20,
		BookingDetails: ToBookingDetailsReq(req, order, session, cfg),
		CustomerDetail: cusDetail,
		PaymentDetails: payment,
		TestingHashKey: req.TestingHashKey,
	}
}

func ToBookingDetailsReq(req *domain.HubBookReq, order *domain.HubHotelOrder, session *domain.AgodaSessionInfo, cfg *config.Schema) *models.BookingDetail {
	if req == nil || order == nil || session == nil || cfg == nil {
		return nil
	}

	return &models.BookingDetail{
		SearchID:         session.SearchID,
		Tag:              order.OrderCode,
		AllowDuplication: true,
		CheckIn:          order.HotelSearchRequest.Stay.CheckIn,
		CheckOut:         order.HotelSearchRequest.Stay.CheckOut,
		UserCountry:      constants.DefaultCountry,
		Language:         constants.DefaultLanguage,
		Property:         ToBookingProperty(req, order, session, cfg),
	}
}

func ToBookingProperty(req *domain.HubBookReq, order *domain.HubHotelOrder, session *domain.AgodaSessionInfo, cfg *config.Schema) *models.Property {
	hotelID, err := strconv.Atoi(order.Hotel.ProviderHotelID)
	if err != nil {
		return nil
	}

	return &models.Property{
		PropertyID: hotelID,
		Rooms:      ToBookingRooms(req, order, session, cfg),
	}
}

func ToBookingRooms(req *domain.HubBookReq, order *domain.HubHotelOrder, session *domain.AgodaSessionInfo, cfg *config.Schema) []*models.Room {
	if req == nil || order == nil || order.Hotel == nil || len(order.Hotel.ListRooms) == 0 {
		return nil
	}

	mainHolder := req.Holder.HolderDetail[0]
	mainKey := strings.ToLower(mainHolder.GivenName + mainHolder.Surname)

	var (
		guestList       []*models.GuestDetail
		specialRequests []string
		addedGuestKeys  = map[string]bool{}
	)

	for idx, holder := range req.Holder.HolderDetail {
		fullName := strings.ToLower(holder.GivenName + holder.Surname)

		// Tránh trùng tên holder chính
		if idx == 0 || fullName != mainKey {
			if addedGuestKeys[fullName] {
				continue
			}

			addedGuestKeys[fullName] = true

			nationlity := constants.DefaultCountry
			if holder.Nationality != "" {
				nationlity = holder.Nationality
			}

			guestList = append(guestList, &models.GuestDetail{
				Title:              "",
				FirstName:          holder.GivenName,
				LastName:           holder.Surname,
				CountryOfResidence: nationlity,
				Primary:            idx == 0,
			})
		}

		if req := strings.TrimSpace(holder.SpecialRequest); req != "" {
			specialRequests = append(specialRequests, req)
		}
	}

	roomCount := len(order.HotelSearchRequest.Occupancies)
	adultCount, childCount, childAges := ProcessOccupancies(roomCount, order.HotelSearchRequest.Occupancies)

	agodaRoom := &models.Room{
		BlockID:        order.RateData.ProviderRateID,
		Rate:           ToRoomRate(order.RateData.AdditionalData.AgodaCheckAvaibilityInfo.Rate),
		Surcharges:     ToRoomSurcharges(order.RateData.AdditionalData.AgodaCheckAvaibilityInfo.Surcharges),
		GuestDetails:   guestList,
		Currency:       cfg.AgodaCurrency,
		PaymentModel:   session.PaymentModel,
		Count:          roomCount,
		Adults:         adultCount,
		Children:       childCount,
		ChildrenAges:   childAges,
		SpecialRequest: strings.Join(specialRequests, ", "),
	}

	return []*models.Room{agodaRoom}
}

func CheckBookingStatus(status string) (enum.BookingStatus, bool) {
	switch status {
	case constants.BookingStatusCharged:
		return enum.BookingStatusSuccess, false
	case constants.BookingStatusReceived, constants.BookingStatusConfirmed:
		return enum.BookingStatusPending, false
	case constants.BookingStatusRejected, constants.BookingStatusError, constants.BookingStatusDeparted, constants.BookingStatusTest, constants.BookingStatusAllotment:
		return enum.BookingStatusPending, true
	default:
		return enum.BookingStatusPending, true
	}
}

func ToHubRetrieveConfirmationID(room *domain.HubOrderRoomItem, bookingStatus enum.BookingStatus, comfirmationID string, hotelConfirmationID string) *domain.HubRetrieveConfirmationID {
	if room == nil {
		return nil
	}

	return &domain.HubRetrieveConfirmationID{
		ProviderRoomID:      room.ProviderRoomID,
		OccupancyType:       room.OccupancyType,
		ConfirmationID:      comfirmationID,
		HotelConfirmationID: hotelConfirmationID,
		BookStatus:          bookingStatus,
		BedOptionID:         room.BedOption.OptionID,
		GivenName:           room.GivenName,
		Surname:             room.Surname,
		Used:                false,
	}
}

func BuildPaymentDetails(cfg *config.Schema) (*models.PaymentDetails, error) {
	var paymentDetails *models.PaymentDetails

	err := json.Unmarshal([]byte(cfg.AgodaPaymentJSON), &paymentDetails)
	if err != nil {
		return nil, fmt.Errorf("failed to parse payment JSON: %w", err)
	}

	if paymentDetails.CreditCardInfo == nil {
		paymentDetails.CreditCardInfo = &models.CreditCardInfo{}
	}

	paymentDetails.CreditCardInfo.Number = cfg.BZTCardNumber
	paymentDetails.CreditCardInfo.ExpiryDate = cfg.BZTCardExpiryDate
	paymentDetails.CreditCardInfo.CVC = cfg.BZTCardCVC
	paymentDetails.CreditCardInfo.HolderName = cfg.BZTCardHolderName

	return paymentDetails, nil
}

func BuildCustomerDetails(cfg *config.Schema) (*models.CustomerDetail, error) {
	var cusDetails *models.CustomerDetail

	err := json.Unmarshal([]byte(cfg.AgodaCustomerDetail), &cusDetails)
	if err != nil {
		return nil, fmt.Errorf("failed to parse customer detail JSON: %w", err)
	}

	return cusDetails, nil
}
