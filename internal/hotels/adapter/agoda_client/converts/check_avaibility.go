package converts

import (
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
	pkgHelpers "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func ToAgodaCheckAvailabilityReq(req *domain.CheckAvaiAdapterReq, currency string) (*models.SearchHotelReq, error) {
	if req == nil || req.HubRequest == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	hubReq := req.HubRequest

	hotelID, err := strconv.Atoi(req.ProviderHotelID)
	if err != nil {
		return nil, errors.Wrap(err, "strconv.Atoi")
	}

	// Số phòng từ occupancies
	roomCount := len(hubReq.Occupancies)
	adultCount, childCount, childAges := ProcessOccupancies(roomCount, hubReq.Occupancies)

	result := &models.SearchHotelReq{
		WaitTime: constants.WaitTime,
		Criteria: &models.Criteria{
			PropertyIDs:  []int{hotelID},
			CheckIn:      req.HubRequest.Stay.CheckIn,
			CheckOut:     req.HubRequest.Stay.CheckOut,
			Rooms:        roomCount,
			Adults:       adultCount,
			Children:     childCount,
			ChildrenAges: childAges,
			Language:     constants.DefaultLanguage,
			Currency:     currency,
			UserCountry:  constants.DefaultCountry,
		},
		Features: &models.Features{
			RatesPerProperty: 100,
			Extra:            constants.FeaturesExtra,
		},
	}

	return result, nil
}

func ToDomainRooms(res *models.SearchHotelRes, req *domain.CheckAvaiAdapterReq) []*domain.HubRoom {
	if res == nil || len(res.Properties) == 0 {
		return nil
	}

	// Lấy hotel đầu tiên (vì chỉ có 1 khách sạn trong response)
	hotel := res.Properties[0]
	if hotel == nil {
		return nil
	}

	// Nhóm các theo room.ParentRoomID
	roomRateMap := make(map[int][]*models.Room)

	for _, room := range hotel.Rooms {
		if room == nil {
			continue
		}

		roomRateMap[room.ParentRoomID] = append(roomRateMap[room.ParentRoomID], room)
	}

	result := make([]*domain.HubRoom, 0, len(roomRateMap))
	timezone := hotel.PropertyUTCOffset

	// Xử lý từng phòng
	for parentRoomID, rates := range roomRateMap {
		if len(rates) == 0 {
			continue
		}

		// Lấy RoomName từ rate đầu tiên theo yêu cầu
		firstRate := rates[0]

		hubRoom := &domain.HubRoom{
			RoomID:         MapRoomID(strconv.Itoa(hotel.PropertyID), strconv.FormatInt(int64(parentRoomID), 10)),
			ProviderRoomID: strconv.FormatInt(int64(parentRoomID), 10),
			Name:           firstRate.TranslatedRoomName,
			Provider:       commonEnum.HotelProviderAgoda,
			RateData:       make([]*domain.HubRateData, 0, len(rates)),
		}

		// Xử lý từng rate của room
		for _, rate := range rates {
			rateData := ProcessRoomRate(rate, req.HubRequest, timezone)

			if rateData != nil {
				hubRoom.RateData = append(hubRoom.RateData, rateData)
			}
		}

		if len(hubRoom.RateData) > 0 {
			result = append(result, hubRoom)
		}
	}

	return result
}

func ProcessRoomRate(rate *models.Room, req *domain.HubCheckAvailabilityReq, timezone string) *domain.HubRateData {
	if rate == nil {
		return nil
	}

	refundable, cancelPolicies := ProcessCancellationPolicies(rate.CancellationPolicy, rate.DailyRate, req.Stay.CheckOut, req.Stay.RoomCount, timezone)

	rateData := &domain.HubRateData{
		RateID:         uuid.NewString(),
		ProviderRateID: rate.BlockID,
		Available:      strconv.Itoa(rate.RemainingRooms),
		OccupancyRate:  ToHubOccupancyRates(rate, req.Occupancies, req.Stay.RoomCount, req.Stay.DayCount),
		Currency:       rate.Rate.Currency,
		Amenities:      ToHubAmenities(rate.Benefits),
		PayNow:         rate.TotalPayment.Inclusive,
		CancelPolicies: cancelPolicies,
		Refundable:     refundable,
		HasBreakfast:   rate.FreeBreakFast,
		NonSmoking:     true,
		HasExtraBed:    CheckHasExtraBed(rate.Benefits, rate.ExtraBeds),
		Promotions:     []*domain.HubPromotion{ToDomainPromotion(rate.PromotionDetail)},
		AdditionalData: ToAddtionalData(rate),
	}

	totalPayAtHotel := 0.0

	for _, rate := range rateData.OccupancyRate {
		for _, payAtHotel := range rate.PayAtHotel {
			totalPayAtHotel += payAtHotel.Amount
		}
	}

	rateData.TotalPayAtHotel = pkgHelpers.CeilToPrecision(totalPayAtHotel, constants.DefaultPrecisionAmount)

	return rateData
}

func ToAddtionalData(in *models.Room) *domain.AdditionalData {
	if in == nil {
		return nil
	}

	return &domain.AdditionalData{
		AgodaCheckAvaibilityInfo: &domain.AgodaCheckAvaibilityInfo{
			PaymentModel: in.PaymentModel,
			Rate:         ToDomainRate(in.Rate),
			Surcharges:   ToDomainSurcharges(in.Surcharges),
		},
	}
}

func ToDomainRate(in *models.Payment) *domain.AgodaRate {
	if in == nil {
		return nil
	}

	return &domain.AgodaRate{
		Inclusive: in.Inclusive,
	}
}

func ToDomainSurcharges(in []*models.Surcharge) []*domain.Surcharge {
	result := make([]*domain.Surcharge, 0, len(in))

	for _, i := range in {
		result = append(result, ToDomainSurcharge(i))
	}

	return result
}

func ToDomainSurcharge(in *models.Surcharge) *domain.Surcharge {
	if in == nil {
		return nil
	}

	return &domain.Surcharge{
		ID:   in.ID,
		Rate: ToDomainRate(in.Rate),
	}
}

func ToDomainPromotion(in *models.PromotionDetail) *domain.HubPromotion {
	if in == nil || !in.CodeEligible {
		return nil
	}

	return &domain.HubPromotion{
		Code:   strconv.Itoa(in.PromotionID),
		Remark: in.Description,
	}
}

func ToHubOccupancyRates(rate *models.Room, occupancies []*domain.HubSearchOccupancy, roomCount int, dayCount int) []*domain.HubOccupancyRate {
	occupancyTypeMap := make(map[string]uint)

	for _, occ := range occupancies {
		if occ == nil {
			continue
		}

		occType := occ.GenOccupancyType()
		occupancyTypeMap[occType]++
	}

	result := make([]*domain.HubOccupancyRate, 0, len(occupancyTypeMap))

	for occType, quantity := range occupancyTypeMap {
		occupancyRate := &domain.HubOccupancyRate{
			OccupancyType:    occType,
			RoomQuantity:     quantity,
			TotalNightlyRate: ToDomainHubRatesFromPriceList(rate.DailyRate, rate.Rate, dayCount, roomCount),
			PayAtHotel:       ToDomainOccPayAtHotels(rate.Surcharges, roomCount),
			RateTaxes:        ToDomainRateTaxes(rate.Surcharges, roomCount),
		}

		result = append(result, occupancyRate)
	}

	return result
}

func ToDomainOccPayAtHotels(in []*models.Surcharge, roomCount int) []*domain.PayAtHotel {
	result := make([]*domain.PayAtHotel, 0, len(in))

	for _, i := range in {
		if i.Charge == constants.SurchargeExcluded {
			result = append(result, ToDomainOccPayAtHotel(i, roomCount))
		}
	}

	return result
}

func ToDomainOccPayAtHotel(in *models.Surcharge, roomCount int) *domain.PayAtHotel {
	if in == nil || roomCount == 0 {
		return nil
	}

	return &domain.PayAtHotel{
		Amount:      in.Rate.Inclusive / float64(roomCount),
		Currency:    in.Rate.Currency,
		Description: in.Name,
	}
}

func ToDomainRateTaxes(in []*models.Surcharge, roomCount int) []*domain.HubRateTax {
	result := make([]*domain.HubRateTax, 0, len(in))

	for _, i := range in {
		if i.Charge == constants.SurchargeMandatory {
			result = append(result, ToDomainRateTaxe(i, roomCount))
		}
	}

	return result
}

func ToDomainRateTaxe(in *models.Surcharge, roomCount int) *domain.HubRateTax {
	if in == nil || roomCount == 0 {
		return nil
	}

	amount := in.Rate.Inclusive / float64(roomCount)

	return &domain.HubRateTax{
		Amount:      &amount,
		Currency:    in.Rate.Currency,
		Type:        enum.TaxTypeTaxAndFee,
		Description: in.Name,
	}
}

func ToDomainHubRatesFromPriceList(dailyRate []*models.Payment, rate *models.Payment, dayCount, roomCount int) []*domain.HubRate {
	result := make([]*domain.HubRate, 0, dayCount)

	if len(dailyRate) == dayCount {
		for _, r := range dailyRate {
			if r == nil {
				continue
			}

			hubRate := ToDomainHubRate(r, roomCount)
			hubRate.Currency = rate.Currency

			result = append(result, hubRate)
		}
	} else {
		if rate == nil {
			return nil
		}

		for i := 0; i < dayCount; i++ {
			result = append(result, ToDomainHubRate(rate, 1))
		}
	}

	return result
}

func ToDomainHubRate(r *models.Payment, roomCount int) *domain.HubRate {
	if r == nil {
		return nil
	}

	return &domain.HubRate{
		RateBasic: r.Exclusive / float64(roomCount),
		TaxAmount: (r.Tax + r.Fees) / float64(roomCount),
		Currency:  r.Currency,
	}
}

func ToHubAmenities(in []*models.Benefit) []*domain.HubAmenity {
	result := make([]*domain.HubAmenity, 0, len(in))

	for _, i := range in {
		result = append(result, ToHubAmenity(i))
	}

	return result
}

func ToHubAmenity(in *models.Benefit) *domain.HubAmenity {
	if in == nil {
		return nil
	}

	return &domain.HubAmenity{
		ID:   fmt.Sprintf("ag%d", in.ID),
		Name: in.TranslatedBenefitName,
	}
}

func ProcessCancellationPolicies(
	cancelPolicy *models.CancellationPolicy,
	dailyRates []*models.Payment,
	checkOut string,
	roomCount int,
	timezone string,
) (bool, []*domain.HubCancelPolicy) {
	if cancelPolicy == nil || len(cancelPolicy.Date) == 0 {
		return false, nil
	}

	// Parse timezone khách sạn
	hotelLoc := time.FixedZone("hotelTZ", ParseOffsetToSeconds(timezone))
	now := time.Now().In(hotelLoc)

	// Parse checkout date
	checkOutTime, err := time.ParseInLocation("2006-01-02", checkOut, hotelLoc)
	if err != nil {
		return false, nil
	}

	endOfCheckout := time.Date(
		checkOutTime.Year(), checkOutTime.Month(), checkOutTime.Day(),
		23, 59, 59, 0, hotelLoc,
	)

	// Tính tổng inclusive * roomCount
	var totalInclusive float64
	var currency string
	for _, r := range dailyRates {
		totalInclusive += r.Inclusive
		if currency == "" {
			currency = r.Currency
		}
	}

	totalInclusive = helpers.RoundToPrecision(totalInclusive, 2)

	var policies []*domain.HubCancelPolicy
	refundable := false
	var lastEnd time.Time = now

	// --- Mapping cancel_policies ---
	for i, p := range cancelPolicy.Date {
		if p.Rate == nil {
			continue
		}

		inclusive := helpers.RoundToPrecision(p.Rate.Inclusive, 2)

		var startDate, endDate time.Time
		var penaltyAmount float64
		localRefundable := true

		// --- Case Onward (chỉ ở object cuối cùng) ---
		if p.Onward != "" && i == len(cancelPolicy.Date)-1 {
			tOnward, err := time.ParseInLocation("2006-01-02T15:04:05", p.Onward, hotelLoc)
			if err != nil {
				continue
			}

			startDate = tOnward
			endDate = endOfCheckout

			if inclusive >= totalInclusive {
				localRefundable = false
			} else {
				localRefundable = true
			}

			if localRefundable {
				penaltyAmount = inclusive
			} else {
				penaltyAmount = 0
			}

			lastEnd = endDate

		} else if p.Before != "" {
			// --- Case Before ---
			tBefore, err := time.ParseInLocation("2006-01-02T15:04:05", p.Before, hotelLoc)
			if err != nil {
				continue
			}

			if tBefore.After(now) {
				startDate = lastEnd
				endDate = tBefore

				if AlmostEqual(inclusive, 0) {
					lastEnd = tBefore
					continue
				}

				if inclusive >= totalInclusive {
					localRefundable = false
				} else {
					localRefundable = true
				}

				if localRefundable {
					penaltyAmount = inclusive
				} else {
					penaltyAmount = 0
				}

				lastEnd = endDate
			} else {
				lastEnd = tBefore
				continue
			}
		}

		policies = append(policies, &domain.HubCancelPolicy{
			StartDate:     startDate.Format(time.RFC3339),
			EndDate:       endDate.Format(time.RFC3339),
			PenaltyAmount: penaltyAmount,
			Refundable:    localRefundable,
			PenaltyInfo: domain.HubCancelPolicyPenaltyInfo{
				Amount: penaltyAmount,
			},
			Currency: p.Rate.Currency,
		})
	}

	// --- Mapping refundable ngoài cùng ---
	refundable = false
	for idx := 0; idx < len(cancelPolicy.Date); idx++ {
		p := cancelPolicy.Date[idx]
		if p.Rate == nil {
			continue
		}

		inclusive := p.Rate.Inclusive
		if AlmostEqual(inclusive, 0) {
			refundable = true
			break
		}

		if p.Onward != "" {
			if tOnward, err := time.ParseInLocation("2006-01-02T15:04:05", p.Onward, hotelLoc); err == nil {
				if tOnward.After(now) {
					refundable = true
				} else {
					refundable = inclusive < totalInclusive
				}
			}

			break
		}

		if p.Before != "" {
			if tBefore, err := time.ParseInLocation("2006-01-02T15:04:05", p.Before, hotelLoc); err == nil {
				if tBefore.After(now) {
					refundable = inclusive < totalInclusive
					break
				} else {
					// xét object tiếp theo nếu còn
					if idx+1 < len(cancelPolicy.Date) {
						continue
					}
					refundable = false
				}
			}
		}
	}

	return refundable, policies
}

func ToPreCheckReq(req *domain.HotelSearchResult, rate *domain.HubRateData, result *domain.HubHotel, currency string) (*models.PreCheckReq, error) {
	if req == nil || rate == nil || result == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	return &models.PreCheckReq{
		PrecheckDetails: ToPreCheckDetail(req, rate, result, currency),
	}, nil
}

func ToPreCheckDetail(req *domain.HotelSearchResult, rate *domain.HubRateData, result *domain.HubHotel, currency string) *models.PrecheckDetails {
	return &models.PrecheckDetails{
		SearchID:         req.AgodaSearchID,
		AllowDuplication: true,
		CheckIn:          req.HotelSearchRequest.Stay.CheckIn,
		CheckOut:         req.HotelSearchRequest.Stay.CheckOut,
		Language:         constants.DefaultLanguage,
		UserCountry:      constants.DefaultCountry,
		Property:         ToProperty(req, rate, result, currency),
	}
}

func ToProperty(req *domain.HotelSearchResult, rate *domain.HubRateData, result *domain.HubHotel, currency string) *models.Property {
	if rate == nil || result == nil {
		return nil
	}

	hotelID, err := strconv.Atoi(result.ProviderHotelID)
	if err != nil {
		return nil
	}

	room := ToRoom(req, rate, result, currency)

	return &models.Property{
		PropertyID: hotelID,
		Rooms:      []*models.Room{room},
	}
}

func ToRoom(req *domain.HotelSearchResult, rate *domain.HubRateData, result *domain.HubHotel, currency string) *models.Room {
	if rate == nil || result == nil || rate.AdditionalData == nil || rate.AdditionalData.AgodaCheckAvaibilityInfo == nil {
		return nil
	}

	adultCount, childCount, childAges := ProcessOccupancies(len(rate.OccupancyRate), req.HotelSearchRequest.Occupancies)

	return &models.Room{
		Currency:     currency,
		PaymentModel: rate.AdditionalData.AgodaCheckAvaibilityInfo.PaymentModel,
		BlockID:      rate.ProviderRateID,
		Count:        len(rate.OccupancyRate),
		Adults:       adultCount,
		Children:     childCount,
		ChildrenAges: childAges,
		Rate:         ToRoomRate(rate.AdditionalData.AgodaCheckAvaibilityInfo.Rate),
		Surcharges:   ToRoomSurcharges(rate.AdditionalData.AgodaCheckAvaibilityInfo.Surcharges),
	}
}

func ToRoomRate(in *domain.AgodaRate) *models.Payment {
	if in == nil {
		return nil
	}

	return &models.Payment{
		Inclusive: in.Inclusive,
	}
}

func ToRoomSurcharges(in []*domain.Surcharge) []*models.Surcharge {
	result := make([]*models.Surcharge, 0, len(in))

	for _, i := range in {
		result = append(result, ToRoomSurcharge(i))
	}

	return result
}

func ToRoomSurcharge(in *domain.Surcharge) *models.Surcharge {
	if in == nil {
		return nil
	}

	return &models.Surcharge{
		ID:   in.ID,
		Rate: ToRoomRate(in.Rate),
	}
}
