package agoda_client

import (
	"context"
	"net/http"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type AgodaAdapter interface {
	SearchHotel(ctx context.Context, req *domain.SearchAdapterReq, tracingID string) ([]*domain.HotelSummary, error)
	CheckAvailability(ctx context.Context, req *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, int64, error)
	PriceCheck(ctx context.Context, req *domain.HotelSearchResult, hotelID string, rate *domain.HubRateData, result *domain.HubHotel, priceCheckReq *domain.HubPriceCheckReq) (*domain.HubRateData, *domain.AgodaSessionInfo, error)
	RetrieveBooking(ctx context.Context, req *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error)
	CancelBooking(ctx context.Context, req *domain.HubHotelOrder, tracingID string) (float64, float64, error)
	Booking(ctx context.Context, req *domain.HubBookReq, order *domain.HubHotelOrder, session *domain.AgodaSessionInfo, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error)
	RetrieveForRefund(ctx context.Context, req *domain.HubHotelOrder, paymentAmount, refundAmount float64, tracingID string) (bool, *domain.RefundData, error)
}

type agodaAdapter struct {
	agodaClient client.AgodaClient
	cfg         *config.Schema
}

func NewAgodaAdapter(cfg *config.Schema, redis redis.IRedis, requestRepo repositories.RequestRepository) AgodaAdapter {
	return &agodaAdapter{
		agodaClient: client.NewAgodaClient(cfg, requestRepo),
		cfg:         cfg,
	}
}

func (a *agodaAdapter) SearchHotel(ctx context.Context, req *domain.SearchAdapterReq, tracingID string) ([]*domain.HotelSummary, error) {
	if req.ProviderHotelIds == nil || req.HubRequest == nil {
		return nil, commonErrors.ErrInvalidInput
	}

	clientReq := converts.ToAgodaSearchHotelReq(req, a.cfg.AgodaCurrency)

	res, err := a.agodaClient.SearchHotel(ctx, clientReq, tracingID)
	if err != nil {
		return nil, errors.Wrap(err, "agodaClient.SearchHotel")
	}

	return converts.ToDomainHotelSummaries(res, req.HubRequest), nil
}

func (a *agodaAdapter) CheckAvailability(ctx context.Context, req *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, int64, error) {
	if req.ProviderHotelID == "" {
		return nil, "", 0, commonErrors.ErrInvalidInput
	}

	clientReq, err := converts.ToAgodaCheckAvailabilityReq(req, a.cfg.AgodaCurrency)
	if err != nil {
		return nil, "", 0, errors.Wrap(err, "converts.ToDidaCheckAvailabilityReq")
	}

	res, err := a.agodaClient.SearchHotel(ctx, clientReq, req.TracingID)
	if err != nil {
		return nil, "", 0, errors.Wrap(err, "client.PriceSearch")
	}

	domainRooms := converts.ToDomainRooms(res, req)

	return domainRooms, a.cfg.AgodaCurrency, res.SearchID, nil
}

func (a *agodaAdapter) PriceCheck(ctx context.Context, req *domain.HotelSearchResult, hotelID string, rate *domain.HubRateData, result *domain.HubHotel, priceCheckReq *domain.HubPriceCheckReq) (*domain.HubRateData, *domain.AgodaSessionInfo, error) {
	if req == nil {
		log.Error("PriceCheck req == nil")
		return nil, nil, domain.ErrInvalidValue
	}

	preCheckReq, err := converts.ToPreCheckReq(req, rate, result, a.cfg.AgodaCurrency)
	if err != nil {
		return nil, nil, err
	}

	preCheckReq.PrecheckDetails.TestingHashKey = priceCheckReq.TestingHashKey

	response, err := a.agodaClient.PriceCheck(ctx, preCheckReq, req.SearchKey)
	if err != nil {
		log.Error("agodaClient.PriceCheck error", log.Any("req", req), log.Any("err", err))
		return nil, nil, errors.Wrap(err, "agodaClient.PriceCheck")
	}

	switch response.Status {
	case http.StatusNotImplemented:
		return nil, nil, domain.ErrRateNotAvailable
	case http.StatusBadGateway, http.StatusServiceUnavailable:
		return nil, nil, domain.ErrRoomSoldOut
	}

	session := &domain.AgodaSessionInfo{
		SearchID:     req.AgodaSearchID,
		PaymentModel: rate.AdditionalData.AgodaCheckAvaibilityInfo.PaymentModel,
	}

	return rate, session, nil
}

func (a *agodaAdapter) RetrieveBooking(ctx context.Context, req *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	if req == nil {
		log.Error("AgodaAdapter.RetrieveBooking: req == nil")
		return "", nil, enum.BookingStatusPending, false, domain.ErrInvalidValue
	}

	bookingID, err := strconv.ParseInt(req.ReservationCode, 10, 64)
	if err != nil {
		return "", nil, enum.BookingStatusPending, false, err
	}

	clientReq := &models.RetrieveBookingReq{
		BookingIDs: []int64{bookingID},
	}

	res, err := a.agodaClient.RetrieveBooking(ctx, clientReq, tracingID)
	if err != nil || res == nil || len(res.Bookings) == 0 {
		log.Error("retrieveBooking failed", log.Any("bookingID", bookingID), log.Any("err", err))
		return "", nil, enum.BookingStatusPending, false, errors.New("retrieve booking failed")
	}

	return a.processBookingList(res.Bookings[0], req.Hotel.ListRooms)
}

func (a *agodaAdapter) CancelBooking(ctx context.Context, req *domain.HubHotelOrder, tracingID string) (float64, float64, error) {
	if req == nil {
		log.Error("AgodaAdapter.CancelBooking: req == nil")
		return 0, 0, domain.ErrInvalidValue
	}

	bookingID, err := strconv.ParseInt(req.ReservationCode, 10, 64)
	if err != nil {
		return 0, 0, errors.Wrap(err, "agodaClient.CancelBooking")
	}

	clientReq := &models.CancelBookingReq{
		BookingID: bookingID,
	}

	res, err := a.agodaClient.CancelBooking(ctx, clientReq, tracingID)
	if err != nil || res == nil {
		log.Error("agodaClient.CancelBooking error", log.Any("req", req), log.Any("err", err))
		return 0, 0, constants.ErrSomethingError
	}

	var (
		payment float64
		refund  float64
	)

	if res.CancellationSummary != nil && len(res.CancellationSummary.PaymentRate) > 0 {
		payment = res.CancellationSummary.PaymentRate[0].Inclusive
	}

	if res.CancellationSummary != nil && len(res.CancellationSummary.RefundRate) > 0 {
		refund = res.CancellationSummary.RefundRate[0].Inclusive
	}

	if refund <= 0 {
		return 0, 0, domain.ErrBookingNotAllowedToCancel
	}

	cfReq := &models.ConfirmCancelBookingReq{
		BookingID:    res.CancellationSummary.BookingID,
		Reference:    res.CancellationSummary.Reference,
		RefundRate:   res.CancellationSummary.RefundRate[0],
		CancelReason: 0,
	}

	cfRes, err := a.agodaClient.ConfirmCancelBooking(ctx, cfReq, tracingID)
	if err != nil {
		if cfRes != nil && cfRes.ErrorMessage.ID == constants.ErrorID909 && cfRes.ErrorMessage.Message == constants.ErrMsgCancellationRequestIsPending {
			log.Info("Pending cancellation, proceed with RetrieveBooking flow")
			return payment, refund, nil
		}

		return 0, 0, errors.Wrap(err, "ConfirmCancelBooking failed")
	}

	return payment, refund, nil
}

func (a *agodaAdapter) RetrieveForRefund(ctx context.Context, req *domain.HubHotelOrder, paymentAmount, refundAmount float64, tracingID string) (bool, *domain.RefundData, error) {
	bookingID, err := strconv.ParseInt(req.ReservationCode, 10, 64)
	if err != nil {
		return false, nil, errors.Wrap(err, "client.Retrieve")
	}

	var lastErr error

	// cần retrieve cho tới khi đã cancel
	for i := 0; i < constants.MaxRetry; i++ {
		select {
		case <-ctx.Done():
			return false, nil, ctx.Err()
		default:
		}

		clientReq := &models.RetrieveBookingReq{
			BookingIDs: []int64{bookingID},
		}

		res, err := a.agodaClient.RetrieveBooking(ctx, clientReq, tracingID)
		if err != nil || res == nil || len(res.Bookings) == 0 {
			log.Error("retrieveBooking failed", log.Any("bookingID", bookingID), log.Any("err", err))
			return false, nil, errors.Wrap(err, "client.Retrieve")
		}

		currency := constants.DefaultCurrency
		if len(res.Bookings[0].TotalRates) > 0 && res.Bookings[0].TotalRates[0].Currency != "" {
			currency = res.Bookings[0].TotalRates[0].Currency
		}

		if res.Bookings[0].Status == constants.BookingStatusCancelledByCustomer || res.Bookings[0].Status == constants.BookingStatusCancelled {
			return true, &domain.RefundData{
				ProviderRefundAmount: refundAmount,
				PenaltyAmount:        paymentAmount - refundAmount,
				Currency:             currency,
			}, nil
		}

		log.Info("Booking not yet cancelled, retrying...", log.String("status", res.Bookings[0].Status))
		time.Sleep(constants.RetryInterval)
	}

	return false, nil, errors.Wrap(lastErr, "booking not cancelled after retries")
}

func (a *agodaAdapter) Booking(ctx context.Context, req *domain.HubBookReq, order *domain.HubHotelOrder, session *domain.AgodaSessionInfo, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, false, domain.ErrInvalidValue
	}

	bookingReq := converts.ToBookingReq(req, order, session, a.cfg)

	// 1. Gọi Booking API
	response, err := a.agodaClient.Booking(ctx, bookingReq, order.OrderCode)
	if errors.Is(err, context.DeadlineExceeded) {
		log.Warn("Booking timeout, fallback to BookList", log.Any("orderCode", order.OrderCode))
		return a.handleBookingFallback(ctx, order, req.TestingHashKey, tracingID)
	}

	// 2. Kiểm tra các mã lỗi nếu có errorMessage
	if response.ErrorMessage != nil {
		errID := response.ErrorMessage.ID
		errMsg := response.ErrorMessage.Message

		log.Error("Booking errorMessage received", log.Any("id", errID), log.Any("message", errMsg))

		// Xét các mã lỗi khiến booking failed
		switch errID {
		case constants.ErrorID909:
			if errMsg == constants.ErrMsgNoAvailableRooms {
				return "", nil, enum.BookingStatusFailed, false, domain.ErrRoomSoldOut
			}

			failedMsgs := []string{
				constants.ErrMsgInvalidUserID,
				constants.ErrMsgSiteIDMismatchedWithRefID,
				constants.ErrMsgHotelNameEmpty,
				constants.ErrMsgCategoryEmpty,
				constants.ErrMsgInvalidSpecialRequest,
			}

			for _, m := range failedMsgs {
				if errMsg == m {
					return "", nil, enum.BookingStatusFailed, false, domain.ErrInvalidInput
				}
			}
		case constants.ErrorID940:
			if errMsg == constants.ErrMsgHotelNameEmpty {
				return "", nil, enum.BookingStatusFailed, false, domain.ErrInvalidInput
			}
		}

		// Các mã lỗi còn lại trả về pending
		return "", nil, enum.BookingStatusPending, true, domain.ErrUnknown
	}

	// 3. Xử lý nếu bookingDetails vẫn có ID
	bookingID := int64(0)
	if len(response.BookingDetails) > 0 {
		bookingID = response.BookingDetails[0].ID
	}

	if bookingID != 0 {
		log.Info("Calling retrieve due to presence of BookingDetails.ID", log.Any("bookingID", bookingID), log.Any("status", response.Status))
		return a.retrieveAndProcessBooking(ctx, bookingID, order, req.TestingHashKey, tracingID)
	}

	// 4. Fallback nếu không có bookingDetails.ID
	log.Warn("Missing BookingDetails.ID, fallback to BookList", log.Any("status", response.Status), log.Any("orderCode", order.OrderCode))
	return a.handleBookingFallback(ctx, order, req.TestingHashKey, tracingID)
}

func (a *agodaAdapter) handleBookingFallback(ctx context.Context, order *domain.HubHotelOrder, testKey string, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	bookListReq := &models.ListBookingReq{Tags: []string{order.OrderCode}, TestingHashKey: testKey}

	var res *models.ListBookingRes
	var err error

	for i := 0; i < 3; i++ {
		res, err = a.agodaClient.ListBooking(ctx, bookListReq, tracingID)
		if err == nil && res != nil && len(res.Bookings) > 0 {
			break
		}

		time.Sleep(constants.RetryInterval)
	}

	if err != nil || res == nil || len(res.Bookings) == 0 {
		log.Error("handleBookingFallback failed", log.Any("orderCode", order.OrderCode), log.Any("err", err))
		return "", nil, enum.BookingStatusPending, true, errors.New("unable to confirm booking via fallback")
	}

	return a.processBookingList(res.Bookings[0], order.Hotel.ListRooms)
}

func (a *agodaAdapter) retrieveAndProcessBooking(ctx context.Context, bookingID int64, order *domain.HubHotelOrder, testKey string, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	req := &models.RetrieveBookingReq{BookingIDs: []int64{bookingID}, TestingHashKey: testKey}

	var res *models.RetrieveBookingRes
	var err error

	// Gọi tối đa 3 lần nếu kết quả trả về không hợp lệ (mảng bookings rỗng)
	for i := 0; i < 3; i++ {
		res, err = a.agodaClient.RetrieveBooking(ctx, req, tracingID)
		if err == nil && res != nil && len(res.Bookings) > 0 {
			return a.processBookingList(res.Bookings[0], order.Hotel.ListRooms)
		}

		time.Sleep(constants.RetryInterval)
	}

	// Nếu sau 3 lần vẫn không có booking → Pending
	return "", nil, enum.BookingStatusPending, true, errors.New("retrieve booking failed after retries")
}

func (a *agodaAdapter) processBookingList(booking *models.Booking, hubRooms []*domain.HubOrderRoomItem) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	if booking == nil {
		return "", nil, enum.BookingStatusPending, false, errors.New("empty booking data")
	}

	hotelConfirmationID := ""
	confirmationID := strconv.Itoa(int(booking.BookingID))

	if booking.SupplierReference != "" && booking.SupplierReference != constants.SupplierReference {
		hotelConfirmationID = booking.SupplierReference
	}

	bookingStatus, unknownPending := converts.CheckBookingStatus(booking.Status)
	confirmationIDs := make([]*domain.HubRetrieveConfirmationID, 0, len(hubRooms))

	for _, hubRoom := range hubRooms {
		confirmationIDs = append(confirmationIDs, converts.ToHubRetrieveConfirmationID(hubRoom, bookingStatus, confirmationID, hotelConfirmationID))
	}

	return strconv.Itoa(int(booking.BookingID)), confirmationIDs, bookingStatus, unknownPending, nil
}
