package client

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	tracingHttp "gitlab.deepgate.io/apps/common/tracing/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (c *agodaClient) getHeader() map[string]string {
	return map[string]string{
		"Authorization": fmt.Sprintf("%s:%s", c.siteID, c.apiKey),
		"Content-Type":  "application/json",
	}
}

func (c *agodaClient) doRequest(
	ctx context.Context,
	method string,
	fullPath string,
	header map[string]string,
	body interface{},
	action string,
	tracingID string,
	responseData interface{},
) error {
	var err error
	var data []byte

	raw, err := json.Marshal(body)
	if err != nil {
		log.Error("Agoda Marshal error",
			log.Any("error", err),
			log.String("relativePath", fullPath),
			log.String("action", action),
			log.Any("req", body))

		return err
	}

	m := map[string]interface{}{}
	if err := json.Unmarshal(raw, &m); err == nil {
		delete(m, "testing_hash_key")
		data, err = json.Marshal(m)
		if err != nil {
			return errors.Wrap(err, "marshal sanitized body")
		}
	} else {
		data = raw
	}

	response, statusCode, duration, err := c.do(ctx, header, fullPath, method, data)

	headerClone := map[string]string{}

	for key, val := range header {
		headerClone[key] = val
	}

	go func(headerClone map[string]string) {
		path := fullPath
		bCtx, cancel := context.WithTimeout(context.Background(), constants.RequestRepoCtxTimeout)

		defer cancel()

		requestID := uuid.NewString()

		if c.requestRepo == nil {
			return
		}

		delete(headerClone, "Authorization")
		bodyToLog := c.hideClientInfoRequest(data)

		doErr := err

		if err := c.requestRepo.Create(bCtx, &repositories.Request{
			RequestID:  requestID,
			Path:       path,
			Method:     methodPost,
			Body:       bodyToLog,
			Headers:    headerClone,
			Response:   response,
			StatusCode: statusCode,
			Duration:   duration,
			Action:     action,
			TracingID:  tracingID,
			IsJson:     true,
			ErrorMsg:   doErr,
			Provider:   commonEnum.HotelProviderAgoda,
		}); err != nil {
			log.Error("Agoda client request",
				log.Any("error", err),
				log.String("requestID", requestID),
				log.String("path", path),
				log.String("action", action),
				log.Any("req", body),
			)
		}
	}(headerClone)

	if statusCode != http.StatusNoContent {
		if err := json.Unmarshal(response, responseData); err != nil {
			return errors.Wrap(err, "Unmarshal")
		}
	}

	return nil
}

func (c *agodaClient) do(
	ctx context.Context,
	header map[string]string,
	fullPath string,
	method string,
	body []byte,
) ([]byte, int, int64, error) {
	var duration int64
	beginAt := time.Now()

	var response *http.Response

	payload := bytes.NewBuffer(body)

	response, err := tracingHttp.RawRequest(ctx, fullPath, method, payload, header)
	if err != nil {
		duration = time.Since(beginAt).Milliseconds()
		return nil, 0, duration, errors.Wrap(err, "tracingHttp.RawRequest")
	}

	duration = time.Since(beginAt).Milliseconds()

	defer response.Body.Close()

	resBody, err := io.ReadAll(response.Body)
	if err != nil {
		return resBody, response.StatusCode, duration, errors.Wrap(err, "io.ReadAll")
	}

	if response.StatusCode >= http.StatusBadRequest {
		err := errors.New(response.Status)
		return resBody, response.StatusCode, duration, errors.Wrap(err, "response status code not success")
	}

	return resBody, response.StatusCode, duration, nil
}

func (c *agodaClient) hideClientInfoRequest(body []byte) []byte {
	var data map[string]interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return body
	}

	delete(data, "paymentDetails")

	newBody, err := json.Marshal(data)
	if err != nil {
		return body
	}

	return newBody
}
