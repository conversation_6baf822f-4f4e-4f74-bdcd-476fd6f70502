package client

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	ct "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const (
	successfulCode             = 200
	methodGet                  = "GET"
	methodPost                 = "POST"
	defaultRequestTimeout      = 60
	actionSearchHotel          = "search_hotel"
	actionCheckAvaibility      = "check_avaibility"
	actionPriceCheckHotel      = "price_check_hotel"
	actionBookingHotel         = "booking_hotel"
	actionRetrieveBookingHotel = "retrieve_booking_hotel"
	actionCancelBooking        = "cancel_booking"
	actionConfirmCancelBooking = "confirm_cancel_booking"
	actionListBooking          = "list_booking"

	searchHotelPath            = "/v4/property/availability"
	checkAvailabilityHotelPath = "/v4/property/availabilit"
	priceCheckHotelPath        = "/v2/prebooking/precheck"
	retrieveBookingHotelPath   = "/v4/bookingreport/bookingdetail"
	bookingHotelPath           = "/v4/book"
	cancelBookingPath          = "/v4/postBooking/cancel"
	confirmCancelBookingPath   = "/v4/postbooking/confirmcancel"
	listBookingPath            = "/v4/bookingreport/booklist"
)

type AgodaClient interface {
	SearchHotel(ctx context.Context, req *models.SearchHotelReq, tracingID string) (*models.SearchHotelRes, error)
	PriceCheck(ctx context.Context, req *models.PreCheckReq, tracingID string) (*models.PreCheckRes, error)
	Booking(ctx context.Context, req *models.BookingReq, tracingID string) (*models.BookingRes, error)
	ListBooking(ctx context.Context, req *models.ListBookingReq, tracingID string) (*models.ListBookingRes, error)
	RetrieveBooking(ctx context.Context, req *models.RetrieveBookingReq, tracingID string) (*models.RetrieveBookingRes, error)
	CancelBooking(ctx context.Context, req *models.CancelBookingReq, tracingID string) (*models.CancelBookingRes, error)
	ConfirmCancelBooking(ctx context.Context, req *models.ConfirmCancelBookingReq, tracingID string) (*models.ConfirmCancelBookingRes, error)
}

type agodaClient struct {
	cfg           *config.Schema
	searchURL     string
	priceCheckURL string
	bookingURL    string
	siteID        string
	apiKey        string
	requestRepo   repositories.RequestRepository
}

func NewAgodaClient(cfg *config.Schema, requestRepo repositories.RequestRepository) AgodaClient {
	return &agodaClient{
		cfg:           cfg,
		searchURL:     cfg.AgodaSearchURL,
		priceCheckURL: cfg.AgodaPriceCheckURL,
		bookingURL:    cfg.AgodaBookingURL,
		siteID:        cfg.AgodaSiteID,
		apiKey:        cfg.AgodaApiKey,
		requestRepo:   requestRepo,
	}
}

func (c *agodaClient) SearchHotel(ctx context.Context, req *models.SearchHotelReq, tracingID string) (*models.SearchHotelRes, error) {
	response := &models.SearchHotelRes{}

	err := c.doRequest(ctx, methodPost, c.searchURL+searchHotelPath, c.getHeader(), req, actionSearchHotel, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *agodaClient) PriceCheck(ctx context.Context, req *models.PreCheckReq, tracingID string) (*models.PreCheckRes, error) {
	response := &models.PreCheckRes{}

	if req.PrecheckDetails.TestingHashKey != "" {
		actionMap, actionExists := ct.TestCaseMap[commonEnum.HotelProviderAgoda]
		if !actionExists {
			return nil, errors.New("provider action map not found")
		}

		if _, actionExists := actionMap[ct.ActionPriceCheck][req.PrecheckDetails.TestingHashKey]; actionExists {
			if value := pkgConstants.GetTestCaseValue(commonEnum.HotelProviderAgoda, pkgConstants.ActionPriceCheck, req.PrecheckDetails.TestingHashKey); value != "" {
				parsedValue, err := strconv.ParseInt(value, 10, 64)
				if err != nil {
					return nil, fmt.Errorf("invalid test case value %q: %v", value, err)
				}

				response.Status = parsedValue
				return response, nil
			}

			return nil, errors.New("provider action map not found")
		}
	}

	err := c.doRequest(ctx, methodPost, c.priceCheckURL+priceCheckHotelPath, c.getHeader(), req, actionPriceCheckHotel, tracingID, &response)
	if err != nil {
		return response, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *agodaClient) Booking(ctx context.Context, req *models.BookingReq, tracingID string) (*models.BookingRes, error) {
	if req.TestingHashKey != "" {
		actionMap, actionExists := ct.TestCaseMap[commonEnum.HotelProviderAgoda]
		if !actionExists {
			return nil, errors.New("provider action map not found")
		}

		if _, actionExists := actionMap[ct.ActionCreateBooking][req.TestingHashKey]; actionExists {
			resMock := &models.BookingRes{
				ErrorMessage: &models.ErrorMessage{
					ID:      "",
					Message: "",
				},
			}

			if value := pkgConstants.GetTestCaseValue(commonEnum.HotelProviderAgoda, pkgConstants.ActionCreateBooking, req.TestingHashKey); value != "" {
				resMock.TestingHashKeyMatch = true

				if value == "Unknown" {
					resMock.ErrorMessage.Message = value
					return resMock, nil
				}

				parts := strings.SplitN(value, "-", 2)
				if len(parts) == 2 {
					resMock.ErrorMessage.ID = parts[0]
					resMock.ErrorMessage.Message = parts[1]
				} else {
					resMock.ErrorMessage.Message = value
				}

				return resMock, nil
			}

			// return nil, errors.New("provider action map not found")
		}
	}

	response := &models.BookingRes{}

	err := c.doRequest(ctx, methodPost, c.bookingURL+bookingHotelPath, c.getHeader(), req, actionBookingHotel, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return response, nil
}

func (c *agodaClient) RetrieveBooking(ctx context.Context, req *models.RetrieveBookingReq, tracingID string) (*models.RetrieveBookingRes, error) {
	response := &models.RetrieveBookingRes{}

	err := c.doRequest(ctx, methodGet, c.bookingURL+retrieveBookingHotelPath, c.getHeader(), req, actionRetrieveBookingHotel, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if req.TestingHashKey != "" {
		actionMap, actionExists := ct.TestCaseMap[commonEnum.HotelProviderAgoda]
		if !actionExists {
			return nil, errors.New("provider action map not found")
		}

		if _, actionExists := actionMap[ct.ActionOrderBookingFinishStatus][req.TestingHashKey]; actionExists {
			if value := pkgConstants.GetTestCaseValue(commonEnum.HotelProviderAgoda, pkgConstants.ActionOrderBookingFinishStatus, req.TestingHashKey); value != "" {
				response.TestingHashKeyMatch = true
				for _, res := range response.Bookings {
					res.Status = value
				}

				return response, nil
			}

			return nil, errors.New("provider action map not found")
		}
	}

	return response, nil
}

func (c *agodaClient) CancelBooking(ctx context.Context, req *models.CancelBookingReq, tracingID string) (*models.CancelBookingRes, error) {
	res := &models.CancelBookingRes{}

	err := c.doRequest(ctx, methodPost, c.bookingURL+cancelBookingPath, c.getHeader(), req, actionCancelBooking, tracingID, &res)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	return res, nil
}

func (c *agodaClient) ConfirmCancelBooking(ctx context.Context, req *models.ConfirmCancelBookingReq, tracingID string) (*models.ConfirmCancelBookingRes, error) {
	res := &models.ConfirmCancelBookingRes{}

	err := c.doRequest(ctx, methodPost, c.bookingURL+confirmCancelBookingPath, c.getHeader(), req, actionConfirmCancelBooking, tracingID, res)
	if err != nil {
		return res, errors.Wrap(err, "doRequest")
	}

	return res, nil
}

func (c *agodaClient) ListBooking(ctx context.Context, req *models.ListBookingReq, tracingID string) (*models.ListBookingRes, error) {
	response := &models.ListBookingRes{}

	err := c.doRequest(ctx, methodPost, c.bookingURL+listBookingPath, c.getHeader(), req, actionListBooking, tracingID, &response)
	if err != nil {
		return nil, errors.Wrap(err, "doRequest")
	}

	if req.TestingHashKey != "" {
		actionMap, actionExists := ct.TestCaseMap[commonEnum.HotelProviderAgoda]
		if !actionExists {
			return nil, errors.New("provider action map not found")
		}

		if _, actionExists := actionMap[ct.ActionOrderBookingFinishStatus][req.TestingHashKey]; actionExists {
			if value := pkgConstants.GetTestCaseValue(commonEnum.HotelProviderAgoda, pkgConstants.ActionOrderBookingFinishStatus, req.TestingHashKey); value != "" {
				response.TestingHashKeyMatch = true
				for _, res := range response.Bookings {
					res.Status = value
				}

				return response, nil
			}

			return nil, errors.New("provider action map not found")
		}
	}

	return response, nil
}
