package entity

type UpdateConfirmationIDReq struct {
	HubBookingCode   string     `json:"hub_booking_code"`
	Rooms            []*HubRoom `json:"rooms"`
	Signature        string     `json:"signature"`
	HubPartnershipID string     `json:"hub_partnership_id"`
	OfficeID         string     `json:"office_id"`
}

type HubRoom struct {
	RoomID         string `json:"room_id"`
	Name           string `json:"name"`
	ConfirmationID string `json:"confirmation_id,omitempty"`
	OccupancyIndex uint   `json:"occupancy_index,omitempty"`
	OccupancyType  string `json:"occupancy_type,omitempty"`
}
