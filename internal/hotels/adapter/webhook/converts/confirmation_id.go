package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook/entity"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func ToUpdateConfirmationID(in *domain.HubHotelOrder, hID string) *entity.UpdateConfirmationIDReq {
	if in == nil {
		return nil
	}

	return &entity.UpdateConfirmationIDReq{
		HubBookingCode:   in.OrderCode,
		Rooms:            ToRoomsEntity(in.Hotel.ListRooms),
		HubPartnershipID: hID,
		OfficeID:         in.OfficeID,
	}
}

func ToRoomsEntity(rooms []*domain.HubOrderRoomItem) []*entity.HubRoom {
	result := make([]*entity.HubRoom, 0, len(rooms))
	for _, room := range rooms {
		result = append(result, &entity.HubRoom{
			RoomID:         room.RoomID,
			OccupancyIndex: room.OccupancyIndex,
			Name:           room.Name,
			ConfirmationID: room.ConfirmationID,
			OccupancyType:  room.OccupancyType,
		})
	}

	return result
}
