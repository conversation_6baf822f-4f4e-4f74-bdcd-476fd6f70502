package tourmind_client

import (
	"context"

	"github.com/pkg/errors"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type TourmindAdapter interface {
	SearchHotel(ctx context.Context, req *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error)
	PriceCheck(ctx context.Context, searchReq *domain.CacheCheckAvailabilityRequest, hubHotel *domain.HubHotel, rate *domain.HubRateData, tracingID string) (*domain.HubRateData, *domain.TourmindSessionInfo, error)
	Book(ctx context.Context, req *domain.HubBookReq, session *domain.TourmindSessionInfo, order *domain.HubHotelOrder, tracingID string) (string, string, enum.BookingStatus, error)
	// Aggregate contents
	HotelStaticList(ctx context.Context, req *entities.HotelStaicListReq, tracingID string) (*entities.HotelStaicListRes, error)
}

type tourmindAdapter struct {
	cfg    *config.Schema
	client client.TourmindClient
}

func NewTourmindAdapter(cfg *config.Schema, reqRepo repositories.RequestRepository) TourmindAdapter {
	return &tourmindAdapter{
		cfg:    cfg,
		client: client.NewTourmindClient(cfg, reqRepo),
	}
}

func (a *tourmindAdapter) HotelStaticList(ctx context.Context, req *entities.HotelStaicListReq, tracingID string) (*entities.HotelStaicListRes, error) {
	return a.client.HotelStaticList(ctx, req, tracingID)
}

func (a *tourmindAdapter) PriceCheck(ctx context.Context, searchReq *domain.CacheCheckAvailabilityRequest, hubHotel *domain.HubHotel, rate *domain.HubRateData, tracingID string) (*domain.HubRateData, *domain.TourmindSessionInfo, error) {
	return nil, nil, errors.New("Not support")
	// clientReq, err := converts.ToCheckRoomRateReq(searchReq, hubHotel, rate)
	// if err != nil {
	// 	return nil, nil, errors.Wrap(err, "converts.ToCheckRoomRateReq")
	// }
	// res, err := a.client.CheckRoomRate(ctx, clientReq, tracingID)
	// if err != nil {
	// 	return nil, nil, errors.Wrap(err, "client.CheckRoomRate")
	// }
	// roomIds := lo.Map(hubHotel.ListRooms, func(room *domain.HubRoom, _ int) string {
	// 	return room.ProviderRoomID
	// })
	// hotel, _, resRate, err := converts.GetItemsFromHotels(res.Hotels, hubHotel.ProviderHotelID, "", roomIds)
	// if err != nil {
	// 	return nil, nil, errors.Wrap(err, "converts.GetItemsFromHotels")
	// }
	// if resRate == nil {
	// 	return nil, nil, domain.ErrRoomSoldOut
	// }
	// outputRate := converts.ToDomainHubRateData(resRate)
	// hotelCode, err := strconv.Atoi(hotel.HotelCode)
	// if err != nil {
	// 	return nil, nil, errors.Wrap(err, "convert hotel code to int")
	// }
	// return outputRate, &domain.TourmindSessionInfo{
	// 	RateCode:  resRate.RateCode,
	// 	HotelCode: hotelCode,
	// }, nil
}

func (a *tourmindAdapter) SearchHotel(ctx context.Context, req *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	return nil, errors.New("Not support")
}

func (a *tourmindAdapter) Book(ctx context.Context, req *domain.HubBookReq, session *domain.TourmindSessionInfo, order *domain.HubHotelOrder, tracingID string) (string, string, enum.BookingStatus, error) {
	return "", "", enum.BookingStatusNone, nil
}

func (a *tourmindAdapter) getBookingStatus(status string) enum.BookingStatus {
	switch status {
	case constants.OrderStatusConfirmed:
		return enum.BookingStatusSuccess
	case constants.OrderStatusCancelled:
		return enum.BookingStatusCancel
	case constants.OrderStatusFailed:
		return enum.BookingStatusFailed
	case constants.OrderStatusPending:
		return enum.BookingStatusPending
	default:
		return enum.BookingStatusNone
	}
}
