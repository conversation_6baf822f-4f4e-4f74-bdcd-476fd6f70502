package entities

type OrderBookingFinishStatusRequest struct {
	PartnerOrderID string `json:"partner_order_id"`
	TestingHashKey string `json:"testing_hash_key"`
}

type OrderBookingFinishStatusResponse struct {
	Data                OrderBookingFinishStatusData `json:"data"`
	Debug               interface{}                  `json:"debug"`
	Error               string                       `json:"error"`
	Status              string                       `json:"status"`
	TestingHashKeyMatch bool                         `json:"testing_hash_key_match"`
}

type OrderBookingFinishStatusData struct {
	Data3DS        interface{} `json:"data_3ds"`
	PartnerOrderID string      `json:"partner_order_id"`
	Percent        int         `json:"percent"`
}

// Cancellation.
type OrderCancellationRequest struct {
	PartnerOrderID string `json:"partner_order_id"`
}

type OrderCancellationResponse struct {
	Data   OrderCancellationData `json:"data"`
	Debug  interface{}           `json:"debug"`
	Error  string                `json:"error"`
	Status string                `json:"status"`
}

type OrderCancellationData struct {
	AmountPayable  AmountDetail `json:"amount_payable"`
	AmountRefunded AmountDetail `json:"amount_refunded"`
	AmountSell     AmountDetail `json:"amount_sell"`
}

type AmountDetail struct {
	Amount       string     `json:"amount"`
	AmountInfo   AmountInfo `json:"amount_info,omitempty"`
	CurrencyCode string     `json:"currency_code"`
}
