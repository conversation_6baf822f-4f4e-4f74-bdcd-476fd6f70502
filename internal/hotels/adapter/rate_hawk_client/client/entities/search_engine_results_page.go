package entities

type SearchEngineResultsPageRequest struct {
	CheckIn   string   `json:"checkin"`
	CheckOut  string   `json:"checkout"`
	Residency string   `json:"residency"`
	Language  string   `json:"language"`
	Guests    []Guest  `json:"guests"`
	IDs       []string `json:"ids"`
	Currency  string   `json:"currency"`
	Timeout   int      `json:"timeout"`
}

// / Response.
type SearchEngineResultsPageResponse struct {
	Data   SearchEngineData `json:"data"`
	Debug  SearchDebug      `json:"debug"`
	Status string           `json:"status"`
	Error  string           `json:"error"`
}

type SearchEngineData struct {
	Hotels      []Hotel `json:"hotels"`
	TotalHotels int     `json:"total_hotels"`
}

type SearchDebugRequest struct {
	Checkin   string  `json:"checkin"`
	Checkout  string  `json:"checkout"`
	Residency string  `json:"residency"`
	Language  string  `json:"language"`
	Guests    []Guest `json:"guests"`
	HIDs      []int   `json:"hids"`
	Currency  string  `json:"currency"`
}

type SearchDebug struct {
	Request         SearchDebugRequest `json:"request"`
	KeyID           int                `json:"key_id"`
	ValidationError string             `json:"validation_error"`
}
