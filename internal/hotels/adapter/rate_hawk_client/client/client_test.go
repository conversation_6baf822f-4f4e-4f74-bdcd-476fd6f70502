package client

import (
	"context"
	"testing"

	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

func getTestCfg() *config.Schema {
	return &config.Schema{
		RateHawKBaseURL:  "https://api.worldota.net",
		RateHawkUsername: "10619",
		RateHawkPassword: "c36eee91-876b-4d78-ac2f-d48ee4f79c3b",
	}
}

func TestHotelsSearchEngineResultsPage(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()

	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.SearchEngineResultsPageRequest{
		CheckIn:   "2025-03-01",
		CheckOut:  "2025-03-02",
		Residency: "uz",
		Language:  "en",
		Guests:    []entities.Guest{{Adults: 2}},
		IDs:       []string{"test_hotel"},
		Currency:  "USD",
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, err := client.HotelsSearchEngineResultsPage(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Info("res", log.Any("res", res))
}

func TestHotelpageRequest(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()
	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.HotelpageRequest{
		Checkin:   "2025-03-01",
		Checkout:  "2025-03-02",
		Residency: "uz",
		Language:  "en",
		Guests:    []entities.Guest{{Adults: 2}},
		ID:        "test_hotel",
		Currency:  "USD",
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, err := client.Hotelpage(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Info("res", log.Any("res", res))
}

func TestPrebook(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()
	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.PrebookRequest{
		Hash:                 "h-adc1e53d-1672-50f5-baee-c1314d8d30e7",
		PriceIncreasePercent: 10.0,
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, err := client.Prebook(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Info("res", log.Any("res", res))
}

func TestOrderBookingForm(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()
	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.OrderBookingFormRequest{
		PartnerOrderID: "123457",
		BookHash:       "p-32ca86b4-6df3-4d3a-8fc1-4595be055bfa",
		Language:       "vi",
		UserIP:         "0.0.0.0",
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, _, err := client.OrderBookingForm(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Info("res", log.Any("res", res))
}

func TestOrderBookingFormFinish(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()
	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.OrderBookingFormFinishRequest{
		User: entities.UserBookingRequest{
			Email:   "<EMAIL>",
			Comment: "comment",
			Phone:   "12312321",
		},
		Partner: entities.BookingPartnerRequest{
			PartnerOrderID:  "11111111",
			Comment:         "",
			AmountSellB2B2C: "",
		},
		Language: "vi",
		Rooms: []entities.RoomRequest{
			{
				Guests: []entities.BookingGuestInfo{
					{
						FirstName: "Quatro",
						LastName:  "Marty",
					},
				},
			},
		},
		UpsellData: []entities.UpsellDataRequest{},
		PaymentType: entities.BookingPaymentTypeRequest{
			Type:         "deposit",
			Amount:       "8",
			CurrencyCode: "USD",
		},
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, err := client.OrderBookingFormFinish(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Any("res", res)
}

func TestOrderInformation(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()
	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.OrderInformationRequest{
		Ordering: entities.Ordering{
			OrderingType: "desc",
			OrderingBy:   "created_at",
		},
		Pagination: entities.PaginationRequest{
			PageSize:   "10",
			PageNumber: "1",
		},
		Search: entities.SearchFilterRequest{
			CreatedAt: &entities.FilterCreatedAt{
				FromDate: "2018-12-05T00:00",
			},
		},
		Language: "vi",
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, err := client.OrderInformation(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Info("res", log.Any("res", res))
}

func TestOrderBookingFinishStatus(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()
	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.OrderBookingFinishStatusRequest{
		PartnerOrderID: "63445245",
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, _, err := client.OrderBookingFinishStatus(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Info("res", log.Any("res", res))
}

func TestOrderCancellation(t *testing.T) {
	// Create a test client
	cfg := getTestCfg()
	client := NewRateHawkClient(cfg, nil)

	// Create a test request
	req := &entities.OrderCancellationRequest{
		PartnerOrderID: "63445245",
	}

	// Create a test tracing ID
	tracingID := "test-tracing-id"

	// Call the method
	res, err := client.OrderCancellation(context.Background(), req, tracingID)

	// Check the error
	if err != nil {
		t.Errorf("expected no error, got %v", err)
	}

	// Check the response
	if res == nil {
		t.Errorf("expected a non-nil response")
		return
	}

	// Check the status
	if res.Status != successStatus {
		t.Errorf("expected status %q, got %q", successStatus, res.Status)
	}

	log.Info("res", log.Any("res", res))
}
