package rate_hawk_client

import (
	"context"
	"strconv"
	"time"

	"github.com/pkg/errors"
	cErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	ct "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

const (
	successStatus          = "ok"
	errorStatus            = "error"
	processingStatus       = "processing"
	orderingType           = "desc"
	orderingBy             = "created_at"
	bookingStatusCancelled = "cancelled"
)

type RateHawkAdapter interface {
	SearchHotel(ctx context.Context, req *domain.SearchAdapterReq, searchKey string) ([]*domain.HotelSummary, error)
	CheckAvailability(ctx context.Context, req *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, error)
	PriceCheck(ctx context.Context, reqData *domain.CacheCheckAvailabilityRequest, selectedBed *domain.BedOption, rate *domain.HubRateData, tracingID string, hotelAddress *domain.Address) (*domain.HubRateData, *domain.BasicSessionInfo, error)
	Book(ctx context.Context, req *domain.HubBookReq, session *domain.BasicSessionInfo, order *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error)
	Retrieve(ctx context.Context, hubOrder *domain.HubHotelOrder) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error)
	Cancel(ctx context.Context, order *domain.HubHotelOrder, endUserIP, tracingID string) error
	RetrieveCancelStatus(ctx context.Context, hotelOrder *domain.HubHotelOrder) (bool, bool, *domain.RefundData)
}

type rateHawkAdapter struct {
	cfg    *config.Schema
	client client.RateHawkClient
}

func NewRateHawkAdapter(cfg *config.Schema, reqRepo repositories.RequestRepository) RateHawkAdapter {
	return &rateHawkAdapter{
		cfg:    cfg,
		client: client.NewRateHawkClient(cfg, reqRepo),
	}
}

func (a *rateHawkAdapter) Retrieve(ctx context.Context, hotelOrder *domain.HubHotelOrder) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	orderCode := hotelOrder.OrderCode
	rooms := hotelOrder.Hotel.ListRooms

	if hotelOrder.ProviderBookingStatus != successStatus {
		clientOrderStatusReq := &entities.OrderBookingFinishStatusRequest{
			PartnerOrderID: hotelOrder.OrderCode,
		}

		statusRes, _, err := a.client.OrderBookingFinishStatus(ctx, clientOrderStatusReq, hotelOrder.OrderCode)
		if err != nil {
			return nil, enum.BookingStatusFailed, errors.Wrap(err, "client.OrderBookingFinishStatus")
		}

		hotelOrder.ProviderBookingStatus = statusRes.Status

		if statusRes.Status != successStatus {
			return nil, enum.BookingStatusPending, nil
		}
	}

	orderInfoReq := &entities.OrderInformationRequest{
		Ordering: entities.Ordering{
			OrderingType: orderingType,
			OrderingBy:   orderingBy,
		},
		Pagination: entities.PaginationRequest{
			PageSize:   "1",
			PageNumber: "1",
		},
		Search: entities.SearchFilterRequest{
			PartnerOrderIds: []string{orderCode},
		},
		Language: constants.DefaultLanguage,
	}

	statusRes, err := a.client.OrderInformation(ctx, orderInfoReq, orderCode)
	if err != nil {
		return nil, enum.BookingStatusPending, errors.Wrap(err, "client.OrderInformation")
	}

	if statusRes == nil || statusRes.Status != successStatus || len(statusRes.Data.Orders) == 0 {
		return nil, enum.BookingStatusPending, nil
	}

	var myOrder *entities.Order

	for _, orderItem := range statusRes.Data.Orders {
		if orderItem.PartnerData.OrderID == orderCode {
			myOrder = &orderItem
			break
		}
	}

	if myOrder == nil {
		return nil, enum.BookingStatusFailed, nil
	}

	hotelConfirmID := myOrder.HotelData.OrderID
	cfID := strconv.Itoa(myOrder.OrderID)

	var confirmData []*domain.HubRetrieveConfirmationID

	if cfID != "" {
		confirmData = a.mappingConfirmationIDs(rooms, cfID, hotelConfirmID)
	}

	return confirmData, enum.BookingStatusSuccess, nil
}

func (a *rateHawkAdapter) Book(ctx context.Context, req *domain.HubBookReq, session *domain.BasicSessionInfo, order *domain.HubHotelOrder, tracingID string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusFailed, "", false, domain.ErrInvalidValue
	}

	if req.Holder.HolderDetail == nil {
		return "", nil, enum.BookingStatusFailed, "", false, errors.New("holder detail nil")
	}

	if order.CustomerIP == "" {
		order.CustomerIP = "0.0.0.0"
	}

	clientBkFormReq := &entities.OrderBookingFormRequest{
		PartnerOrderID: order.OrderCode,
		BookHash:       session.BookingKey,
		Language:       constants.DefaultLanguage,
		UserIP:         order.CustomerIP,
		TestingHashKey: req.TestingHashKey,
	}

	if req.TestingHashKey != "" {
		actionMap, actionExists := ct.TestCaseMap[commonEnum.HotelProviderRateHawk]
		if !actionExists {
			return "", nil, enum.BookingStatusFailed, "", false, errors.New("provider action map not found")
		}

		if _, actionExists := actionMap[ct.ActionOrderBookingForm][req.TestingHashKey]; actionExists {
			res, unknownResBookingForm, err := a.client.OrderBookingForm(ctx, clientBkFormReq, tracingID)
			if err != nil {
				return "", nil, enum.BookingStatusFailed, "", unknownResBookingForm, errors.Wrap(err, "client.OrderBookingForm")
			}

			if res.Status == ct.PendingStatus {
				return "", []*domain.HubRetrieveConfirmationID{}, enum.BookingStatusPending, res.Status, unknownResBookingForm, nil
			}

			order.ReservationCode = strconv.Itoa(res.Data.OrderID)

			return order.ReservationCode, nil, enum.BookingStatusSuccess, res.Status, unknownResBookingForm, nil
		} else if _, actionExists := actionMap[ct.ActionOrderBookingFinishStatus][req.TestingHashKey]; actionExists {
			clientOrderStatusReq := &entities.OrderBookingFinishStatusRequest{
				PartnerOrderID: order.OrderCode,
				TestingHashKey: req.TestingHashKey,
			}

			statusRes, unknownResBookingForm, err := a.client.OrderBookingFinishStatus(ctx, clientOrderStatusReq, tracingID)
			if err != nil {
				return "", nil, enum.BookingStatusFailed, "", unknownResBookingForm, errors.Wrap(err, "client.OrderBookingFinishStatus")
			}

			if statusRes.Status == ct.PendingStatus {
				return "", []*domain.HubRetrieveConfirmationID{}, enum.BookingStatusPending, statusRes.Status, unknownResBookingForm, nil
			}

			return "", nil, enum.BookingStatusSuccess, statusRes.Status, unknownResBookingForm, nil
		}

		return "", nil, enum.BookingStatusFailed, "", false, errors.New("unknown TestingHashKey action")
	}

	res, unknownPendingBookingForm, err := a.client.OrderBookingForm(ctx, clientBkFormReq, tracingID)
	if err != nil {
		return "", nil, enum.BookingStatusFailed, "", unknownPendingBookingForm, errors.Wrap(err, "client.OrderBookingForm")
	}

	if res.Status == ct.PendingStatus {
		return "", []*domain.HubRetrieveConfirmationID{}, enum.BookingStatusPending, res.Status, unknownPendingBookingForm, nil
	}

	order.ReservationCode = strconv.Itoa(res.Data.OrderID)

	finalPaymentType, ok := converts.GetPaymentType(res.Data.PaymentTypes)
	if !ok {
		return "", nil, enum.BookingStatusFailed, "", false, errors.New("payment type not found")
	}

	clientBookingRequest, err := converts.ToEntityOrderBookingFormFinishRequest(req, order, finalPaymentType, a.cfg)
	if err != nil {
		return "", nil, enum.BookingStatusFailed, "", false, errors.Wrap(err, "converts.ToEntityOrderBookingFormFinishRequest")
	}

	_, err = a.client.OrderBookingFormFinish(ctx, clientBookingRequest, tracingID)
	if err != nil {
		log.Error("RH OrderBookingFormFinish err", log.Any("err", err))
	}

	clientOrderStatusReq := &entities.OrderBookingFinishStatusRequest{
		PartnerOrderID: order.OrderCode,
		TestingHashKey: req.TestingHashKey,
	}

	statusRes, unknownPendingFinishStatus, err := a.client.OrderBookingFinishStatus(ctx, clientOrderStatusReq, tracingID)
	if err != nil {
		return "", nil, enum.BookingStatusFailed, "", unknownPendingFinishStatus, errors.Wrap(err, "client.OrderBookingFinishStatus")
	}

	if res.Status == ct.PendingStatus {
		return "", []*domain.HubRetrieveConfirmationID{}, enum.BookingStatusPending, res.Status, unknownPendingFinishStatus, nil
	}

	providerStatus := statusRes.Status

	shouldPending := true

	if providerStatus == processingStatus {
		retryTimes := 3

		for i := 0; i < retryTimes; i++ {
			time.Sleep(time.Second * 3)

			statusResRetry, unknownPendingRetry, err := a.client.OrderBookingFinishStatus(ctx, clientOrderStatusReq, tracingID)
			if err != nil {
				log.Error("[Exception]client.OrderBookingFinishStatus error", log.Any("error", err))
				continue
			}

			providerStatus = statusResRetry.Status
			unknownPendingFinishStatus = unknownPendingRetry
			if providerStatus == successStatus {
				shouldPending = false
				break
			}
		}
	} else if providerStatus == successStatus {
		shouldPending = false
	}

	orderInfoReq := &entities.OrderInformationRequest{
		Ordering: entities.Ordering{
			OrderingType: orderingType,
			OrderingBy:   orderingBy,
		},
		Pagination: entities.PaginationRequest{
			PageSize:   "1",
			PageNumber: "1",
		},
		Search: entities.SearchFilterRequest{
			PartnerOrderIds: []string{order.OrderCode},
		},
		Language: constants.DefaultLanguage,
	}

	var confirmData []*domain.HubRetrieveConfirmationID

	if !shouldPending {
		var listOrder *entities.OrderInformationResponse

		const orderRetrivalRetryTimes = 3
		for i := 0; i < orderRetrivalRetryTimes; i++ {
			time.Sleep(time.Second)

			listOrder, err = a.client.OrderInformation(ctx, orderInfoReq, tracingID)
			if err != nil {
				log.Error("[Exception]client.OrderInformation with retry error", log.Any("error", err), log.Int("retry", i+1), log.String("tracingID", tracingID))
				continue
			}

			if listOrder != nil && len(listOrder.Data.Orders) > 0 {
				break
			}
		}

		var myOrder *entities.Order

		for _, orderItem := range listOrder.Data.Orders {
			if orderItem.PartnerData.OrderID == order.OrderCode {
				myOrder = &orderItem
				break
			}
		}

		if myOrder == nil {
			shouldPending = true
		} else {
			hotelConfirmationID := myOrder.HotelData.OrderID
			cfID := strconv.Itoa(myOrder.OrderID)

			confirmData = a.mappingConfirmationIDs(order.Hotel.ListRooms, cfID, hotelConfirmationID)
		}
	}

	bkStatus := enum.BookingStatusSuccess

	if shouldPending {
		bkStatus = enum.BookingStatusPending
	}

	order.FareDataIssuing = &domain.FareDataIssuing{
		Amount:       0,
		AmountString: finalPaymentType.Amount,
		Currency:     finalPaymentType.CurrencyCode,
		Type:         finalPaymentType.Type,
	}

	return order.ReservationCode, confirmData, bkStatus, providerStatus, unknownPendingFinishStatus, nil
}

func (a *rateHawkAdapter) mappingConfirmationIDs(rooms []*domain.HubOrderRoomItem, confirmationID, hotelConfirmationID string) []*domain.HubRetrieveConfirmationID {
	out := []*domain.HubRetrieveConfirmationID{}

	for _, room := range rooms {
		room.ConfirmationID = confirmationID
		room.HotelConfirmationID = hotelConfirmationID

		temp := &domain.HubRetrieveConfirmationID{
			ProviderRoomID:      room.ProviderRoomID,
			ConfirmationID:      confirmationID,
			HotelConfirmationID: hotelConfirmationID,
			OccupancyType:       room.OccupancyType,
			BedOptionID:         room.BedOption.OptionID,
			BookStatus:          enum.BookingStatusSuccess,
		}

		out = append(out, temp)
	}

	return out
}

func (a *rateHawkAdapter) PriceCheck(ctx context.Context, reqData *domain.CacheCheckAvailabilityRequest, selectedBed *domain.BedOption, rate *domain.HubRateData, tracingID string, hotelAddress *domain.Address) (*domain.HubRateData, *domain.BasicSessionInfo, error) {
	const priceChangePercent = 10 // BA & a Khoi define

	if reqData == nil || rate == nil || selectedBed == nil {
		return nil, nil, cErrs.ErrInvalidInput
	}
	hash := rate.ProviderRateID

	clientReq := &entities.PrebookRequest{
		Hash:                 hash,
		PriceIncreasePercent: priceChangePercent,
	}

	res, err := a.client.Prebook(ctx, clientReq, tracingID)
	if err != nil {
		return nil, nil, errors.Wrap(err, "client.Prebook")
	}

	if res == nil || res.Data.Hotels == nil || res.Data.Hotels[0].Rates == nil {
		return nil, nil, domain.ErrRoomSoldOut
	}

	roomKey := selectedBed.PriceCheckToken

	responseRates := res.Data.Hotels[0].Rates
	var userSelectedRate *entities.Rate

	hotelID := res.Data.Hotels[0].ID

	for _, rate := range responseRates {
		rgExtDomain := converts.ToDomainRoomExt(&rate.RgExt)
		resRoomKey := converts.GetRoomKey(rgExtDomain, hotelID)

		if resRoomKey == roomKey {
			userSelectedRate = &rate
			break
		}
	}

	if userSelectedRate == nil {
		return nil, nil, domain.ErrRoomSoldOut
	}

	sessionData := &domain.BasicSessionInfo{
		BookingKey: userSelectedRate.BookHash,
	}

	newHubRate := converts.ToDomainHubRateData(*userSelectedRate, reqData.Occupancies, reqData.Stay.RoomCount, reqData.Stay.DayCount, roomKey, reqData.Stay.CheckIn, hotelAddress)

	return newHubRate, sessionData, nil
}

func (a *rateHawkAdapter) CheckAvailability(ctx context.Context, req *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, error) {
	if req == nil || req.HubRequest == nil {
		return nil, "", cErrs.ErrInvalidInput
	}

	hubReq := req.HubRequest

	clientRequest := &entities.HotelpageRequest{
		Checkin:   hubReq.Stay.CheckIn,
		Checkout:  hubReq.Stay.CheckOut,
		Residency: hubReq.CountryCode,
		Language:  constants.DefaultLanguage,
		Guests:    converts.ToEntitiesGuests(hubReq.Occupancies),
		ID:        req.ProviderHotelID,
		Currency:  constants.DefaultCurrency,
	}

	res, err := a.client.Hotelpage(ctx, clientRequest, req.TracingID)
	if err != nil {
		return nil, "", errors.Wrap(err, "client.Hotelpage")
	}

	if res == nil || len(res.Data.Hotels) == 0 {
		return nil, "", domain.ErrRoomSoldOut
	}

	if err := hubReq.Stay.CountDays(); err != nil {
		return nil, "", err
	}

	out, err := converts.ToDomainHubRooms(&res.Data.Hotels[0], hubReq.Occupancies, hubReq.CountRooms(), hubReq.Stay.DayCount, req.ContentRooms, hubReq.Stay.CheckIn, req.Address)
	if err != nil {
		return nil, "", errors.Wrap(err, "converts.ToDomainHubRooms")
	}

	return out, constants.DefaultCurrency, nil
}

func (a *rateHawkAdapter) SearchHotel(ctx context.Context, req *domain.SearchAdapterReq, searchKey string) ([]*domain.HotelSummary, error) {
	if req.ProviderHotelIds == nil || req.HubRequest == nil {
		return nil, cErrs.ErrInvalidInput
	}

	hubReq := req.HubRequest

	if req.Timeout == 0 {
		req.Timeout = 10
	}

	clientReq := &entities.SearchEngineResultsPageRequest{
		CheckIn:   hubReq.Stay.CheckIn,
		CheckOut:  hubReq.Stay.CheckOut,
		Residency: req.HubRequest.CountryCode,
		Language:  constants.DefaultLanguage,
		IDs:       req.ProviderHotelIds,
		Currency:  constants.DefaultCurrency,
		Timeout:   req.Timeout,
	}

	clientReq.Guests = converts.ToEntitiesGuests(hubReq.Occupancies)

	res, err := a.client.HotelsSearchEngineResultsPage(ctx, clientReq, searchKey)
	if err != nil {
		return nil, errors.Wrap(err, "client.HotelsSearchEngineResultsPage")
	}

	if res == nil || res.Data.Hotels == nil {
		return nil, cErrs.ErrNotFound
	}

	out, err := converts.ToDomainHotelSummaries(res.Data.Hotels, req.HubRequest)
	if err != nil {
		return nil, errors.Wrap(err, "converts.ToDomainHotelSummaries")
	}

	return out, nil
}

func (a *rateHawkAdapter) Cancel(ctx context.Context, order *domain.HubHotelOrder, endUserIP, tracingID string) error {
	maxRetries := 3
	delay := 2 * time.Second

	var err error
	for i := 0; i < maxRetries; i++ {
		res, err := a.client.OrderCancellation(ctx, &entities.OrderCancellationRequest{
			PartnerOrderID: order.OrderCode,
		}, tracingID)

		if err != nil || res == nil {
			log.Error("rateHawkAdapter cancel error", log.Any("err", err))
			time.Sleep(delay)

			continue
		}

		if res.Status == "error" && (res.Error == "order_not_found" || res.Error == "order_not_cancellable") {
			log.Error("OrderCancellation res error", log.Any("res", res))
			return domain.ErrBookingCancelFailed
		}

		if res.Status == successStatus {
			err = nil
			break
		}
	}

	if err != nil {
		return domain.ErrBookingCancelFailed
	}

	return nil
}

func (a *rateHawkAdapter) RetrieveCancelStatus(ctx context.Context, hotelOrder *domain.HubHotelOrder) (bool, bool, *domain.RefundData) {
	orderCode := hotelOrder.OrderCode

	orderInfoReq := &entities.OrderInformationRequest{
		Ordering: entities.Ordering{
			OrderingType: orderingType,
			OrderingBy:   orderingBy,
		},
		Pagination: entities.PaginationRequest{
			PageSize:   "1",
			PageNumber: "1",
		},
		Search: entities.SearchFilterRequest{
			PartnerOrderIds: []string{orderCode},
		},
		Language: constants.DefaultLanguage,
	}

	statusRes, err := a.client.OrderInformation(ctx, orderInfoReq, orderCode)
	if err != nil {
		log.Error("RetrieveCancelStatus OrderInformation err", log.Any("err", err))
		return false, false, nil
	}

	if statusRes == nil || statusRes.Status != successStatus || len(statusRes.Data.Orders) == 0 {
		return false, false, nil
	}

	var myOrder *entities.Order

	for _, orderItem := range statusRes.Data.Orders {
		if orderItem.PartnerData.OrderID == orderCode {
			myOrder = &orderItem
			break
		}
	}

	if myOrder == nil {
		return false, false, nil
	}

	if myOrder.Status != bookingStatusCancelled {
		return false, false, nil
	}

	totalRefundAmount, err := strconv.ParseFloat(myOrder.AmountRefunded.Amount, 64)
	if err != nil {
		log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("string", myOrder.AmountRefunded.Amount))
		return false, false, nil
	}

	totalPayAmount, err := strconv.ParseFloat(myOrder.AmountSell.Amount, 64)
	if err != nil {
		log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("string", myOrder.AmountSell.Amount))
		return false, false, nil
	}

	currency := myOrder.AmountRefunded.CurrencyCode

	return true, totalPayAmount == totalRefundAmount, &domain.RefundData{
		ProviderRefundAmount: totalRefundAmount,
		PenaltyAmount:        totalPayAmount - totalRefundAmount,
		Currency:             currency,
	}
}
