package converts

import (
	"strconv"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client/client/entities"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func ToDomainHotelSummaries(inputs []entities.Hotel, hubReq *domain.HubSearchHotelRequest) ([]*domain.HotelSummary, error) {
	out := []*domain.HotelSummary{}

	divideRatio := hubReq.Stay.DayCount * hubReq.Stay.RoomCount
	if divideRatio == 0 {
		return nil, errors.New("divideRatio == 0")
	}

	for _, input := range inputs {
		temp, err := ToDomainHotelSummary(input, float64(divideRatio))
		if err != nil {
			return nil, errors.Wrap(err, "ToDomainHotelSummary item"+input.ID)
		}

		out = append(out, temp)
	}

	return out, nil
}

func F64(input string) float64 {
	f, _ := strconv.ParseFloat(input, 64)
	return f
}

func GetPaymentType(in []entities.PaymentType) (*entities.PaymentType, bool) {
	paymentType, ok := lo.Find(in, func(item entities.PaymentType) bool {
		return item.Type == constants.PaymentTypeDeposit
	})

	return &paymentType, ok
}

func getLowestRate(rates []entities.Rate) (*entities.Rate, error) {
	min := float64(0)
	var minRate *entities.Rate

	for _, rate := range rates {
		if rate.Allotment < 1 {
			continue
		}

		paymentType, ok := GetPaymentType(rate.PaymentOptions.PaymentTypes)

		if !ok {
			continue
		}

		payAmount := F64(paymentType.CommissionInfo.Charge.AmountNet)

		if payAmount == 0 {
			continue
		}

		if min == 0 || payAmount < min {
			min = payAmount
			minRate = &rate
		}
	}

	return minRate, nil
}

// func getAirlineIncludedTaxes(input []entities.Tax, currencyCode string) (float64, error) {
// 	var total float64

// 	for _, tax := range input {
// 		if !tax.IncludedBySupplier {
// 			if tax.CurrencyCode != currencyCode {
// 				return 0, fmt.Errorf("GetAirlineIncludedTaxes: %s != %s", tax.CurrencyCode, currencyCode)
// 			}

// 			total += f64(tax.Amount)
// 		}
// 	}

// 	return total, nil
// }

func ToDomainHotelSummary(input entities.Hotel, divideRatio float64) (*domain.HotelSummary, error) {
	rate, err := getLowestRate(input.Rates)
	if err != nil {
		return nil, errors.Wrap(err, "err")
	}

	if rate == nil {
		return &domain.HotelSummary{
			ProviderHotelID: input.ID,
			Available:       false,
			RoomLeft:        0,
		}, nil
	}

	paymentType, _ := GetPaymentType(rate.PaymentOptions.PaymentTypes)

	_, sumTaxes := calculateHubRateTaxes(paymentType.TaxData.Taxes, 1, true)

	chargeInfo := paymentType.CommissionInfo.Charge

	discountPrice := (F64(chargeInfo.AmountNet) - sumTaxes) / divideRatio
	originalPrice := (F64(chargeInfo.AmountNet) + F64(chargeInfo.AmountCommission) - sumTaxes) / divideRatio

	payAtHotel, totalPayAtHotel := convertToPayAtHotels(paymentType.TaxData.Taxes)

	canRefund := canRefundable(
		paymentType.CancellationPenalties.Policies,
		paymentType.CommissionInfo.Charge.AmountNet,
		paymentType.CancellationPenalties.FreeCancellationBefore,
	)

	return &domain.HotelSummary{
		Provider:        commonEnum.HotelProviderRateHawk,
		MatchKey:        commonEnum.HotelProviderToHash(commonEnum.HotelProviderRateHawk),
		ProviderHotelID: input.ID,
		Price: &domain.Price{
			PricePerNight: &domain.PricePerNight{
				DiscountPrice: discountPrice,
				OriginalPrice: originalPrice,
			},
			Total:           F64(chargeInfo.AmountNet),
			IsIncludeTax:    true,
			Currency:        paymentType.CurrencyCode,
			PayAtHotel:      payAtHotel,
			TotalPayAtHotel: totalPayAtHotel,
			HasBreakfast:    rate.MealData.HasBreakfast,
			HasExtraBed:     hasExtraBed(rate.AmenitiesData),
			NonSmoking:      isNonSmoking(rate.AmenitiesData),
			Refundable:      canRefund,
		},
		Available: rate.Allotment > 0,
		RoomLeft:  rate.Allotment,
	}, nil
}
