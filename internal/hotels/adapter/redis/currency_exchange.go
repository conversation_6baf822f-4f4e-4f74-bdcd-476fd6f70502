package redis

import (
	"context"
	"encoding/json"
	"time"

	"github.com/pkg/errors"
	goRedis "github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

const (
	currencyExchangeCacheKey  = "currency_exchange_list_"
	currencyExchangeCacheTime = time.Hour
)

type CurrencyExchangeRepository interface {
	Set(ctx context.Context, from string, val []*domain.CurrencyExchange) error
	Get(ctx context.Context, from string) ([]*domain.CurrencyExchange, error)
}

type currencyExchangeRepository struct {
	redisClient redis.IRedis
}

func NewCurrencyExchangeRepository(
	redisClient redis.IRedis,
) CurrencyExchangeRepository {
	return &currencyExchangeRepository{
		redisClient,
	}
}

func (r *currencyExchangeRepository) Get(ctx context.Context, from string) ([]*domain.CurrencyExchange, error) {
	out := []*domain.CurrencyExchange{}

	cmd := r.redisClient.CMD().Get(ctx, currencyExchangeCacheKey+from)

	err := cmd.Err()
	if err != nil {
		if errors.Is(err, goRedis.Nil) {
			return nil, nil
		}

		return nil, err
	}

	err = json.Unmarshal([]byte(cmd.Val()), &out)
	if err != nil {
		return nil, errors.Wrap(err, "json.Unmarshal")
	}

	return out, nil
}

func (r *currencyExchangeRepository) Set(_ context.Context, from string, val []*domain.CurrencyExchange) error {
	data, err := json.Marshal(val)
	if err != nil {
		return errors.Wrap(err, "json.Marshal")
	}

	err = r.redisClient.SetWithExpiration(currencyExchangeCacheKey+from, data, currencyExchangeCacheTime)
	if err != nil {
		return errors.Wrap(err, "SetWithExpiration")
	}

	return nil
}
