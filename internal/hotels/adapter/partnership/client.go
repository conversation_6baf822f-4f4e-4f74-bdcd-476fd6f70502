package partnership

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	partnershipBackendPb "gitlab.deepgate.io/apps/api/gen/go/partnership/backend"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/server"
	tracingGRPC "gitlab.deepgate.io/apps/common/tracing/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"google.golang.org/grpc/metadata"
)

type partnershipClient struct {
	cfg *config.Schema
}

type PartnershipClient interface {
	RetrievePartnership(ctx context.Context, pID string) (*domain.Partnership, error)
	GetPartnershipUserByID(ctx context.Context, userID, pID string) (*domain.PartnershipUser, error)
}

func NewPartnershipClient(cfg *config.Schema) PartnershipClient {
	return &partnershipClient{cfg: cfg}
}

func (c *partnershipClient) RetrievePartnership(ctx context.Context, pID string) (*domain.Partnership, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PartnershipServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "Cannot connect partner service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, pID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	req := &partnershipBackendPb.RetrievePartnershipReq{Id: pID}

	client := partnershipBackendPb.NewPartnershipServiceClient(conn)

	res, err := client.RetrievePartnership(newCtx, req)
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if res.Info == nil {
		return nil, fmt.Errorf("res info nil")
	}

	info := res.Info

	partnership := domain.Partnership{
		Id:       pID,
		FullName: info.FullName,
	}

	return &partnership, nil
}

func (c *partnershipClient) GetPartnershipUserByID(ctx context.Context, userID, pID string) (*domain.PartnershipUser, error) {
	conn, err := tracingGRPC.DialWithTrace(ctx, c.cfg.PartnershipServiceEndpoint)
	if err != nil {
		return nil, errors.Wrap(err, "Cannot connect partner service")
	}
	defer conn.Close()

	md := server.WithMetadataInternalPartnership(c.cfg.InternalSecretToken, pID)
	newCtx := metadata.NewOutgoingContext(ctx, md)

	req := &partnershipBackendPb.GetUserByIDReq{Id: userID}

	client := partnershipBackendPb.NewPartnershipServiceClient(conn)

	res, err := client.GetUserByID(newCtx, req)
	if err != nil {
		return nil, commonError.New(commonError.BadRequest, err.Error())
	}

	if res.Data == nil {
		return nil, fmt.Errorf("res data nil")
	}

	info := res.Data

	partnershipUser := domain.PartnershipUser{
		Id:              pID,
		CreatedAt:       info.CreatedAt,
		UpdatedAt:       info.UpdatedAt,
		Name:            info.Name,
		Email:           info.Email,
		PhoneNumber:     info.PhoneNumber,
		PhoneCode:       info.PhoneCode,
		PartnershipId:   info.PartnershipId,
		Username:        info.Username,
		OfficeManagerId: info.OfficeManagerId,
	}

	return &partnershipUser, nil
}
