package test

import (
	"encoding/json"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/retrieve_booking"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/search_hotel"
	svcConvert "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func Testsearch_hotel() error {
	byteValue, _ := utils.ReadXMLFile("./internal/hotels/adapter/expedia_client/docs/search_res.json")

	res := []search_hotel.SearchResponse{}

	err := json.Unmarshal(byteValue, &res)
	if err != nil {
		fmt.Println("Error parse", err)
		return err
	}

	utils.WriteStructToJSONFile(res, "logs/expedia_search_res.json")

	// searchResult := converts.ToDomainSearchResults(res)

	// utils.WriteStructToJSONFile(searchResult, "logs/expedia_search_res.json")

	return nil
}

func TestMapConfirmation() error {
	var retrieveResponse *retrieve_booking.Response
	byteValue, _ := utils.ReadXMLFile("./internal/hotels/adapter/expedia_client/docs/retrieve_res.json")

	err := json.Unmarshal(byteValue, &retrieveResponse)
	if err != nil {
		return err
	}

	confirmIDs, _, err := converts.GetConfirmationID(retrieveResponse)
	if err != nil {
		log.Error("GetConfirmationID error", log.Any("err", err))
		return err
	}

	utils.WriteStructToJSONFile(confirmIDs, "logs/confirm_ids.json")

	rooms := []*domain.HubOrderRoomItem{
		{
			RoomID:         "108477977-325308515",              // từ trường "room_id"
			ProviderRoomID: "325308515",                        // từ trường "provider_room_id"
			Name:           "Biệt thự Deluxe, quang cảnh vườn", // từ trường "name"
			RateData:       []*domain.HubRateData{},            // không có trong dữ liệu nguồn
			Provider:       0,                                  // từ trường "provider" ($numberLong: "0")
			ConfirmationID: "",                                 // không có trong dữ liệu nguồn
			OccupancyIndex: 1,                                  // từ trường "occupancy_index" ($numberLong: "1")
			OccupancyType:  "1adt",                             // từ trường "occupancy_type"
			GivenName:      "MINH PHUONG",                      // không có trong dữ liệu nguồn
			Surname:        "HUYNH",                            // không có trong dữ liệu nguồn
			Email:          "",                                 // không có trong dữ liệu nguồn
			SpecialRequest: "",                                 // không có trong dữ liệu nguồn
			BedOption: &domain.RoomBedOption{
				OptionID: "37396",                             // từ bed_option.option_id
				Name:     "2 giường cỡ queen và 1 giường đôi", // từ bed_option.name
				Quantity: 3,                                   // từ bed_option.quantity ($numberLong: "3")

			},
			BookingStatus: "", // từ trường "booking_status"
		},
	}

	svcConvert.MapRoomConfirmationID(commonEnum.HotelProviderExpedia, rooms, confirmIDs)

	utils.WriteStructToJSONFile(rooms, "logs/rooms.json")

	return nil
}
