package region

import (
	"net/url"
	"strconv"
)

type RegionsRequest struct {
	Include                []string `json:"include"`
	Language               string   `json:"language"`
	AncestorID             string   `json:"ancestor_id"`
	Area                   string   `json:"area"`
	CountryCode            []string `json:"country_code"`
	CountrySubdivisionCode []string `json:"country_subdivision_code"`
	IATALocationCode       string   `json:"iata_location_code"`
	Limit                  int      `json:"limit"`
	SupplySource           string   `json:"supply_source"`
	Type                   []string `json:"type"`
	BillingTerms           string   `json:"billing_terms"`
	PartnerPointOfSale     string   `json:"partner_point_of_sale"`
	PaymentTerms           string   `json:"payment_terms"`
	PlatformName           string   `json:"platform_name"`
}

func (r *RegionsRequest) ToQueryParams() string {
	queryParams := url.Values{}

	if len(r.Include) > 0 {
		queryParams["include"] = r.Include
	}

	if r.Language != "" {
		queryParams.Set("language", r.Language)
	}

	if r.AncestorID != "" {
		queryParams.Set("ancestor_id", r.AncestorID)
	}

	if r.Area != "" {
		queryParams.Set("area", r.Area)
	}

	if len(r.CountryCode) > 0 {
		queryParams["country_code"] = r.CountryCode
	}

	if len(r.CountrySubdivisionCode) > 0 {
		queryParams["country_subdivision_code"] = r.CountrySubdivisionCode
	}

	if r.IATALocationCode != "" {
		queryParams.Set("iata_location_code", r.IATALocationCode)
	}

	if r.Limit > 0 {
		queryParams.Set("limit", strconv.Itoa(r.Limit))
	}

	if r.SupplySource != "" {
		queryParams.Set("supply_source", r.SupplySource)
	}

	if len(r.Type) > 0 {
		queryParams["type"] = r.Type
	}

	if r.BillingTerms != "" {
		queryParams.Set("billing_terms", r.BillingTerms)
	}

	if r.PartnerPointOfSale != "" {
		queryParams.Set("partner_point_of_sale", r.PartnerPointOfSale)
	}

	if r.PaymentTerms != "" {
		queryParams.Set("payment_terms", r.PaymentTerms)
	}

	if r.PlatformName != "" {
		queryParams.Set("platform_name", r.PlatformName)
	}

	return queryParams.Encode()
}
