package utils

import (
	"time"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/search_hotel"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

const ExpediaSearchDateFormat = "2006-01-02"

// parse HubSearch date string to Expedia Search String.
func ConvertToExpediaTimeStr(timeStr string) (string, error) {
	t, err := time.Parse(constants.HubSearchDateFormat, timeStr)
	if err != nil {
		log.Error("ConvertToExpediaTimeStr parse err", log.Any("err", err), log.String("timeStr", timeStr))
		return "", err
	}

	return t.Format(ExpediaSearchDateFormat), nil
}

func ConvertToExpediaFullDateStr(timeStr string) (string, error) {
	t, err := time.Parse("2006-01-02T15:04:05.000-07:00", timeStr)
	if err != nil {
		log.Error("ConvertToExpediaFullDateStr parse err", log.Any("err", err), log.String("timeStr", timeStr))
		return "", err
	}

	return t.Format(constants.HubDateFormat), nil
}

func HasBreakfastAmenity(amenities map[string]search_hotel.Amenity) bool {
	ids := map[string]struct{}{
		"2098":       {},
		"2103":       {},
		"2104":       {},
		"2105":       {},
		"2193":       {},
		"2194":       {},
		"2205":       {},
		"2209":       {},
		"2210":       {},
		"2211":       {},
		"1073742621": {},
		"1073742786": {},
		"1073742857": {},
		"1073744734": {},
		"1073744735": {},
	}

	for key := range amenities {
		if _, exists := ids[key]; exists {
			return true
		}
	}

	return false
}
