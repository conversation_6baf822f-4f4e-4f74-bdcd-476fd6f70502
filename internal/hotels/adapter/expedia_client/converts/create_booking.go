package converts

import (
	"strings"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/create_booking"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/retrieve_booking"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	hubEnum "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func ToBookRequest(in *domain.HubBookReq, rooms []*domain.HubOrderRoomItem, companyEmail string) (*create_booking.Request, error) {
	if in == nil || in.Holder == nil || len(in.Holder.HolderDetail) == 0 || len(rooms) == 0 {
		log.Error("ToBookRequest input empty")
		return nil, errors.ErrInvalidInput
	}

	// defaultMail := in.Holder.HolderDetail[0].Email

	result := &create_booking.Request{
		AffiliateReferenceID: in.OrderCode,
		Hold:                 false,
		Email:                companyEmail,
		Phone: &create_booking.RequestPhone{
			CountryCode: in.Holder.PhoneCode,
			Number:      in.Holder.PhoneNumber,
		},
		Rooms: []*create_booking.RequestRoom{},
		Payments: []*create_booking.RequestPayment{{
			Type: "affiliate_collect",
			BillingContact: &create_booking.RequestBillingContact{
				GivenName:  "Bizitrip",
				FamilyName: "Company",
				Address: &create_booking.RequestAddress{
					CountryCode:       "VN",
					Line1:             "140B Nguyen Van Troi Street, Ward 8, Phu Nhuan, Ho Chi Minh City, Vietnam",
					StateProvinceCode: "SG",
					PostalCode:        "700000",
				},
			},
		}},
	}

	for _, room := range rooms {
		result.Rooms = append(result.Rooms, &create_booking.RequestRoom{
			GivenName:      processName(room.GivenName),
			FamilyName:     processName(room.Surname),
			SpecialRequest: helpers.EncodeSpecialChars(room.SpecialRequest),
		})
	}

	return result, nil
}

func processName(name string) string {
	name = strings.ToUpper(name)
	return helpers.RemoveAccents(name)
}

func ToHubBookingStatus(in enum.ExpediaBookingStatus) hubEnum.BookingStatus {
	switch in {
	case enum.ExpediaBookingStatusBooked:
		return hubEnum.BookingStatusSuccess
	case enum.ExpediaBookingStatusPending:
		return hubEnum.BookingStatusPending
	case enum.ExpediaBookingStatusCanceled:
		return hubEnum.BookingStatusCancel
	default:
		return hubEnum.BookingStatusNone
	}
}

func GetConfirmationID(in *retrieve_booking.Response) ([]*domain.HubRetrieveConfirmationID, enum.ExpediaBookingStatus, error) {
	result := []*domain.HubRetrieveConfirmationID{}

	if in == nil {
		log.Error("GetConfirmationID input nil")
		return result, enum.ExpediaBookingStatusNone, domain.ErrInvalidValue
	}

	if len(in.Rooms) == 0 {
		log.Error("GetConfirmationID Rooms len 0")
		return result, enum.ExpediaBookingStatusPending, nil
	}

	for _, room := range in.Rooms {
		if room.Status == enum.ExpediaBookingStatusPending {
			return nil, enum.ExpediaBookingStatusPending, nil
		}

		confirmID := room.ConfirmationID.Expedia
		hotelConfirmID := room.ConfirmationID.Property

		if confirmID == "" {
			continue
		}

		occupancy := domain.HubSearchOccupancy{
			Adults: room.NumberOfAdults,
			Children: &domain.HubSearchChildren{
				Number: uint(len(room.Chd)),
				Age:    room.Chd,
			},
			Rooms: 1,
		}

		result = append(result, &domain.HubRetrieveConfirmationID{
			ProviderRoomID:      room.ID,
			ConfirmationID:      confirmID,
			HotelConfirmationID: hotelConfirmID,
			OccupancyType:       occupancy.GenOccupancyType(),
			GivenName:           room.GivenName,
			Surname:             room.SurName,
			BedOptionID:         room.BedGroupID,
			BookStatus:          ToHubBookingStatus(room.Status),
		})
	}

	if len(result) != len(in.Rooms) {
		return result, enum.ExpediaBookingStatusPending, nil
	}

	return result, enum.ExpediaBookingStatusBooked, nil
}
