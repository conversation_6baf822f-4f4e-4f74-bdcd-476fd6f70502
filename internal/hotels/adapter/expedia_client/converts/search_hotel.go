package converts

import (
	"fmt"
	"math"
	"strconv"
	"strings"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/search_hotel"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	hubEnum "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

// GenerateOccupancy tạo danh sách occupancy từ dữ liệu đầu vào.
func GenerateOccupancy(in []*domain.HubSearchOccupancy) []string {
	result := []string{}

	for _, occupancy := range in {
		if occupancy != nil {
			childrenStr := ""

			if occupancy.Children != nil {
				for _, age := range occupancy.Children.Age {
					childrenStr += fmt.Sprintf("%d,", age)
				}
			}

			occupancyStr := fmt.Sprintf("%d", occupancy.Adults)

			if childrenStr != "" {
				occupancyStr += fmt.Sprintf("-%s", strings.TrimSuffix(childrenStr, ","))
			}

			for i := uint(0); i < occupancy.Rooms; i++ {
				result = append(result, occupancyStr)
			}
		}
	}

	return result
}

// ToSearchHotelReq chuyển đổi dữ liệu yêu cầu tìm kiếm khách sạn.
func ToSearchHotelReq(reqData *domain.SearchHotelRequestData) (*search_hotel.SearchRequest, error) {
	req := reqData.SearchReq

	if req == nil || len(req.Occupancies) == 0 {
		log.Error("ToSearchHotelReq err input", log.Any("req", req))
		return nil, errors.ErrInvalidInput
	}

	checkInStr, err := utils.ConvertToExpediaTimeStr(req.Stay.CheckIn)
	if err != nil {
		log.Error("ToSearchHotelReq ConvertToExpediaTimeStr err", log.Any("err", err), log.String("check-in", req.Stay.CheckIn))
		return nil, errors.ErrInvalidInput
	}

	checkOutStr, err := utils.ConvertToExpediaTimeStr(req.Stay.CheckOut)
	if err != nil {
		log.Error("ToSearchHotelReq ConvertToExpediaTimeStr err", log.Any("err", err), log.String("check-out", req.Stay.CheckOut))
		return nil, errors.ErrInvalidInput
	}

	ratePlanCount := req.RatePlanCount
	if ratePlanCount == 0 {
		ratePlanCount = constants.DefaultRatePlanCount
	}

	result := &search_hotel.SearchRequest{
		CheckIn:          checkInStr,
		CheckOut:         checkOutStr,
		Currency:         string(enum.CurrencyUSD),
		CountryCode:      req.CountryCode,
		Occupancy:        GenerateOccupancy(req.Occupancies),
		PropertyID:       reqData.ProviderHotelIds,
		SalesChannel:     enum.SalesChannelAgentTool,
		Language:         req.Language,
		SalesEnvironment: enum.SalesEnvironmentHotelPackage,
		RatePlanCount:    ratePlanCount,
		TravelPurpose:    enum.TravelPurposeLeisure,
		RateOption:       []string{"member"},
		BillingTerms:     "EAC",
		PointOfSale:      "B2B_CORP_SA_PKG_MOD",
		PaymentTerms:     "1",
	}

	return result, nil
}

// GenOccupancyTypeMap tạo map loại occupancy từ dữ liệu đầu vào.
func GenOccupancyTypeMap(in []*domain.HubSearchOccupancy) (map[string]string, map[string]uint) {
	result := map[string]string{}
	occupancyCount := map[string]uint{}

	for _, occupancy := range in {
		if occupancy != nil {
			childrenStr := ""

			if occupancy.Children != nil {
				for _, age := range occupancy.Children.Age {
					childrenStr += fmt.Sprintf("%d,", age)
				}
			}

			occupancyStr := fmt.Sprintf("%d", occupancy.Adults)
			occupancyType := fmt.Sprintf("%dadt", occupancy.Adults)

			if childrenStr != "" {
				occupancyStr += fmt.Sprintf("-%s", strings.TrimSuffix(childrenStr, ","))
				occupancyType += fmt.Sprintf(",%dchd-%s", occupancy.Children.Number, strings.TrimSuffix(childrenStr, ","))
			}

			result[occupancyStr] = occupancyType

			if occupancyCount[occupancyStr] == 0 {
				occupancyCount[occupancyStr] = occupancy.Rooms
			} else {
				occupancyCount[occupancyStr] += occupancy.Rooms
			}
		}
	}

	return result, occupancyCount
}

// ConvertCancelPenalty chuyển đổi thông tin hủy phòng.
func ConvertCancelPenalty(in search_hotel.CancelPenalty, occupancyInfo *OccupancyRateCalcAdditionalInfo, stayCount int64) (*domain.HubCancelPolicy, error) {
	if occupancyInfo == nil {
		log.Error("occupancyInfo nil")
		return nil, errors.ErrInvalidInput
	}

	startDate, err := utils.ConvertToExpediaFullDateStr(in.Start)
	if err != nil {
		log.Error("ConvertToExpediaFullDateStr err", log.Any("err", err), log.String("start", in.Start))
		return nil, err
	}

	endDate, err := utils.ConvertToExpediaFullDateStr(in.End)
	if err != nil {
		log.Error("ConvertToExpediaFullDateStr err", log.Any("err", err), log.String("End", in.End))
		return nil, err
	}

	partialRefund := !((in.Amount == "" || in.Amount == "0") &&
		(in.Percent == "" || in.Percent == "0") &&
		(in.Nights == "" || in.Nights == "0"))

	penaltyAmount := float64(0)
	refundable := true

	var amount float64
	if in.Amount != "" {
		amount, err = helpers.ParseFloat(in.Amount, false)
		if err != nil {
			log.Error("ParseFloat err", log.Any("err", err), log.String("Amount", in.Amount))
			return nil, err
		}

		if amount != occupancyInfo.TotalInclusiveAmount {
			penaltyAmount += math.Abs(amount)
		} else {
			refundable = false
		}
	}

	var percent float64
	if in.Percent != "" {
		percent, err = helpers.ParseFloat(strings.ReplaceAll(in.Percent, "%", ""), false)
		if err != nil {
			log.Error("ParseFloat err", log.Any("err", err), log.String("Percent", in.Percent))
			return nil, err
		}

		if percent > 0 && percent < MaxPercent {
			penaltyAmount += occupancyInfo.TotalInclusiveAmount * percent / MaxPercent
		} else if percent == MaxPercent {
			refundable = false
		}
	}

	if in.Nights != "" {
		nightValue, err := strconv.ParseInt(strings.ReplaceAll(in.Nights, "%", ""), 0, 64)
		if err != nil {
			log.Error("ParseFloat err", log.Any("err", err), log.String("Nights", in.Nights))
			return nil, err
		}

		if nightValue > 0 && nightValue < stayCount {
			if len(occupancyInfo.NightlyTotalAmount) < int(nightValue) {
				log.Error("occupancyInfo.NightlyTotalAmount not match", log.Any("occupancyInfo.NightlyTotalAmount", occupancyInfo.NightlyTotalAmount), log.Any("nightValue", nightValue))
				return nil, errors.ErrInvalidInput
			}

			for i := 0; i < int(nightValue); i++ {
				penaltyAmount += occupancyInfo.NightlyTotalAmount[i]
			}
		} else if nightValue >= stayCount {
			refundable = false
		}
	}

	return &domain.HubCancelPolicy{
		StartDate:              startDate,
		EndDate:                endDate,
		Currency:               in.Currency,
		NonrefundableDateRange: []*domain.HubNonrefundableDateRange{},
		PartialRefund:          partialRefund,
		PenaltyInfo: domain.HubCancelPolicyPenaltyInfo{
			Amount:         amount,
			Percent:        percent,
			NumberOfNights: in.Nights,
		},
		Refundable:    refundable,
		PenaltyAmount: penaltyAmount,
	}, nil
}

// MappingTaxType ánh xạ loại thuế.
func MappingTaxType(in enum.FeeType) hubEnum.TaxType {
	switch in {
	case enum.FeeTypeTaxAndServiceFee:
		return hubEnum.TaxTypeTaxAndFee
	case enum.FeeTypeSalesTax:
		return hubEnum.TaxTypeTax
	case enum.FeeTypeBaseRate:
		return hubEnum.TaxTypeFee
	case enum.FeeTypeExtraPersonFee:
		return hubEnum.TaxTypeFee
	case enum.FeeTypePropertyFee:
		return hubEnum.TaxTypeFee
	case enum.FeeTypeAdjustment:
		return hubEnum.TaxTypeFee
	case enum.FeeTypeRecoveryChargesAndFees:
		return hubEnum.TaxTypeFee
	case enum.FeeTypeTravelerServiceFee:
		return hubEnum.TaxTypeFee
	default:
		return hubEnum.TaxTypeFee
	}
}

// FromSearchResponseToHubHotel chuyển đổi dữ liệu tìm kiếm khách sạn.
func FromSearchResponseToHubHotel(response *search_hotel.SearchResponse, occupancyMap map[string]string, occupancyCount map[string]uint, stayCount int64) (*domain.HubHotel, error) {
	if response == nil {
		log.Error("FromSearchResponseToHubHotel response nil err")
		return nil, domain.ErrInvalidValue
	}

	hotel := &domain.HubHotel{
		ProviderHotelID: response.PropertyID,
		ListRooms:       make([]*domain.HubRoom, 0, len(response.Rooms)),
	}

	for _, room := range response.Rooms {
		hubRoom := &domain.HubRoom{
			ProviderRoomID: room.ID,
			Name:           room.RoomName,
			RateData:       make([]*domain.HubRateData, 0, len(room.Rates)),
		}

		for _, rate := range room.Rates {
			promotions := []*domain.HubPromotion{}
			if rate.Promotions.Deal.ID != "" {
				promotions = append(promotions, &domain.HubPromotion{
					Code:   rate.Promotions.Deal.ID,
					Name:   rate.Promotions.Deal.Description,
					Remark: "deal",
				})
			}

			for _, valueAdd := range rate.Promotions.ValueAdds {
				personCount := ""
				if valueAdd.PersonCount != 0 {
					personCount = fmt.Sprintf("%d", valueAdd.PersonCount)
				}

				promotions = append(promotions, &domain.HubPromotion{
					Remark:      "value_adds",
					Code:        valueAdd.ID,
					Name:        valueAdd.Description,
					OfferType:   valueAdd.OfferType,
					Frequency:   valueAdd.Frequency,
					PersonCount: personCount,
				})
			}

			cancelPenalties := []*domain.HubCancelPolicy{}

			occupancyRates, additionalInfo, err := CalculateOccupancyRatesSearchHotel(rate.OccupancyPricing, occupancyMap, occupancyCount)
			if err != nil {
				return nil, err
			}

			currency := additionalInfo.RateCurrency
			hotel.Currency = additionalInfo.HotelCurrency

			nonRefundable := []*domain.HubNonrefundableDateRange{}

			for _, item := range rate.NonRefundableRanges {
				nonRefundable = append(nonRefundable, &domain.HubNonrefundableDateRange{
					StartDate: item.Start,
					EndDate:   item.End,
				})
			}

			for _, item := range rate.CancelPenalties {
				cancelPenalty, err := ConvertCancelPenalty(item, additionalInfo, stayCount)
				if err != nil {
					return nil, domain.ErrInvalidValue
				}

				cancelPenalties = append(cancelPenalties, cancelPenalty)
			}

			amenities := []*domain.HubAmenity{}
			for _, item := range rate.Amenities {
				amenities = append(amenities, &domain.HubAmenity{
					ID:   item.ID,
					Name: item.Name,
				})
			}

			bedOptions := []*domain.BedOption{}

			for _, item := range rate.BedGroups {
				value, ok := item.Links["price_check"]
				if ok {
					quantity := uint(0)
					for _, config := range item.Configuration {
						quantity += uint(config.Quantity)
					}

					bedOptions = append(bedOptions, &domain.BedOption{
						OptionID:        item.ID,
						Name:            item.Description,
						Quantity:        quantity,
						PriceCheckToken: value.Href,
						BedConfigs:      ToDomainBedConfigs(item.Configuration),
					})
				}
			}

			saleScenario := []string{}

			for key, value := range rate.SaleScenario {
				if value {
					saleScenario = append(saleScenario, key)
				}
			}

			hubRate := &domain.HubRateData{
				RateID:                 rate.ID,
				Available:              fmt.Sprintf("%d", rate.AvailableRooms),
				CancelPolicies:         cancelPenalties,
				Promotions:             promotions,
				OccupancyRate:          occupancyRates,
				Currency:               currency,
				NonrefundableDateRange: nonRefundable,
				BedOptions:             bedOptions,
				Amenities:              amenities,
				Refundable:             rate.Refundable,
				SaleScenario:           saleScenario,
				HasBreakfast:           utils.HasBreakfastAmenity(rate.Amenities),
			}
			hubRoom.RateData = append(hubRoom.RateData, hubRate)
		}

		hotel.ListRooms = append(hotel.ListRooms, hubRoom)
	}

	return hotel, nil
}

// FromSearchResponseToHubHotels chuyển đổi danh sách dữ liệu tìm kiếm khách sạn.
func FromSearchResponseToHubHotels(response []*search_hotel.SearchResponse, occupancyMap map[string]string, occupancyCount map[string]uint, stayCount int64) ([]*domain.HubHotel, error) {
	result := make([]*domain.HubHotel, 0, len(response))

	for _, item := range response {
		hubHotel, err := FromSearchResponseToHubHotel(item, occupancyMap, occupancyCount, stayCount)
		if err != nil {
			return nil, err
		}

		result = append(result, hubHotel)
	}

	return result, nil
}

// ToDomainBedConfigs chuyển đổi cấu hình giường.
func ToDomainBedConfigs(in []search_hotel.BedConfiguration) []*domain.BedConfiguration {
	out := []*domain.BedConfiguration{}
	for _, item := range in {
		out = append(out, &domain.BedConfiguration{
			Type:     item.Type,
			Size:     item.Size,
			Quantity: float64(item.Quantity),
		})
	}

	return out
}
