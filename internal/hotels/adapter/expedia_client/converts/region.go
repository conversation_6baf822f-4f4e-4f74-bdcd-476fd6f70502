package converts

import (
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/region"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

// ToDomainRegionRequest converts RegionsRequest to domain.RegionsRequest.
func ToDomainRegionRequest(req *region.RegionsRequest) *domain.RegionsRequest {
	if req == nil {
		return nil
	}

	return &domain.RegionsRequest{
		Include:                req.Include,
		Language:               req.Language,
		AncestorID:             req.AncestorID,
		Area:                   req.Area,
		CountryCode:            req.CountryCode,
		CountrySubdivisionCode: req.CountrySubdivisionCode,
		IATALocationCode:       req.IATALocationCode,
		Limit:                  req.Limit,
		SupplySource:           req.SupplySource,
		Type:                   req.Type,
		BillingTerms:           req.BillingTerms,
		PartnerPointOfSale:     req.PartnerPointOfSale,
		PaymentTerms:           req.PaymentTerms,
		PlatformName:           req.PlatformName,
	}
}

// FromDomainRegionRequest converts domain.RegionsRequest to RegionsRequest.
func FromDomainRegionRequest(req *domain.RegionsRequest) *region.RegionsRequest {
	if req == nil {
		return nil
	}

	return &region.RegionsRequest{
		Include:                req.Include,
		Language:               req.Language,
		AncestorID:             req.AncestorID,
		Area:                   req.Area,
		CountryCode:            req.CountryCode,
		CountrySubdivisionCode: req.CountrySubdivisionCode,
		IATALocationCode:       req.IATALocationCode,
		Limit:                  req.Limit,
		SupplySource:           req.SupplySource,
		Type:                   req.Type,
		BillingTerms:           req.BillingTerms,
		PartnerPointOfSale:     req.PartnerPointOfSale,
		PaymentTerms:           req.PaymentTerms,
		PlatformName:           req.PlatformName,
	}
}

// ToDomainRegions converts an array of RegionsResponse to an array of Region.
func ToDomainRegions(responses []*region.RegionsResponse) []*domain.Region {
	if responses == nil {
		return nil
	}

	domainRegions := make([]*domain.Region, len(responses))
	for i, resp := range responses {
		domainRegions[i] = ToDomainRegion(resp)
	}

	return domainRegions
}

// FromDomainRegions converts an array of Region to an array of RegionsResponse.
func FromDomainRegions(regions []*domain.Region) []*region.RegionsResponse {
	if regions == nil {
		return nil
	}

	responseRegions := make([]*region.RegionsResponse, len(regions))
	for i, region := range regions {
		responseRegions[i] = FromDomainRegion(region)
	}

	return responseRegions
}

// ToDomainRegion converts RegionsResponse to Region.
func ToDomainRegion(resp *region.RegionsResponse) *domain.Region {
	return &domain.Region{
		RegionID:               resp.ID,
		Type:                   resp.Type,
		Name:                   resp.Name,
		FullName:               resp.FullName,
		Descriptor:             resp.Descriptor,
		IATAAirportCode:        resp.IATAAirportCode,
		IATAAirportMetroCode:   resp.IATAAirportMetroCode,
		CountryCode:            resp.CountryCode,
		CountrySubdivisionCode: resp.CountrySubdivisionCode,
		Coordinates:            ToDomainCoordinates(resp.Coordinates),
		Associations:           resp.Associations,
		Ancestors:              ToDomainAncestors(resp.Ancestors),
		Descendants:            resp.Descendants,
		PropertyIDs:            resp.PropertyIDs,
		PropertyIDsExpanded:    resp.PropertyIDsExpanded,
		Categories:             resp.Categories,
		Tags:                   resp.Tags,
	}
}

// FromDomainRegion converts Region to RegionsResponse.
func FromDomainRegion(info *domain.Region) *region.RegionsResponse {
	if info == nil {
		return nil
	}

	return &region.RegionsResponse{
		ID:                     info.RegionID,
		Type:                   info.Type,
		Name:                   info.Name,
		FullName:               info.FullName,
		Descriptor:             info.Descriptor,
		IATAAirportCode:        info.IATAAirportCode,
		IATAAirportMetroCode:   info.IATAAirportMetroCode,
		CountryCode:            info.CountryCode,
		CountrySubdivisionCode: info.CountrySubdivisionCode,
		Coordinates:            FromDomainCoordinates(info.Coordinates),
		Associations:           info.Associations,
		Ancestors:              FromDomainAncestors(info.Ancestors),
		Descendants:            info.Descendants,
		PropertyIDs:            info.PropertyIDs,
		PropertyIDsExpanded:    info.PropertyIDsExpanded,
		Categories:             info.Categories,
		Tags:                   info.Tags,
	}
}

// ToDomainCoordinates converts Coordinates to RegionCoordinates.
func ToDomainCoordinates(coords *region.Coordinates) *domain.RegionCoordinates {
	if coords == nil {
		return nil
	}

	return &domain.RegionCoordinates{
		CenterLongitude: coords.CenterLongitude,
		CenterLatitude:  coords.CenterLatitude,
		BoundingPolygon: ToDomainBoundingPolygon(coords.BoundingPolygon),
	}
}

// FromDomainCoordinates converts RegionCoordinates to Coordinates.
func FromDomainCoordinates(coords *domain.RegionCoordinates) *region.Coordinates {
	if coords == nil {
		return nil
	}

	return &region.Coordinates{
		CenterLongitude: coords.CenterLongitude,
		CenterLatitude:  coords.CenterLatitude,
		BoundingPolygon: FromDomainBoundingPolygon(coords.BoundingPolygon),
	}
}

// ToDomainBoundingPolygon converts BoundingPolygon to BoundingPolygon.
func ToDomainBoundingPolygon(polygon *region.BoundingPolygon) *domain.BoundingPolygon {
	if polygon == nil {
		return nil
	}

	return &domain.BoundingPolygon{
		Type:        polygon.Type,
		Coordinates: polygon.Coordinates,
	}
}

// FromDomainBoundingPolygon converts BoundingPolygon to BoundingPolygon.
func FromDomainBoundingPolygon(polygon *domain.BoundingPolygon) *region.BoundingPolygon {
	if polygon == nil {
		return nil
	}

	return &region.BoundingPolygon{
		Type:        polygon.Type,
		Coordinates: polygon.Coordinates,
	}
}

// ToDomainAncestors converts a slice of Ancestor to a slice of Ancestor.
func ToDomainAncestors(ancestors []*region.Ancestor) []*domain.Ancestor {
	if ancestors == nil {
		return nil
	}

	domainAncestors := make([]*domain.Ancestor, len(ancestors))
	for i, ancestor := range ancestors {
		domainAncestors[i] = &domain.Ancestor{
			ID:   ancestor.ID,
			Type: ancestor.Type,
		}
	}

	return domainAncestors
}

// FromDomainAncestors converts a slice of Ancestor to a slice of Ancestor.
func FromDomainAncestors(ancestors []*domain.Ancestor) []*region.Ancestor {
	if ancestors == nil {
		return nil
	}

	regionAncestors := make([]*region.Ancestor, len(ancestors))
	for i, ancestor := range ancestors {
		regionAncestors[i] = &region.Ancestor{
			ID:   ancestor.ID,
			Type: ancestor.Type,
		}
	}

	return regionAncestors
}
