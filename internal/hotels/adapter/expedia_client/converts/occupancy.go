package converts

import (
	"fmt"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/price_check"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/search_hotel"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	hubEnum "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

const (
	// DefaultPrecision là độ chính xác mặc định cho các phép tính.
	DefaultPrecision = 3
	// MaxPercent là giá trị phần trăm tối đa.
	MaxPercent = 100
	// DefaultPrecisionAmount là độ chính xác mặc định cho số tiền.
	DefaultPrecisionAmount = 2
)

// OccupancyRateCalcAdditionalInfo chứa thông tin bổ sung cho việc tính toán tỷ lệ occupancy.
type OccupancyRateCalcAdditionalInfo struct {
	RateCurrency  string
	HotelCurrency string

	TotalInclusiveAmount float64
	NightlyTotalAmount   []float64
}

// CalculateOccupancyRatesPriceCheck tính toán tỷ lệ occupancy từ dữ liệu price check.
func CalculateOccupancyRatesPriceCheck(occuPricings map[string]price_check.OccupancyPricing, occupancyMap map[string]string, occupancyCount map[string]uint) ([]*domain.HubOccupancyRate, *OccupancyRateCalcAdditionalInfo, error) {
	out := []*domain.HubOccupancyRate{}
	additionalInfo := &OccupancyRateCalcAdditionalInfo{}

	rateCurrency := ""
	hotelCurrency := ""

	totalInclusiveAmount := float64(0)
	nightlyTotalAmount := []float64{}

	for groupName, occuPricing := range occuPricings {
		totalTaxAndFee := float64(0)
		totalBaseRate := float64(0)

		strMarketingFee := occuPricing.Totals.MarketingFee.RequestCurrency.Value
		totalMarketingFee, err := helpers.ParseFloat(strMarketingFee, true)
		if err != nil {
			return nil, nil, fmt.Errorf("helpers.ParseFloat err: %w val: %s", err, strMarketingFee)
		}

		marketingFeePerNight := totalMarketingFee / float64(len(occuPricing.Nightly))

		occupancyRate := &domain.HubOccupancyRate{
			OccupancyType: occupancyMap[groupName],
			RoomQuantity:  occupancyCount[groupName],
			MarketingFee:  totalMarketingFee,
		}

		// Fees
		for key, data := range occuPricing.Fees {
			value := data.RequestCurrency.Value
			if value == "" {
				value = "0"
			}

			amount, err := helpers.ParseFloat(value, true)
			if err != nil {
				log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("key", key), log.String("value", value))
				return nil, nil, domain.ErrInvalidValue
			}

			occupancyRate.PayAtHotel = append(occupancyRate.PayAtHotel, &domain.PayAtHotel{
				Amount:      helpers.CeilToPrecision(amount, DefaultPrecisionAmount),
				Description: key,
				Currency:    data.RequestCurrency.Currency,
			})
		}

		feeAndTaxesType := []enum.FeeType{
			enum.FeeTypeTaxAndServiceFee,
			enum.FeeTypePropertyFee,
			enum.FeeTypeRecoveryChargesAndFees,
			enum.FeeTypeSalesTax,
		}

		// Nightly
		for index, nightly := range occuPricing.Nightly {
			nightlyRate := &domain.HubRate{}

			if len(nightlyTotalAmount) < index+1 {
				nightlyTotalAmount = append(nightlyTotalAmount, 0)
			}

			for _, fee := range nightly {
				if fee.Value == "" {
					continue
				}

				feeValue, err := helpers.ParseFloat(fee.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", fee.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				if fee.Type == enum.FeeTypeBaseRate {
					totalBaseRate += feeValue

					nightlyRate.RateBasic = feeValue - marketingFeePerNight
					nightlyRate.Currency = fee.Currency

					if rateCurrency == "" {
						rateCurrency = fee.Currency
					}

					continue
				}

				if lo.Contains(feeAndTaxesType, fee.Type) {
					nightlyRate.TaxAmount += feeValue
					totalTaxAndFee += feeValue
				}

				// FeeValue exclude marketingFeePerNight
				nightlyTotalAmount[index] += feeValue * float64(occupancyRate.RoomQuantity)
			} // end foreach fee in nightly

			nightlyRate.RateAmount = nightlyRate.RateBasic + nightlyRate.TaxAmount
			occupancyRate.TotalNightlyRate = append(occupancyRate.TotalNightlyRate, nightlyRate)
		}

		// Stay
		for _, stay := range occuPricing.Stay {
			var amount *float64

			if lo.Contains(feeAndTaxesType, stay.Type) {
				if stay.Value != "" {
					amountValue, err := helpers.ParseFloat(stay.Value, true)
					if err != nil {
						log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", stay.Value))
						return nil, nil, domain.ErrInvalidValue
					}

					amount = &amountValue
					totalTaxAndFee += *amount
				}

				occupancyRate.RateTaxes = append(occupancyRate.RateTaxes, &domain.HubRateTax{
					Amount:      amount,
					Currency:    stay.Currency,
					Included:    true,
					Type:        MappingTaxType(stay.Type),
					Description: string(stay.Type),
				})
			}
		}

		inclusiveAmount, err := helpers.ParseFloat(occuPricing.Totals.Inclusive.RequestCurrency.Value, true)
		if err != nil {
			log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.Inclusive.RequestCurrency.Value))
			return nil, nil, domain.ErrInvalidValue
		}

		totalInclusiveAmount += inclusiveAmount * float64(occupancyRate.RoomQuantity)

		occupancyRate.Surcharges = helpers.NormalizeZero(inclusiveAmount - totalTaxAndFee - totalBaseRate)

		basicAmount, err := helpers.ParseFloat(occuPricing.Totals.Exclusive.RequestCurrency.Value, true)
		if err != nil {
			log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.PropertyInclusive.RequestCurrency.Value))
			return nil, nil, domain.ErrInvalidValue
		}

		for enumName, name := range hubEnum.RateDiscountNameName {
			switch enumName {
			case hubEnum.RateDiscountNameBase:
				if occuPricing.Totals.Strikethrough.RequestCurrency.Value == "" {
					continue
				}

				strikeThrough, err := helpers.ParseFloat(occuPricing.Totals.Strikethrough.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.Strikethrough.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				occupancyRate.RateDiscounts = append(occupancyRate.RateDiscounts, &domain.HubRateDiscount{
					Amount:   helpers.CeilToPrecision(helpers.NormalizeZero(strikeThrough-basicAmount), DefaultPrecision),
					Name:     name,
					Currency: occuPricing.Totals.Strikethrough.RequestCurrency.Currency,
				})
			case hubEnum.RateDiscountNameTax:
				if occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Value == "" ||
					occuPricing.Totals.Inclusive.RequestCurrency.Value == "" {
					continue
				}

				inclusiveStrikeThrough, err := helpers.ParseFloat(occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				inclusive, err := helpers.ParseFloat(occuPricing.Totals.Inclusive.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.Inclusive.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				occupancyRate.RateDiscounts = append(occupancyRate.RateDiscounts, &domain.HubRateDiscount{
					Amount:   helpers.CeilToPrecision(helpers.NormalizeZero(inclusiveStrikeThrough-inclusive), DefaultPrecision),
					Name:     name,
					Currency: occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Currency,
				})
			case hubEnum.RateDiscountNameAll:
				if occuPricing.Totals.PropertyInclusiveStrikethrough.RequestCurrency.Value == "" ||
					occuPricing.Totals.PropertyInclusive.RequestCurrency.Value == "" {
					continue
				}

				propertyInclusiveStrikeThrough, err := helpers.ParseFloat(occuPricing.Totals.PropertyInclusiveStrikethrough.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.PropertyInclusiveStrikethrough.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				propertyInclusive, err := helpers.ParseFloat(occuPricing.Totals.PropertyInclusive.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.PropertyInclusive.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				occupancyRate.RateDiscounts = append(occupancyRate.RateDiscounts, &domain.HubRateDiscount{
					Amount:   helpers.CeilToPrecision(helpers.NormalizeZero(propertyInclusiveStrikeThrough-propertyInclusive), DefaultPrecision),
					Name:     name,
					Currency: occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Currency,
				})
			}
		}

		out = append(out, occupancyRate)
		hotelCurrency = occuPricing.Totals.PropertyInclusive.RequestCurrency.Currency
	}

	additionalInfo.HotelCurrency = hotelCurrency
	additionalInfo.RateCurrency = rateCurrency
	additionalInfo.NightlyTotalAmount = nightlyTotalAmount
	additionalInfo.TotalInclusiveAmount = totalInclusiveAmount

	return out, additionalInfo, nil
}

// CalculateOccupancyRatesSearchHotel tính toán tỷ lệ occupancy từ dữ liệu search hotel.
func CalculateOccupancyRatesSearchHotel(occuPricings map[string]search_hotel.OccupancyPricing, occupancyMap map[string]string, occupancyCount map[string]uint) ([]*domain.HubOccupancyRate, *OccupancyRateCalcAdditionalInfo, error) {
	out := []*domain.HubOccupancyRate{}
	additionalInfo := &OccupancyRateCalcAdditionalInfo{}

	rateCurrency := ""
	hotelCurrency := ""

	totalInclusiveAmount := float64(0)
	nightlyTotalAmount := []float64{}

	for groupName, occuPricing := range occuPricings {
		strMarketingFee := occuPricing.Totals.MarketingFee.RequestCurrency.Value
		totalMarketingFee, err := helpers.ParseFloat(strMarketingFee, true)
		if err != nil {
			return nil, nil, fmt.Errorf("helpers.ParseFloat err: %w val: %s", err, strMarketingFee)
		}

		marketingFeePerNight := totalMarketingFee / float64(len(occuPricing.Nightly))

		occupancyRate := &domain.HubOccupancyRate{
			OccupancyType: occupancyMap[groupName],
			RoomQuantity:  occupancyCount[groupName],
			MarketingFee:  totalMarketingFee,
		}

		totalBaseRate := float64(0)
		totalTaxAndFee := float64(0)

		for key, data := range occuPricing.Fees {
			value := data.RequestCurrency.Value
			if value == "" {
				value = "0"
			}
			currency := data.RequestCurrency.Currency

			amount, err := helpers.ParseFloat(value, true)
			if err != nil {
				log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("key", key), log.String("value", value))
				return nil, nil, domain.ErrInvalidValue
			}

			occupancyRate.PayAtHotel = append(occupancyRate.PayAtHotel, &domain.PayAtHotel{
				Amount:      helpers.CeilToPrecision(amount, DefaultPrecisionAmount),
				Description: key,
				Currency:    currency,
			})
		}

		feeAndTaxesType := []enum.FeeType{
			enum.FeeTypeTaxAndServiceFee,
			enum.FeeTypePropertyFee,
			enum.FeeTypeRecoveryChargesAndFees,
			enum.FeeTypeSalesTax,
		}

		for index, nightly := range occuPricing.Nightly {
			nightlyRate := &domain.HubRate{}

			if len(nightlyTotalAmount) < index+1 {
				nightlyTotalAmount = append(nightlyTotalAmount, 0)
			}

			for _, fee := range nightly {
				if fee.Value == "" {
					continue
				}

				feeValue, err := helpers.ParseFloat(fee.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", fee.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				if fee.Type == enum.FeeTypeBaseRate {
					totalBaseRate += feeValue

					nightlyRate.RateBasic = feeValue - marketingFeePerNight
					nightlyRate.Currency = fee.Currency

					if rateCurrency == "" {
						rateCurrency = fee.Currency
					}
				}

				if lo.Contains(feeAndTaxesType, fee.Type) {
					nightlyRate.TaxAmount += feeValue
					totalTaxAndFee += feeValue
				}

				// FeeValue exclude marketingFeePerNight
				nightlyTotalAmount[index] += feeValue * float64(occupancyRate.RoomQuantity)
			} // end foreach fee in nightly

			nightlyRate.RateAmount = nightlyRate.RateBasic + nightlyRate.TaxAmount
			occupancyRate.TotalNightlyRate = append(occupancyRate.TotalNightlyRate, nightlyRate)
		}

		for _, stay := range occuPricing.Stay {
			var amount *float64

			if lo.Contains(feeAndTaxesType, stay.Type) {
				if stay.Value != "" {
					amountValue, err := helpers.ParseFloat(stay.Value, true)
					if err != nil {
						log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", stay.Value))
						return nil, nil, domain.ErrInvalidValue
					}

					amount = &amountValue
					totalTaxAndFee += *amount
				}

				occupancyRate.RateTaxes = append(occupancyRate.RateTaxes, &domain.HubRateTax{
					Amount:      amount,
					Currency:    stay.Currency,
					Included:    true,
					Type:        MappingTaxType(stay.Type),
					Description: string(stay.Type),
				})
			}
		}

		inclusiveAmount, err := helpers.ParseFloat(occuPricing.Totals.Inclusive.RequestCurrency.Value, true)
		if err != nil {
			log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.Inclusive.RequestCurrency.Value))
			return nil, nil, domain.ErrInvalidValue
		}

		totalInclusiveAmount += inclusiveAmount * float64(occupancyRate.RoomQuantity)

		occupancyRate.Surcharges = helpers.NormalizeZero(inclusiveAmount - totalTaxAndFee - totalBaseRate)

		basicAmount, err := helpers.ParseFloat(occuPricing.Totals.Exclusive.RequestCurrency.Value, true)
		if err != nil {
			log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.PropertyInclusive.RequestCurrency.Value))
			return nil, nil, domain.ErrInvalidValue
		}

		for enumName, name := range hubEnum.RateDiscountNameName {
			switch enumName {
			case hubEnum.RateDiscountNameBase:
				if occuPricing.Totals.Strikethrough.RequestCurrency.Value == "" {
					continue
				}

				strikeThrough, err := helpers.ParseFloat(occuPricing.Totals.Strikethrough.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.Strikethrough.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				occupancyRate.RateDiscounts = append(occupancyRate.RateDiscounts, &domain.HubRateDiscount{
					Amount:   helpers.CeilToPrecision(helpers.NormalizeZero(strikeThrough-basicAmount), DefaultPrecision),
					Name:     name,
					Currency: occuPricing.Totals.Strikethrough.RequestCurrency.Currency,
				})
			case hubEnum.RateDiscountNameTax:
				if occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Value == "" ||
					occuPricing.Totals.Inclusive.RequestCurrency.Value == "" {
					continue
				}

				inclusiveStrikeThrough, err := helpers.ParseFloat(occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				inclusive, err := helpers.ParseFloat(occuPricing.Totals.Inclusive.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.Inclusive.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				occupancyRate.RateDiscounts = append(occupancyRate.RateDiscounts, &domain.HubRateDiscount{
					Amount:   helpers.CeilToPrecision(helpers.NormalizeZero(inclusiveStrikeThrough-inclusive), DefaultPrecision),
					Name:     name,
					Currency: occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Currency,
				})
			case hubEnum.RateDiscountNameAll:
				if occuPricing.Totals.PropertyInclusiveStrikethrough.RequestCurrency.Value == "" ||
					occuPricing.Totals.PropertyInclusive.RequestCurrency.Value == "" {
					continue
				}

				propertyInclusiveStrikeThrough, err := helpers.ParseFloat(occuPricing.Totals.PropertyInclusiveStrikethrough.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.PropertyInclusiveStrikethrough.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				propertyInclusive, err := helpers.ParseFloat(occuPricing.Totals.PropertyInclusive.RequestCurrency.Value, true)
				if err != nil {
					log.Error("FromSearchResponseToHubHotel helpers.ParseFloat err", log.Any("err", err), log.String("value", occuPricing.Totals.PropertyInclusive.RequestCurrency.Value))
					return nil, nil, domain.ErrInvalidValue
				}

				occupancyRate.RateDiscounts = append(occupancyRate.RateDiscounts, &domain.HubRateDiscount{
					Amount:   helpers.CeilToPrecision(helpers.NormalizeZero(propertyInclusiveStrikeThrough-propertyInclusive), DefaultPrecision),
					Name:     name,
					Currency: occuPricing.Totals.InclusiveStrikethrough.RequestCurrency.Currency,
				})
			}
		}

		out = append(out, occupancyRate)
		hotelCurrency = occuPricing.Totals.PropertyInclusive.RequestCurrency.Currency
	}

	additionalInfo.HotelCurrency = hotelCurrency
	additionalInfo.RateCurrency = rateCurrency
	additionalInfo.NightlyTotalAmount = nightlyTotalAmount
	additionalInfo.TotalInclusiveAmount = totalInclusiveAmount

	return out, additionalInfo, nil
}
