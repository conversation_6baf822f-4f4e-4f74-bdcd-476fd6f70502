package converts

import (
	"math"
	"strconv"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/models/retrieve_booking"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func GetCancelLink(in *retrieve_booking.Response) ([]string, error) {
	cancelURL := []string{}

	if len(in.Rooms) == 0 {
		log.Error("GetCancelLink Rooms len 0")
		return nil, domain.ErrBookingCancelFailed
	}

	for _, room := range in.Rooms {
		if value, ok := room.Links["cancel"]; ok {
			cancelURL = append(cancelURL, value.Href)
		}
	}

	if len(cancelURL) != len(in.Rooms) {
		log.Error("GetCancelLink cancelURL not enough")
		return nil, domain.ErrBookingCancelFailed
	}

	return cancelURL, nil
}

func GetRefundInfo(in *retrieve_booking.Response) (bool, bool, *domain.RefundData) {
	if len(in.Rooms) == 0 {
		log.Error("IsBookingCanceled Rooms len 0")
		return false, false, nil
	}

	totalRefundAmount := float64(0)
	totalPayAmount := float64(0)
	currency := ""

	for _, room := range in.Rooms {
		if room.Status != enum.ExpediaBookingStatusCanceled {
			log.Info("IsBookingCanceled status not canceled", log.Any("room.Status", room.Status))
			return false, false, nil
		}

		if room.Rate == nil ||
			room.Rate.Pricing == nil ||
			room.Rate.Pricing.Totals == nil {
			log.Info("IsBookingCanceled Rate nil", log.Any("Rate", room.Rate))
			return false, false, nil
		}

		billingAmount, err := strconv.ParseFloat(room.Rate.Pricing.Totals.Inclusive.BillableCurrency.Value, 64)
		if err != nil {
			log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("string", room.Rate.Pricing.Totals.Inclusive.BillableCurrency.Value))
			return false, false, nil
		}

		totalPayAmount += billingAmount

		if room.Rate.CancelRefund == nil {
			log.Info("IsBookingCanceled CancelRefund nil", log.Any("Rate", room.Rate))
			return false, false, nil
		}

		refundAmount, err := strconv.ParseFloat(room.Rate.CancelRefund.Amount, 64)
		if err != nil {
			log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("string", room.Rate.CancelRefund.Amount))
			return false, false, nil
		}

		totalRefundAmount += math.Abs(refundAmount)

		currency = room.Rate.CancelRefund.Currency
	}

	return true, totalPayAmount == totalRefundAmount, &domain.RefundData{
		ProviderRefundAmount: totalRefundAmount,
		PenaltyAmount:        totalPayAmount - totalRefundAmount,
		Currency:             currency,
	}
}
