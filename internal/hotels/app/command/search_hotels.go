package command

import (
	"context"
	"sort"

	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type SearchHotelsHandler interface {
	Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error)
}

type searchHotelsHandler struct {
	cfg            *config.Schema
	searchService  service.SearchHotelService
	currencyExSvc  service.CurrencyExchangeService
	promoHotelRepo repositories.HotelPrioritiesRepository
}

func NewSearchHotelsHandler(
	cfg *config.Schema,
	searchService service.SearchHotelService,
	currencyExSvc service.CurrencyExchangeService,
	promoHotelRepo repositories.HotelPrioritiesRepository,
) SearchHotelsHandler {
	return &searchHotelsHandler{cfg, searchService, currencyExSvc, promoHotelRepo}
}

func (h *searchHotelsHandler) Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	if req.Place.Type == enum.PlaceTypeContinent || req.Place.Type == enum.PlaceTypeCountry {
		return &domain.HubSearchHotelResponse{
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			HotelSummary:             []*domain.HotelSummary{},
			Pagination:               &commonDomain.Pagination{},
		}, nil
	}

	if req.RequestCurrency != "" && req.RequestCurrency != constants.VNDCurrency {
		if err := h.currencyExSvc.ValidateCurrency(ctx, req.RequestCurrency); err != nil {
			log.Error("h.currencyExSvc.ValidateCurrency err", log.Any("err", err), log.Any("currency", req.RequestCurrency))
			return nil, err
		}
	}

	if err := req.Stay.CountDays(); err != nil {
		log.Error("SearchHotelPrice stay.DayCount err", log.Any("err", err), log.Any("stay.DayCount", req.Stay.CountDays))
		return nil, err
	}

	if req.SaleChannel == "" {
		req.SaleChannel = enum.SalesChannelAgentTool
	}

	if req.TravelPurpose == "" {
		req.TravelPurpose = enum.TravelPurposeLeisure
	}

	if req.SalesEnv == "" {
		req.SalesEnv = enum.SalesEnvironmentHotelPackage
	}

	req.Stay.RoomCount = req.CountRooms()

	enableProvider := []commonEnum.HotelProvider{
		commonEnum.HotelProviderNone,
	}

	enableProvider = append(enableProvider, req.EnableProviders...)

	if req.ImageMode == enum.ImageModeTypeNone {
		req.ImageMode = enum.ImageModeTypeFull
	}

	// Search
	res, loadMore, err := h.searchService.Search(ctx, req, enableProvider)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return &domain.HubSearchHotelResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: true,
			},
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			Pagination:               req.Pagination,
			HotelSummary:             []*domain.HotelSummary{},
		}, nil
	}

	totalCounts := req.Pagination.TotalRecord

	if req.RequestCurrency != "" && req.RequestCurrency != constants.VNDCurrency {
		var exRateItem *domain.CurrencyExchange

		for _, hotel := range res {
			var err error
			var rate float64

			if exRateItem != nil && exRateItem.Rate != 0 && exRateItem.From == hotel.Price.Currency {
				rate = exRateItem.Rate
			}

			hotel.Price, exRateItem, err = h.currencyExSvc.ConvertHotelSummaryPrice(ctx, hotel.Price, req.RequestCurrency, rate)
			if err != nil {
				log.Error("currencyExSvc.ConvertHotelSummaryPrice err", log.Any("err", err))
			}
		}
	}

	var filter *domain.SearchHotelFilterResponse
	sortedList := res

	if !req.ExcludeContent {
		filter = h.searchService.CalcFilterRequest(ctx, res)

		filterList := h.searchService.ApplyFilterRequest(ctx, res, req.SearchHotelFilterRequest)

		sortedList = h.searchService.ApplySortRequest(ctx, req, filterList)
	}

	isFilterDefault := req.SearchHotelFilterRequest.IsDefault()

	if isFilterDefault && req.IsSortDefault() {
		promoList, err := h.promoHotelRepo.FindAll(ctx)

		if err != nil {
			log.Error("[SearchHotelByHotelIDs] promoHotelRepo.FindAll err", log.Any("err", err))
			return nil, errors.ErrSomethingOccurred
		}

		if len(promoList) > 0 {
			promoHotelIDs := make([]string, 0, len(promoList))

			for _, item := range promoList {
				promoHotelIDs = append(promoHotelIDs, item.HotelID)
			}

			if len(sortedList) > 0 {
				for _, item := range sortedList {
					if contain := commonHelpers.Contains(promoHotelIDs, item.ID); contain {
						item.HotelPriority = true
					}
				}

				keepFirstItem := req.Place.Type == enum.PlaceTypeHotel && req.Pagination.PageCurrent == 1

				sort.Slice(sortedList, func(i, j int) bool {
					if sortedList[i].ID == req.Place.PlaceID && keepFirstItem {
						return true
					}

					if sortedList[j].ID == req.Place.PlaceID && keepFirstItem {
						return false
					}

					return sortedList[i].HotelPriority && !sortedList[j].HotelPriority
				})
			}
		}
	}

	pagedData := h.searchService.ApplyPaging(ctx, sortedList, req.Pagination)

	pagedDataWithHiddenFee := helpers.Copy(pagedData).([]*domain.HotelSummary)

	// re-gen key no need to check error
	searchKey, _ := req.GenSearchKey()

	result := &domain.HubSearchHotelResponse{
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		SearchHotelFilterRequest: filter,
		Pagination:               req.Pagination,
		HotelSummary:             pagedDataWithHiddenFee,
	}

	// if h.cfg.Env != commonConstants.EnvProduction {
	result.SearchKey = searchKey
	// }

	if loadMore && result.Pagination.PageCurrent == result.Pagination.TotalPage {
		if totalCounts == 0 || totalCounts <= req.Pagination.PageLimit {
			totalCounts = 1000
		}

		result.Pagination.TotalPage = totalCounts / req.Pagination.PageLimit
		result.Pagination.TotalRecord = totalCounts
	}

	return result, nil
}
