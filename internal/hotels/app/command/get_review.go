package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type GetReviewHandler interface {
	Handle(ctx context.Context, req *domain.HubHotelReviewReq) (*domain.HubHotelReviewRes, error)
}

type getReviewHandler struct {
	cfg              *config.Schema
	getReviewService service.GetReviewService
}

func NewGetReviewHandler(
	cfg *config.Schema,
	getReviewService service.GetReviewService,
) GetReviewHandler {
	return &getReviewHandler{
		cfg,
		getReviewService,
	}
}

func (h *getReviewHandler) Handle(ctx context.Context, req *domain.HubHotelReviewReq) (*domain.HubHotelReviewRes, error) {
	if value, ok := commonEnum.HotelProviderValue[h.cfg.EnableProvider]; ok {
		req.Provider = value
	} else {
		req.Provider = commonEnum.HotelProviderRateHawk
	}

	response, err := h.getReviewService.GetHotelReview(ctx, req)
	if err != nil {
		log.Error("h.getReviewService.GetHotelReview error", log.Any("error", err), log.Any("req", req))
		return nil, err
	}

	return &domain.HubHotelReviewRes{
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		Reviews: response,
	}, nil
}
