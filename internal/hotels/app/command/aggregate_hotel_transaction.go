package command

import (
	"context"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type AggregateHotelTransactionHandler interface {
	Handle(ctx context.Context, partnershipID, newOfficeID string) error
}

type aggregateHotelTransactionHandler struct {
	cfg           *config.Schema
	orderRepo     repositories.OrderRepository
	walletClient  wallet.WalletClient
	partnerClient partner.PartnerClient
}

func NewAggregateHotelTransactionHandler(
	cfg *config.Schema,
	orderRepo repositories.OrderRepository,
	walletClient wallet.WalletClient,
	partnerClient partner.PartnerClient,
) AggregateHotelTransactionHandler {
	return &aggregateHotelTransactionHandler{
		cfg,
		orderRepo,
		walletClient,
		partnerClient,
	}
}

func (h aggregateHotelTransactionHandler) Handle(ctx context.Context, partnershipID, newOfficeID string) error {
	// orders, err := h.orderRepo.ListOrderAggregate(ctx, nil, &domain.ListOrderFilter{
	// 	OfficeID: &newOfficeID,
	// })
	// if err != nil {
	// 	log.Error("orderRepo.ListOrderAggregate error", log.Any("error", err))
	// 	return err
	// }

	// for _, order := range orders {
	// 	if order.LastTransactionID == "" {
	// 		continue
	// 	}
	// 	office, err := h.partnerClient.GetOfficeInfo(ctx, newOfficeID, h.cfg.HubPartnershipID)
	// 	if err != nil {
	// 		log.Error("partnerClient.GetOfficeInfo error", log.Any("error", err), log.String("officeID", order.OfficeID))
	// 		continue
	// 	}

	// 	newTx, err := h.walletClient.AggregateHubTransaction(ctx, order.LastTransactionID, order, office)
	// 	if err != nil {
	// 		log.Error("walletClient.AggregateHubTransaction error", log.Any("error", err), log.String("officeID", order.OfficeID))
	// 		continue
	// 	}

	// 	err = h.orderRepo.UpdateOneV2(ctx, order.ID, &domain.HubOrderUpdate{
	// 		AgentCode:            office.Code,
	// 		LastTransactionID:    &newTx,
	// 		LastTransactionIDOld: &order.LastTransactionID,
	// 		NewOfficeID:          &newOfficeID,
	// 		OfficeIDOld:          &order.OfficeID,
	// 	})
	// 	if err != nil {
	// 		log.Error("orderRepo.UpdateOneV2 error", log.Any("error", err), log.String("officeID", order.OfficeID))
	// 		continue
	// 	}
	// }
	return nil
}
