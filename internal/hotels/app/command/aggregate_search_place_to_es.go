package command

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/converts"
	esRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/es/es_repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type AggregatePlaceHandler interface {
	Handle(ctx context.Context, countryCode string, syncES bool) error
}

type aggregatePlaceHandler struct {
	regionRepo               repositories.RegionRepository
	hotelRepo                repositories.HotelRepository
	inActiveHotelsRepository repositories.InActiveHotelsRepository
	esPlaceRepo              esRepo.PlaceElasticRepository
}

func NewAggregatePlaceHandler(
	regionRepo repositories.RegionRepository,
	hotelRepo repositories.HotelRepository,
	inActiveHotelsRepository repositories.InActiveHotelsRepository,
	esPlaceRepo esRepo.PlaceElasticRepository,
) AggregatePlaceHandler {
	return &aggregatePlaceHandler{
		regionRepo,
		hotelRepo,
		inActiveHotelsRepository,
		esPlaceRepo,
	}
}

func WriteLineToFile(filename string, line string) error {
	// Open the file in append mode, create it if it doesn't exist
	file, err := os.OpenFile(filename, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0o644)
	if err != nil {
		return fmt.Errorf("error opening file: %w", err)
	}
	defer file.Close()

	// Write the line to the file with a newline character
	if _, err := file.WriteString(line + "\n"); err != nil {
		return fmt.Errorf("error writing to file: %w", err)
	}

	return nil
}

func ChunkRegionsMapByKeys(hotelMap map[string][]*domain.Region, chunkSize int) []map[string][]*domain.Region {
	var chunks []map[string][]*domain.Region

	// Create a slice to hold the keys of the original map
	keys := make([]string, 0, len(hotelMap))
	for key := range hotelMap {
		keys = append(keys, key)
	}

	// Iterate over the keys and create chunks
	for i := 0; i < len(keys); i += chunkSize {
		end := i + chunkSize
		if end > len(keys) {
			end = len(keys)
		}

		// Create a new map for the chunk
		chunk := make(map[string][]*domain.Region)
		for j := i; j < end; j++ {
			chunk[keys[j]] = hotelMap[keys[j]]
		}

		chunks = append(chunks, chunk)
	}

	return chunks
}

func ChunkHotelMapByKeys(hotelMap map[string][]*domain.Hotel, chunkSize int) []map[string][]*domain.Hotel {
	var chunks []map[string][]*domain.Hotel

	// Create a slice to hold the keys of the original map
	keys := make([]string, 0, len(hotelMap))
	for key := range hotelMap {
		keys = append(keys, key)
	}

	// Iterate over the keys and create chunks
	for i := 0; i < len(keys); i += chunkSize {
		end := i + chunkSize
		if end > len(keys) {
			end = len(keys)
		}

		// Create a new map for the chunk
		chunk := make(map[string][]*domain.Hotel)
		for j := i; j < end; j++ {
			chunk[keys[j]] = hotelMap[keys[j]]
		}

		chunks = append(chunks, chunk)
	}

	return chunks
}

func (h *aggregatePlaceHandler) HandleHotel(ctx context.Context, countryCode string) error {
	excludeHotelID := []string{}

	fmt.Println("new loop ", countryCode)

	hotels, err := h.hotelRepo.FindAllByCountryCode(ctx, countryCode)
	if err != nil {
		return err
	}

	fmt.Println("hotel", len(hotels), len(excludeHotelID))
	start := time.Now().UnixMilli()

	hotelMap := make(map[string][]*domain.Hotel, len(hotels))
	for _, hotel := range hotels {
		hotelMap[hotel.HotelID] = append(hotelMap[hotel.HotelID], hotel)
	}

	chunkHotelMap := ChunkHotelMapByKeys(hotelMap, 1000)
	fmt.Println(len(chunkHotelMap))

	for index, hotelMapChunked := range chunkHotelMap {
		var oErr []error
		var buf bytes.Buffer

		for _, hotel := range hotelMapChunked {
			modelPlace := converts.FromDomainHotel(hotel[0], hotel)

			data, err := json.Marshal(modelPlace)
			if err != nil {
				oErr = append(oErr, fmt.Errorf("json.Marshal : %w", err))
				continue
			}

			meta := []byte(fmt.Sprintf(`{ "index" : { "_id" : "hotel_%s" } }%s`, modelPlace.PlaceID, "\n"))

			data = append(data, "\n"...)

			buf.Grow(len(meta) + len(data))
			buf.Write(meta)
			buf.Write(data)
		}

		if buf.Len() == 0 {
			log.Error("[aggregatePlaceHandler] buf data emty",
				log.String("index name", config.PlaceIndexName))
			return errors.ErrSomethingOccurred
		}

		if len(oErr) > 0 {
			log.Info("[aggregatePlaceHandler] convert to es model fail", log.Any("total err", len(oErr)))
		}

		err = h.esPlaceRepo.Bulk(ctx, config.PlaceIndexName, buf.Bytes())
		if err != nil {
			log.Error("[aggregatePlaceHandler] h.elasticsearch.Bulk error",
				log.Any("error", err),
				log.String("index name", config.PlaceIndexName))
			// err = WriteLineToFile("./logs/es_aggregate_err.jsonl", string(buf.Bytes()))
			if err != nil {
				log.Error("WriteLineToFile err", log.Any("err", err))
			}

			return errors.ErrSomethingOccurred
		}

		fmt.Println("[aggregatePlaceHandler] h.elasticsearch.Bulk create done ", index)
	}

	fmt.Println("[aggregatePlaceHandler] h.elasticsearch.Bulk success", time.Now().UnixMilli()-start)

	return nil
}

func (h *aggregatePlaceHandler) HandleRegion(ctx context.Context, countryCode string) error {
	// TO DO Get count from config or save count in db
	// regionCount, err := h.regionRepo.Count(ctx, constants.DefaultLanguage)
	// if err != nil {
	// 	log.Error("[aggregatePlaceHandler] regionRepo.Count error",
	// 		log.Any("error", err))
	// 	return errors.ErrSomethingOccurred
	// }
	excludeRegionID := []string{}

	regions, err := h.regionRepo.FindAllByCountryCode(ctx, countryCode)
	if err != nil {
		log.Error("[aggregatePlaceHandler]  h.regionRepo.FindAllByCountryCode error",
			log.Any("error", err),
			log.String("countryCode", countryCode))

		return errors.ErrSomethingOccurred
	}

	fmt.Println("region", len(regions), len(excludeRegionID))

	regionMap := make(map[string][]*domain.Region, len(regions))
	for _, region := range regions {
		regionMap[region.RegionID] = append(regionMap[region.RegionID], region)
	}

	chunkRegionMap := ChunkRegionsMapByKeys(regionMap, 1000)
	for index, regionMapChunked := range chunkRegionMap {
		var oErr []error
		var buf bytes.Buffer

		for _, regions := range regionMapChunked {
			modelPlace := converts.FromDomainRegion(regions[0], regions)

			data, err := json.Marshal(modelPlace)
			if err != nil {
				oErr = append(oErr, fmt.Errorf("json.Marshal : %w", err))
				continue
			}

			meta := []byte(fmt.Sprintf(`{ "index" : { "_id" : "%s" } }%s`, modelPlace.PlaceID, "\n"))

			data = append(data, "\n"...)
			buf.Grow(len(meta) + len(data))
			buf.Write(meta)
			buf.Write(data)
		}

		if buf.Len() == 0 {
			log.Error("[aggregatePlaceHandler] buf data emty",
				log.String("index name", config.PlaceIndexName))
			return errors.ErrSomethingOccurred
		}

		if len(oErr) > 0 {
			log.Info("[aggregatePlaceHandler] convert to es model fail", log.Any("total err", len(oErr)))
		}

		err = h.esPlaceRepo.Bulk(ctx, config.PlaceIndexName, buf.Bytes())
		if err != nil {
			log.Error("[aggregatePlaceHandler] h.elasticsearch.Bulk error",
				log.Any("error", err),
				log.String("index name", config.PlaceIndexName))

			return errors.ErrSomethingOccurred
		}

		fmt.Println("[aggregatePlaceHandler] h.elasticsearch.Bulk create done ", index)
	}

	log.Info("[aggregatePlaceHandler] h.elasticsearch.Bulk Region success")

	return nil
}

func (h *aggregatePlaceHandler) Handle(ctx context.Context, countryCode string, syncES bool) error {
	if syncES {
		var wg sync.WaitGroup

		if countryCode == "" {
			wg.Add(1)

			go func() {
				for _, code := range constants.HotelCountryCodes {
					err := h.HandleHotel(ctx, code)
					if err != nil {
						log.Error("HandleHotel err", log.Any("err", err), log.Any("country_code", code))
					}
				}

				defer wg.Done()
			}()
			// wg.Add(1)
			//
			//	go func() {
			//		for _, code := range constants.RegionCountryCodes {
			//			err := h.HandleRegion(ctx, code)
			//			if err != nil {
			//				log.Error("HandleRegion err", log.Any("err", err), log.Any("country_code", code))
			//			}
			//		}
			//		defer wg.Done()
			//	}()
		} else {
			wg.Add(1)

			go func() {
				err := h.HandleHotel(ctx, countryCode)
				if err != nil {
					log.Error("HandleHotel err", log.Any("err", err), log.Any("country_code", countryCode))
				}

				defer wg.Done()
			}()
			// wg.Add(1)
			//
			//	go func() {
			//		err := h.HandleRegion(ctx, countryCode)
			//		if err != nil {
			//			log.Error("HandleRegion err", log.Any("err", err), log.Any("country_code", countryCode))
			//		}
			//		defer wg.Done()
			//	}()
		}

		wg.Wait()
	}

	inActiveHotel, err := h.inActiveHotelsRepository.FindAll(ctx)
	if err != nil {
		log.Error("HandleHotel err", log.Any("err", err))
		return err
	}

	const batchSize = 1000

	idsToDelete := []string{}
	count := 0
	for _, hotel := range inActiveHotel {
		hotelID := fmt.Sprintf("hotel_%s", hotel.HotelID)
		idsToDelete = append(idsToDelete, hotelID)
		count++
		// If we reach the batch size, perform a bulk delete
		if len(idsToDelete) >= batchSize {
			err := h.esPlaceRepo.BulkDelete(ctx, config.PlaceIndexName, idsToDelete)
			if err != nil {
				log.Error("BulkDelete err", log.Any("err", err))
				continue
			}

			log.Info("deleted", log.Any("count", count))
			idsToDelete = nil
		}
	}

	// Delete any remaining IDs after the loop
	if len(idsToDelete) > 0 {
		err := h.esPlaceRepo.BulkDelete(ctx, config.PlaceIndexName, idsToDelete)
		if err != nil {
			log.Error("BulkDelete err", log.Any("err", err))
		}
	}

	log.Info("Deleted done", log.Any("count", count))
	return nil
}
