package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type AggregateHotelContentReviewHandler interface {
	Handle(ctx context.Context, req *AggregateHotelContentReviewReq) error
}

type aggregateHotelContentReviewHandler struct {
	hotelRepo repositories.HotelRepository
}

type AggregateHotelContentReviewReq struct {
	ContentVersion    string
	Provider          commonEnum.HotelProvider
	PrefixNumber      int
	RateHawkContent   *domain.RateHawkHotelReview
	SkipUpdateOverall bool
}

func NewAggregateHotelContentReviewHandler(
	hotelRepo repositories.HotelRepository,
) AggregateHotelContentReviewHandler {
	return &aggregateHotelContentReviewHandler{hotelRepo}
}

func (h *aggregateHotelContentReviewHandler) Handle(ctx context.Context, req *AggregateHotelContentReviewReq) error {
	switch req.Provider {
	case commonEnum.HotelProviderExpedia, commonEnum.HotelProviderRateHawk:
		return h.HandleUpdateReviewContent(ctx, req)
	}

	return nil
}

func (h *aggregateHotelContentReviewHandler) HandleUpdateReviewContent(ctx context.Context, req *AggregateHotelContentReviewReq) error {
	if req == nil {
		log.Error("HandleInitContent req nil")
		return errors.ErrInvalidInput
	}

	var hotelID string
	var ratings *domain.GuestRating

	switch req.Provider {
	case commonEnum.HotelProviderRateHawk:
		if req.RateHawkContent == nil {
			log.Error("HandleInitContent req.RateHawkContent nil")
			return errors.ErrInvalidInput
		}
		hotelID, ratings = req.RateHawkContent.ToHotelGuestRating()
	default:
		log.Error("HandleInitContent un-support provider")
		return domain.ErrNoProviderForOfficeID
	}

	if hotelID == "" || ratings == nil {
		return errors.ErrSomethingOccurred
	}

	if !req.SkipUpdateOverall {
		err := h.hotelRepo.UpdateHotelRating(ctx, hotelID, req.Provider, ratings)
		if err != nil {
			log.Error("Find hotel error", log.Any("err", err))
			return err
		}
	}

	return nil
}
