package command

import (
	"context"
	"time"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type BookOldProviderHandler interface {
	Handle(ctx context.Context, req *domain.HubBookOldProviderReq) error
}

type bookOldProviderHandler struct {
	priceCheckSvc service.PriceCheckService
	bookService   service.BookingService
	bookingRepo   repositories.OrderRepository
}

func NewBookOldProviderHandler(
	priceCheckSvc service.PriceCheckService,
	bookService service.BookingService,
	bookingRepo repositories.OrderRepository,
) BookOldProviderHandler {
	return &bookOldProviderHandler{priceCheckSvc, bookService, bookingRepo}
}

// Deprecate by disable ta_provider 15
func (h *bookOldProviderHandler) Handle(ctx context.Context, req *domain.HubBookOldProviderReq) error {
	hubOrder, err := h.bookingRepo.FindOneByOrderCode(ctx, "", req.OrderCode)
	if err != nil {
		log.Error("CancelBooking FindOneByOrderCode", log.Any("err", err), log.String("orderCode", req.OrderCode))
		return nil
	}

	if hubOrder == nil {
		return errors.ErrNotFound
	}

	hotel := hubOrder.Hotel
	pivotRoom := hotel.GetPivotRoom()
	selectedRate := hubOrder.RateData

	h.rollbackPreviousProviderInfo(hubOrder)

	priceCheckReq := &domain.HubPriceCheckReq{
		OfficeID:        hubOrder.OfficeID,
		SearchKey:       hubOrder.SearchKey,
		HotelID:         hotel.HotelID,
		RoomID:          pivotRoom.RoomID,
		RateID:          selectedRate.RateID,
		BedOptionID:     pivotRoom.BedOption.OptionID,
		Stateful:        true,
		EnableProviders: []commonEnum.HotelProvider{hubOrder.Provider},
	}

	priceCheckRes, err := h.priceCheckSvc.PriceCheck(ctx, priceCheckReq)
	if err != nil {
		log.Error("priceCheckSvc.PriceCheck error", log.Any("error", err), log.Any("req", req))
		return h.failBooking(ctx, enum.BookingStatusFailed, hubOrder, err)
	}

	providerCfRate := priceCheckRes.ProviderCfRate

	hubCfRate := hubOrder.OriginalRateDataCf

	if providerCfRate.PayNow > hubCfRate.PayNow {
		return h.failBooking(ctx, enum.BookingStatusFailed, hubOrder, domain.ErrBookingFailed)
	}

	if providerCfRate != nil {
		hubOrder.FareDataIssuing = &domain.FareDataIssuing{
			Amount:       providerCfRate.PayNow,
			AmountString: "",
			Currency:     providerCfRate.Currency,
			Type:         "",
		}
	}

	if priceCheckRes.SessionInfo == nil {
		return h.failBooking(ctx, enum.BookingStatusFailed, hubOrder, domain.ErrSessionInvalidOrExpired)
	}

	session := &domain.HotelSession{
		SessionID:           priceCheckRes.SessionID,
		OfficeID:            hubOrder.OfficeID,
		ExpiredAt:           time.Now().Add(constants.SessionExpireTime).UnixMilli(),
		ExpediaSessionInfo:  priceCheckRes.SessionInfo.ExpediaSessionInfo,
		TourmindSessionInfo: priceCheckRes.SessionInfo.TourmindSessionInfo,
		BasicSessionInfo:    priceCheckRes.SessionInfo.BasicSessionInfo,
	}

	temp := hubOrder.SessionID
	hubOrder.SessionID = priceCheckRes.SessionID
	hubOrder.NewSessionID = hubOrder.SessionID

	bookReq := &domain.HubBookReq{
		OfficeID:         hubOrder.OfficeID,
		SessionID:        hubOrder.SessionID,
		Holder:           hubOrder.RequestHolder,
		EndUserIPAddress: hubOrder.CustomerIP,
		// EndUserBrowserAgent: hubOrder.,
		// AgentID:   "",
		OrderCode: hubOrder.OrderCode,
	}

	reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, _, err := h.bookService.Book(ctx, hubOrder.Provider, bookReq, session, hubOrder)
	if err != nil {
		return h.failBooking(ctx, bookingStatus, hubOrder, err)
	}

	hubOrder.ProviderBookingStatus = providerBookingStatus
	hubOrder.ReservationCode = reservationCode
	hubOrder.BookingStatus = bookingStatus
	hubOrder.SessionID = temp

	if bookingStatus == enum.BookingStatusSuccess {
		converts.MapRoomConfirmationID(hubOrder.Provider, hubOrder.Hotel.ListRooms, confirmationIDs)
	}

	// Renew PendingDeadline
	if bookingStatus == enum.BookingStatusPending && hubOrder.Provider != commonEnum.HotelProviderExpediaManual {
		hubOrder.PendingDeadline = time.Now().Add(constants.TicketPendingDeadlineTime).UnixMilli()
		hubOrder.PendingStartAt = time.Now().UnixMilli()

		if hubOrder.Provider == commonEnum.HotelProviderExpedia {
			hubOrder.PendingStartAt = time.Now().Add(time.Second * 90).UnixMilli()
		}

		if hubOrder.Provider == commonEnum.HotelProviderTA {
			hubOrder.PendingStartAt = time.Now().Add(time.Minute * 25).UnixMilli()
		}
	} else {
		hubOrder.PendingDeadline = 0
	}

	err = h.bookingRepo.UpdateOne(ctx, hubOrder.ID, hubOrder)
	if err != nil {
		log.Error("h.hotelOrder.UpdateOne error", log.Any("error", err), log.String("orderID", hubOrder.ID), log.Any("req", req))
		return err
	}

	return nil
}

func (h *bookOldProviderHandler) rollbackPreviousProviderInfo(hubOrder *domain.HubHotelOrder) {
	// 1
	temp := hubOrder.Provider
	hubOrder.Provider = hubOrder.OldProvider
	hubOrder.OldProvider = temp

	// 2
	temp2 := hubOrder.Hotel.ProviderHotelID
	hubOrder.Hotel.ProviderHotelID = hubOrder.Hotel.OldProviderID
	hubOrder.Hotel.ProviderHotelID = temp2

	// 3
	for _, room := range hubOrder.Hotel.ListRooms {
		temp3 := room.ProviderRoomID
		room.ProviderRoomID = room.OldProviderRoomID
		room.OldProviderRoomID = temp3
	}
}

func (h *bookOldProviderHandler) failBooking(ctx context.Context, bkStatus enum.BookingStatus, hubOrder *domain.HubHotelOrder, rootErr error) error {
	if bkStatus == enum.BookingStatusFailed || bkStatus == enum.BookingStatusNone {
		rollbackCtx, cc := context.WithTimeout(context.Background(), constants.DefaultCtxTimeout)
		defer cc()
		// Cancel booking due to order is unusable
		err := h.bookService.MarkBookingFail(rollbackCtx, hubOrder)
		if err != nil {
			log.Error("bookService.Refund error", log.Any("error", err), log.String("orderCode", hubOrder.OrderCode))
			return err
		}

		err = h.bookService.Refund(ctx, hubOrder)
		if err != nil {
			log.Error("bookService.Refund error", log.Any("error", err), log.String("orderCode", hubOrder.OrderCode))
			return err
		}

		return rootErr
	}

	return rootErr
}
