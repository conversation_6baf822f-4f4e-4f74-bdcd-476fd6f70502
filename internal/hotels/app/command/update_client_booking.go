package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type UpdateClientBookingHandler interface {
	Handle(ctx context.Context, reqs []*domain.ClientBookingUpdateReq) error
}

type updateClientBookingHandler struct {
	clientBookingRepo repositories.ClientBookingRepository
}

func NewUpdateClientBookingHandler(
	clientBookingRepo repositories.ClientBookingRepository,
) UpdateClientBookingHandler {
	return &updateClientBookingHandler{
		clientBookingRepo,
	}
}

func (h *updateClientBookingHandler) Handle(ctx context.Context, reqs []*domain.ClientBookingUpdateReq) error {
	err := h.clientBookingRepo.WithTransaction(ctx, func(tnxClientBooking context.Context) (interface{}, error) {
		for _, updateReq := range reqs {
			if len(updateReq.HubOrderCodes) == 0 {
				continue
			}

			err := h.clientBookingRepo.UpdateByHubOrderCodes(tnxClientBooking, updateReq)
			if err != nil {
				log.Error("UpdateByHubOrderCodes err", log.Any("err", err), log.Any("updateReq", updateReq))
				return nil, err
			}
		}

		return reqs, nil
	})

	if err != nil {
		log.Error("WithTransaction err", log.Any("err", err))
		return errors.ErrSomethingOccurred
	}

	return nil
}
