package command

import (
	"context"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type CheckAvailabilityHandler interface {
	Handle(ctx context.Context, req *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error)
}

type checkAvailabilityHandler struct {
	cfg               *config.Schema
	searchService     service.CheckAvailabilityService
	currencyExSvc     service.CurrencyExchangeService
	hiddenFeeSvc      service.HiddenFeeService
	inActiveHotelRepo repositories.InActiveHotelsRepository
}

func NewCheckAvailabilityHandler(
	cfg *config.Schema,
	searchService service.CheckAvailabilityService,
	currencyExSvc service.CurrencyExchangeService,
	hiddenFeeSvc service.HiddenFeeService,
	inActiveHotelRepo repositories.InActiveHotelsRepository,
) CheckAvailabilityHandler {
	return &checkAvailabilityHandler{cfg, searchService, currencyExSvc, hiddenFeeSvc, inActiveHotelRepo}
}

func (h *checkAvailabilityHandler) Handle(ctx context.Context, req *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error) {
	if err := req.Stay.CountDays(); err != nil {
		log.Error("SearchHotelPrice stay.DayCount err", log.Any("err", err), log.Any("stay.DayCount", req.Stay.CountDays))
		return nil, err
	}

	if req.RequestCurrency != "" && req.RequestCurrency != constants.VNDCurrency {
		if err := h.currencyExSvc.ValidateCurrency(ctx, req.RequestCurrency); err != nil {
			log.Error("h.currencyExSvc.ValidateCurrency err", log.Any("err", err), log.Any("currency", req.RequestCurrency))
			return nil, err
		}
	}

	req.Stay.RoomCount = req.CountRooms()

	if req.HotelID == "" && len(req.ListHotels) > 0 {
		req.HotelID = req.ListHotels[0]
	}

	if len(req.ListHotels) == 0 {
		return nil, errors.New(errors.BadRequest, "not support multi hotel request")
	}

	inActiveHotel, err := h.inActiveHotelRepo.FindByHotelIDs(ctx, []string{req.HotelID})
	if err != nil {
		log.Error("h.inActiveHotelRepo.FindByHotelIDs err", log.Any("err", err), log.Any("req.HotelID", req.HotelID))
		return nil, errors.ErrSomethingOccurred
	}

	if len(inActiveHotel) > 0 {
		searchKey, _ := req.GenSearchKey()

		return &domain.HubCheckAvailabilityRes{
			IsSuccess:            true,
			SearchKey:            searchKey,
			Hotels:               []*domain.HubHotel{},
			AvailableHotelReturn: 0,
		}, nil
	}

	provider := commonEnum.HotelProviderFromHash(req.MatchKey)
	if provider == commonEnum.HotelProviderNone {
		return nil, domain.ErrInvalidMatchKey
	}

	isSubHubRequest := ctx.Value(constants.IsSubHubRequestKey)
	if !lo.Contains(req.EnableProviders, provider) && isSubHubRequest == nil {
		return nil, domain.ErrProviderNotAllowed
	}

	enableProvider := []commonEnum.HotelProvider{provider}

	// Search
	res, _, err := h.searchService.Search(ctx, req, enableProvider)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return nil, nil
	}

	res.Hotels, err = h.currencyExSvc.ConvertCurrency(ctx, req, enableProvider[0], res.Hotels, constants.VNDCurrency)
	if err != nil {
		log.Error("currencyExSvc.ConvertCurrency err", log.Any("err", err))
		return nil, err
	}

	for _, hotel := range res.Hotels {
		for _, room := range hotel.ListRooms {
			rateFinal := make([]*domain.HubRateData, 0)

			for _, rate := range room.RateData {
				// Nil check để tránh panic
				if rate == nil {
					continue
				}

				if rate.IsSoldOut {
					continue
				} else {
					rateFinal = append(rateFinal, rate)
				}
			}

			room.RateData = rateFinal
		}

		calcReq := &domain.CalculateHotelDetailPricesReq{
			PartnershipID: req.PartnershipID,
			OfficeID:      req.OfficeID,
			Multiplier:    uint32(req.Stay.DayCount) * uint32(req.Stay.RoomCount),
			HubHotel:      hotel,
			Provider:      provider,
		}
		if isSubHubRequest != nil && isSubHubRequest.(bool) {
			calcReq.Provider = commonEnum.HotelProviderNone
		}

		h.hiddenFeeSvc.CalculateHotelDetailPrices(ctx, calcReq)

		if req.RequestCurrency != "" && req.RequestCurrency != constants.VNDCurrency {
			for _, room := range hotel.ListRooms {
				for idx, rate := range room.RateData {
					rateDateExchange, _, err := h.currencyExSvc.ConvertHubRateDataCurrency(ctx, rate, provider, rate.Currency, req.RequestCurrency)
					if err != nil {
						log.Error("currencyExSvc.ConvertHubRateDataCurrency err", log.Any("err", err))
						continue
					}

					room.RateData[idx] = rateDateExchange
				}
			}
		}
	}

	// group rate data
	formatGroupBy := req.FormatRateDataGroups()
	converts.GroupRateData(res.Hotels, formatGroupBy)

	return res, nil
}
