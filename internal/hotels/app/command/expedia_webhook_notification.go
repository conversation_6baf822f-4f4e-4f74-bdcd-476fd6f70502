package command

import (
	"bytes"
	"context"
	"crypto/sha512"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"gitlab.deepgate.io/apps/common/constants"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type ExpediaWebhookNotificationHandler interface {
	Handle(ctx context.Context, req *domain.ExpediaWebhookNotificationRequest) error
}

type expediaWebhookNotificationHandler struct {
	cfg                            *config.Schema
	hotelOrderRepo                 repositories.OrderRepository
	expediaWebhookNotificationRepo repositories.ExpediaWebhookNotificationRepository
	webhookService                 service.WebhookService
	notificationClient             notification.NotificationServiceClient
}

func NewExpediaWebhookNotificationHandler(
	cfg *config.Schema,
	hotelOrderRepo repositories.OrderRepository,
	expediaWebhookNotificationRepo repositories.ExpediaWebhookNotificationRepository,
	webhookService service.WebhookService,
	notificationClient notification.NotificationServiceClient,
) ExpediaWebhookNotificationHandler {
	return &expediaWebhookNotificationHandler{
		cfg:                            cfg,
		hotelOrderRepo:                 hotelOrderRepo,
		expediaWebhookNotificationRepo: expediaWebhookNotificationRepo,
		webhookService:                 webhookService,
		notificationClient:             notificationClient,
	}
}

func (h *expediaWebhookNotificationHandler) validateRequest(ctx context.Context, req *domain.ExpediaWebhookNotificationRequest) error {
	if req == nil {
		return errors.ErrInvalidInput
	}

	if req.APIKey != h.cfg.ExpediaApiKey {
		return errors.NewV2(errors.Unauthorized, "invalid_request", "")
	}

	timestamp := fmt.Sprintf("%d", req.Timestamp)

	if h.getSigSHA512(timestamp) != req.Signature {
		return errors.NewV2(errors.Unauthorized, "invalid signature", "Invalid signature")
	}

	req.GenKey()

	record, err := h.expediaWebhookNotificationRepo.FindByKey(ctx, req.Key)
	if err != nil {
		log.Error("expediaWebhookNotificationRepo.FindByNonce error", log.Any("error", err), log.Any("nonce", req.Key))
		return err
	}

	if record != nil {
		return errors.NewV2(errors.Unauthorized, "invalid request", "Duplicate request")
	}

	return nil
}

func (h *expediaWebhookNotificationHandler) getSigSHA512(timestamp string) string {
	sig := h.cfg.ExpediaApiKey + h.cfg.ExpediaSharedSecret + timestamp
	hash := sha512.New()
	hash.Write([]byte(sig))

	return hex.EncodeToString(hash.Sum(nil))
}

func (h *expediaWebhookNotificationHandler) Handle(ctx context.Context, req *domain.ExpediaWebhookNotificationRequest) error {
	err := h.validateRequest(ctx, req)
	if err != nil {
		return err
	}

	req.ExpediaWebhookHeader = &domain.ExpediaWebhookHeader{}

	jsonRequest, err := json.Marshal(req)
	if err != nil {
		log.Error("[Expedia Webhook Notification] json.Marshal error", log.Any("error", err))
	}

	err = h.notificationClient.SendMessage(ctx, &notification.SendMessageRequest{
		EntityType:  constants.NotificationEntityPartnership,
		EntityID:    h.cfg.HubPartnershipID,
		ServiceCode: constants.NotificationServiceCodeHUB,
		Action:      constants.TelegramNotificationActionHotelExpediaWebhook,
		Data:        map[string]string{"Payload": string(jsonRequest)},
	})
	if err != nil {
		log.Error("[Expedia Webhook Notification] notificationClient.SendMessage error", log.Any("error", err))
	}

	if err := h.expediaWebhookNotificationRepo.Insert(ctx, req); err != nil {
		log.Error("[Expedia Webhook Notification] expediaWebhookNotificationRepo.Create error", log.Any("error", err), log.Any("req", req))
		return errors.ErrSomethingOccurred
	}

	return nil
}

type payload struct {
	ChatID    string `json:"chat_id"`
	Text      string `json:"text"`
	ParseMode string `json:"parse_mode,omitempty"`
}

func (c *expediaWebhookNotificationHandler) SendMessageTelegram(ctx context.Context, message string, chatID string, parseMode string) error {
	requestURL := "https://api.telegram.org/bot7205375135:AAF9-RjCU4QekWbNBrWxnrdTT-oG1lqNdLA/sendMessage"

	payloadSend := payload{
		ChatID: chatID,
		Text:   message,
	}

	if parseMode != "" {
		payloadSend.ParseMode = "markdown"
	}

	payloadJSON, err := json.Marshal(payloadSend)
	if err != nil {
		log.Error("[Telegram][SendMessage] marshal failed", log.String("url", requestURL), log.Any("err", err))
		return err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, bytes.NewBuffer(payloadJSON))
	if err != nil {
		log.Error("[Telegram][SendMessage] create request failed", log.String("url", requestURL), log.Any("err", err))
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	res, err := http.DefaultClient.Do(req)
	if err != nil {
		log.Error("[Telegram][SendMessage] send request failed", log.String("url", requestURL), log.String("message", message), log.Any("err", err))
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		log.Error("[Telegram][SendMessage] request failed", log.String("url", requestURL), log.Any("payload", payloadSend), log.Any("status_code", res.StatusCode))
		body, _ := io.ReadAll(res.Body)
		log.Error("[Telegram][SendMessage] response body", log.String("body", string(body)))

		return err
	}

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error("[Telegram][SendMessage] response read failed", log.String("url", requestURL), log.Any("err", err))
		return err
	}

	log.Info("[Telegram][SendMessage] message sent successfully", log.String("response", string(body)))

	return nil
}
