package command

import (
	"context"
	"fmt"
	"time"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mq"
	taclient "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type TAWebhookHandler interface {
	Handle(ctx context.Context, msg *domain.TAWebhookMsg) error
}

type taWebhookHandler struct {
	hubOrderRepo repositories.OrderRepository
	taAdapter    taclient.TAAdapter
	mqClient     mq.Adapter
}

func NewTAWebhookHandler(
	hubOrderRepo repositories.OrderRepository,
	taAdapter taclient.TAAdapter,
	mqClient mq.Adapter,
) TAWebhookHandler {
	return &taWebhookHandler{
		hubOrderRepo,
		taAdapter,
		mqClient,
	}
}

func (h *taWebhookHandler) Handle(ctx context.Context, msg *domain.TAWebhookMsg) error {
	if msg == nil || msg.BookingId == "" {
		return errors.ErrInvalidInput
	}

	status := msg.Status
	loop := 3

GET_ORDER:
	hubOrder, err := h.hubOrderRepo.FindOneByReservationCode(ctx, msg.BookingId, commonEnum.HotelProviderTA)

	if err != nil {
		log.Error("hubOrderRepo.FindOneByReservationCode error", log.Any("error", err), log.String("ReservationCode", msg.BookingId))
		return err
	}

	if hubOrder == nil {
		return errors.ErrNotFound
	}

	if hubOrder.BookingStatus == enum.BookingStatusSuccess || hubOrder.BookingStatus == enum.BookingStatusCancel {
		return nil
	}

	// In case api book_hotel has not yet finished
	if loop > 0 && hubOrder.BookingStatus != enum.BookingStatusPending {
		loop--

		time.Sleep(time.Second)

		goto GET_ORDER
	}

	cfRate := hubOrder.ExchangedRateDataCfRaw

	switch status {
	case domain.TABookingStatusCanceled:
		{
			if err := h.cancelOrder(ctx, msg, hubOrder, false, ""); err != nil {
				return err
			}
		}
	case domain.TABookingStatusPriceChange:
		{
			if msg.Amount > cfRate.PayNow {
				cancelReason := fmt.Sprintf("price_change %f > %f", msg.Amount, cfRate.PayNow)
				if err := h.cancelOrder(ctx, msg, hubOrder, true, cancelReason); err != nil {
					return err
				}
			} else {
				finalAMount, err := h.taAdapter.ConfirmBook(ctx, msg.BookingId, hubOrder.OrderCode)
				if err != nil {
					log.Error("taAdapter.Confirm error", log.Any("error", err), log.String("ReservationCode", msg.BookingId))
					return err
				}

				hubOrder.FareDataIssuing = &domain.FareDataIssuing{
					Amount: finalAMount,
				}
			}
		}
	case domain.TABookingStatusCompleted:
		{
			h.mapRoomConfirmationIDs(hubOrder, msg.ConfirmationInfos)
			hubOrder.BookingStatus = enum.BookingStatusSuccess
			hubOrder.ProviderBookingStatus = msg.Status
			hubOrder.FareDataIssuing = &domain.FareDataIssuing{
				Amount: msg.Amount,
			}
		}
	}

	if err := h.hubOrderRepo.UpdateOne(ctx, hubOrder.ID, hubOrder); err != nil {
		log.Error("hubOrderRepo.UpdateOne error", log.Any("error", err), log.String("ReservationCode", msg.BookingId))
		return err
	}

	return nil
}

func (h *taWebhookHandler) cancelOrder(ctx context.Context, msg *domain.TAWebhookMsg, hubOrder *domain.HubHotelOrder, cancelProvider bool, reason string) error {
	if cancelProvider {
		if err := h.taAdapter.Cancel(ctx, msg.BookingId, hubOrder.OrderCode); err != nil {
			log.Error("taAdapter.Cancel error", log.Any("error", err), log.String("ReservationCode", msg.BookingId))
			return err
		}
	}

	if reason == "" {
		hubOrder.CancelReason = msg.Note
	} else {
		hubOrder.CancelReason = reason
	}

	if err := h.mqClient.BookOldProvider(ctx, hubOrder.OrderCode); err != nil {
		log.Error("mqClient.BookOldProvider error", log.Any("error", err), log.String("ReservationCode", msg.BookingId))
		return err
	}

	return nil
}

func (h *taWebhookHandler) mapRoomConfirmationIDs(order *domain.HubHotelOrder, input []domain.BookingConfirmationIdInfo) bool {
	baseCfID := order.ReservationCode

	for _, item := range input {
		if item.ConfirmationId != "" {
			baseCfID = item.ConfirmationId
			break
		}
	}

	for _, room := range order.Hotel.ListRooms {
		for _, info := range input {
			if info.ProductId == room.ProviderRoomID && !info.Used && room.ConfirmationID == "" {
				room.ConfirmationID = info.ConfirmationId
				info.Used = true

				break
			}
		}

		if room.ConfirmationID == "" {
			room.ConfirmationID = baseCfID
		}
	}

	return true
}
