package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
)

type ForceFailOrderHandler interface {
	Handle(ctx context.Context, orderID string) error
}

type forceFailOrderHandler struct {
	hotelOrderRepo repositories.OrderRepository
	bookingService service.BookingService
}

func NewForceFailOrderHandler(
	hotelOrderRepo repositories.OrderRepository,
	bookingService service.BookingService,
) ForceFailOrderHandler {
	return &forceFailOrderHandler{
		hotelOrderRepo: hotelOrderRepo,
		bookingService: bookingService,
	}
}

func (h *forceFailOrderHandler) Handle(ctx context.Context, orderID string) error {
	hotelOrder, err := h.hotelOrderRepo.FindOrderByID(ctx, orderID)
	if err != nil {
		log.Error("hotelOrderRepo.FindOneByOrderCode error", log.Any("error", err), log.String("orderID", orderID))
		return err
	}

	if hotelOrder == nil || hotelOrder.Hotel == nil {
		return errors.ErrNotFound
	}

	if err := h.bookingService.MarkBookingFail(ctx, hotelOrder); err != nil {
		log.Error("bookingService.MarkBookingFail err", log.Any("err", err), log.Any("bookingCode", hotelOrder.OrderCode))
		return err
	}

	if err := h.bookingService.Refund(ctx, hotelOrder); err != nil {
		log.Error("bookingService.Refund err", log.Any("err", err), log.Any("bookingCode", hotelOrder.OrderCode))
		return err
	}

	return nil
}
