package command

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

var mu sync.Mutex

const (
	DefaultLanguage = "vi-VN"
)

const (
	maxRetries = 3
	retryDelay = 500 * time.Millisecond
)

type AggregateHotelContentHandler interface {
	Handle(ctx context.Context, req *AggregateHotelContentReq) error
}

type aggregateHotelContentHandler struct {
	hotelRepo        repositories.HotelRepository
	roomRepo         repositories.RoomRepository
	syncContentRepo  repositories.SyncContentRepository
	displayImageRepo repositories.DisplayImageRepository
}

type MigrateData struct {
	Page           int
	From           int
	To             int
	TargetLanguage string
	SourceLanguage string
}

type AggregateHotelContentReq struct {
	Migrate             *MigrateData
	ContentVersion      string
	Provider            commonEnum.HotelProvider
	PrefixNumber        int
	Language            string
	ExpediaHotelContent *domain.ExpediaHotelContent
	RateHawkContent     *domain.RateHawkHotelContent
}

func NewAggregateHotelContentHandler(
	hotelRepo repositories.HotelRepository,
	roomRepo repositories.RoomRepository,
	syncContentRepo repositories.SyncContentRepository,
	displayImageRepo repositories.DisplayImageRepository,
) AggregateHotelContentHandler {
	return &aggregateHotelContentHandler{hotelRepo, roomRepo, syncContentRepo, displayImageRepo}
}

func WriteErrorRecordForRetry(content any, filepath string) {
	file, err := os.Create(filepath)
	if err != nil {
		fmt.Println("Error creating file:", err)
		return
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ") // Tùy chọn để định dạng đẹp

	if err := encoder.Encode(content); err != nil {
		fmt.Println("Error writing to file:", err)
		return
	}
}

func (h *aggregateHotelContentHandler) Handle(ctx context.Context, req *AggregateHotelContentReq) error {
	switch req.Provider {
	case commonEnum.HotelProviderExpedia, commonEnum.HotelProviderRateHawk:
		if req.Migrate != nil {
			return h.SyncContent(ctx, req.Migrate.From)
		}

		return h.HandleInitContent(ctx, req)
	}

	return nil
}

func (h *aggregateHotelContentHandler) HandleInitContent(ctx context.Context, req *AggregateHotelContentReq) error {
	if req == nil {
		log.Error("HandleInitContent req nil")
		return errors.ErrInvalidInput
	}

	var rooms []*domain.Room
	var createRooms []*domain.Room
	var updateRooms []*domain.Room

	var hotel *domain.Hotel

	switch req.Provider {
	case commonEnum.HotelProviderExpedia:
		if req.ExpediaHotelContent == nil {
			log.Error("HandleInitContent req.ExpediaHotelContent nil")
			return errors.ErrInvalidInput
		}
		rooms, hotel = domain.MapExpediaHotelContentToRoomsAndHotel(req.ExpediaHotelContent, req.Language, req.ContentVersion)
	case commonEnum.HotelProviderRateHawk:
		if req.RateHawkContent == nil {
			log.Error("HandleInitContent req.RateHawkContent nil")
			return errors.ErrInvalidInput
		}
		rooms, hotel = domain.MapRateHawkHotelContentToRoomsAndHotel(req.RateHawkContent, req.Language)
	default:
		log.Error("HandleInitContent un-support provider")
		return domain.ErrNoProviderForOfficeID
	}

	if hotel == nil {
		return errors.ErrSomethingOccurred
	}

	hotelRecords, err := h.hotelRepo.FindByHotelIDV2(ctx, hotel.HotelID)
	if err != nil {
		log.Error("Find hotel error", log.Any("err", err))
		return err
	}

	var hotelRecord *domain.Hotel
	hotelProviders := map[commonEnum.HotelProvider]string{}

	for _, record := range hotelRecords {
		if record.Language == req.Language {
			hotelRecord = record
		}

		for key, providerID := range record.ProviderIds {
			hotelProviders[key] = providerID
		}
	}

	if len(hotelProviders) > 0 {
		hotel.ProviderIds = hotelProviders
	}

	isUpdate := false

	if hotelRecord != nil {
		isUpdate = true
		hotel.ID = hotelRecord.ID
	}

	roomIDs := []string{}
	for _, room := range rooms {
		roomIDs = append(roomIDs, room.RoomID)
	}

	roomRecords, err := h.roomRepo.FindByRoomIDs(ctx, roomIDs, req.Language)
	if err != nil {
		log.Error("Find Room error", log.Any("err", err))
		return err
	}

	if len(roomRecords) == 0 {
		createRooms = append(createRooms, rooms...)
	} else {
		for _, room := range roomRecords {
			for _, roomContent := range rooms {
				if room.RoomID == roomContent.RoomID {
					roomContent.ID = room.ID
					updateRooms = append(updateRooms, roomContent)
				}
			}
		}
	}

	if len(rooms) == 0 {
		hotel.Active = false
	}

	if isUpdate {
		err = h.retryUpdate(ctx, hotel, createRooms, updateRooms)
		if err != nil {
			log.Error("retryCreateHotel error", log.Any("err", err))
			return err
		}

		fmt.Println("Updated ", hotel.Name, fmt.Sprintf(" Created %d and update %d rooms", len(createRooms), len(updateRooms)))
	} else {
		err = h.retryCreateHotel(ctx, hotel, createRooms)
		if err != nil {
			log.Error("retryCreateHotel error", log.Any("err", err))
			return err
		}

		fmt.Println("Created ", hotel.Name, fmt.Sprintf(" Created %d rooms", len(createRooms)))
	}

	defer func() {
		if err != nil {
			switch req.Provider {
			case commonEnum.HotelProviderExpedia:
				if req.ExpediaHotelContent != nil {
					WriteErrorRecordForRetry(req.ExpediaHotelContent, fmt.Sprintf("./logs/expedia_content_err_%s", req.Language))
				}
			case commonEnum.HotelProviderRateHawk:
				if req.RateHawkContent != nil {
					WriteErrorRecordForRetry(req.RateHawkContent, fmt.Sprintf("./logs/rate_hawk_content_err_%s", req.Language))
				}
			}
		}
	}()

	return nil
}

func (h *aggregateHotelContentHandler) MigrateHotelThumbnail(ctx context.Context) {
	country := constants.HotelCountryCodes
	for _, code := range country {
		hotels, err := h.hotelRepo.FindAllByCountryCode(ctx, code)
		if err != nil {
			log.Error("h.hotelRepo.FindAllByCountryCode err", log.Any("err", err), log.Any("country", code))
			continue
		}

		log.Info("Get content hotel", log.Any("country", code), log.Any("total", len(hotels)))
		start := time.Now().UnixMilli()

		hotelMap := make(map[string][]*domain.Hotel, len(hotels))
		for _, hotel := range hotels {
			hotelMap[hotel.HotelID] = append(hotelMap[hotel.HotelID], hotel)
		}

		createdData := []*domain.DisplayImageContent{}
		for _, item := range hotelMap {
			if len(item) == 0 {
				continue
			}

			doc := item[0]

			var heroImage *domain.Image

			// Check if images array exists and is not empty
			if len(doc.Images) > 0 {
				// Find the first image with hero_image: true
				for _, img := range doc.Images {
					if img.HeroImage {
						heroImage = img
						break
					}
				}

				// If no hero image found, take the first image
				if heroImage == nil {
					heroImage = doc.Images[0]
				}

			}

			if heroImage != nil {
				imageLink := domain.HotelSummaryImageLink{
					Px70:   "",
					Px200:  "",
					Px350:  "",
					Px1000: "",
				}

				for key, value := range heroImage.Links {
					if value != nil {
						switch key {
						case "70px":
							imageLink.Px70 = value.Href
						case "350px":
							imageLink.Px350 = value.Href
						case "200px":
							imageLink.Px200 = value.Href
						case "1000px":
							imageLink.Px1000 = value.Href
						}
					}
				}

				createdData = append(createdData, &domain.DisplayImageContent{
					HotelID: doc.HotelID,
					Image: &domain.HotelSummaryImage{
						HeroImage: heroImage.HeroImage,
						Category:  heroImage.Category,
						Links:     imageLink,
						Caption:   heroImage.Caption,
					},
				})
			}
		}

		err = h.displayImageRepo.CreateBulk(ctx, createdData)
		if err != nil {
			log.Error("h.displayImageRepo.CreateBulk err", log.Any("err", err), log.Any("country", code))
			continue
		}

		log.Info("done", log.Any("country", code), log.Any("duration", time.Now().UnixMilli()-start))
	}

}

func (h *aggregateHotelContentHandler) SyncContent(ctx context.Context, from int) error {
	var wg sync.WaitGroup

	wg.Add(1)

	go func() {
		h.MigrateHotelThumbnail(ctx)

		defer wg.Done()

	}()

	wg.Wait()

	return nil
}

func (h *aggregateHotelContentHandler) retryCreateHotel(ctx context.Context, hotel *domain.Hotel, rooms []*domain.Room) error {
	var err error

	for i := 0; i < maxRetries; i++ {
		// re-generate ID
		hotel.ID = ""
		hotel.InitID()

		roomReference := []*domain.RoomRefInfo{}

		for _, room := range rooms {
			room.HotelRef = hotel.ID
			// re-generate ID
			room.ID = ""
			room.InitID()

			roomReference = append(roomReference, &domain.RoomRefInfo{
				ID:          room.ID,
				ProviderIds: room.GetProviderIDs(),
				RoomID:      room.RoomID,
			})
		}

		hotel.RoomReferences = roomReference

		err := h.hotelRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
			err = h.hotelRepo.Create(tnxSession, hotel)
			if err != nil {
				return nil, err
			}

			if len(rooms) > 0 {
				err = h.roomRepo.CreateMany(tnxSession, rooms, hotel.Language)
				if err != nil {
					return nil, err
				}
			}

			return nil, nil
		})
		if err == nil {
			return nil
		}

		log.Error("WithTransaction error", log.Any("err", err), log.String("hotelID", hotel.HotelID))
		time.Sleep(retryDelay)
	}

	if err != nil {
		return err
	}

	return nil
}

func (h *aggregateHotelContentHandler) retryUpdate(ctx context.Context, hotel *domain.Hotel, rooms, updateRooms []*domain.Room) error {
	var err error

	for i := 0; i < maxRetries; i++ {
		roomReference := []*domain.RoomRefInfo{}

		for _, room := range updateRooms {
			roomReference = append(roomReference, &domain.RoomRefInfo{
				ID:          room.ID,
				ProviderIds: room.GetProviderIDs(),
				RoomID:      room.RoomID,
			})
		}

		for _, room := range rooms {
			room.HotelRef = hotel.ID
			// re-generate ID
			room.ID = ""
			room.InitID()
			roomReference = append(roomReference, &domain.RoomRefInfo{
				ID:          room.ID,
				ProviderIds: room.GetProviderIDs(),
				RoomID:      room.RoomID,
			})
		}

		hotel.RoomReferences = roomReference

		err := h.hotelRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
			err = h.hotelRepo.Update(tnxSession, hotel)
			if err != nil {
				return nil, err
			}

			if len(rooms) > 0 {
				err = h.roomRepo.CreateMany(tnxSession, rooms, hotel.Language)
				if err != nil {
					return nil, err
				}
			}

			if len(updateRooms) > 0 {
				for _, updateRoom := range updateRooms {
					err = h.roomRepo.Update(tnxSession, updateRoom)
					if err != nil {
						return nil, err
					}
				}
			}

			return nil, nil
		})

		if err == nil {
			return nil
		}

		log.Error("WithTransaction error", log.Any("err", err), log.String("hotelID", hotel.HotelID))
		time.Sleep(retryDelay)
	}

	if err != nil {
		return err
	}

	return nil
}
