package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type CreateCurrencyExchangeHandler interface {
	Handle(ctx context.Context, req *domain.CurrencyExchange) error
}

type createCurrencyExchangeHandler struct {
	currencyExchangeRepo repositories.CurrencyExchangeRepository
}

func NewCreateCurrencyExchangeHandler(
	currencyExchangeRepo repositories.CurrencyExchangeRepository,
) CreateCurrencyExchangeHandler {
	return &createCurrencyExchangeHandler{
		currencyExchangeRepo: currencyExchangeRepo,
	}
}

func (h *createCurrencyExchangeHandler) Handle(ctx context.Context, req *domain.CurrencyExchange) error {
	if req == nil {
		log.Error("CreateCurrencyExchangeHandler.Handle error: request is nil")
		return commonErrors.ErrInvalidInput
	}

	currencyExchange, err := h.currencyExchangeRepo.FindOne(ctx, req)
	if err != nil {
		log.Error("currencyExchangeRepo.FindOne error", log.Any("error", err))
		return commonErrors.ErrSomethingOccurred
	}

	if currencyExchange != nil {
		return domain.ErrDuplicateCurrencyExchange
	}

	err = h.currencyExchangeRepo.Create(ctx, req)
	if err != nil {
		log.Error("currencyExchangeRepo.Create error", log.Any("error", err))
		return errors.ErrSomethingOccurred
	}

	return nil
}
