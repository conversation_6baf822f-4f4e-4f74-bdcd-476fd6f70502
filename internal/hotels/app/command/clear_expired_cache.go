package command

import (
	"context"

	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
)

type ClearExpiredCacheHandler interface {
	Handle(ctx context.Context) error
}

type clearExpiredCacheHandler struct {
	repo repositories.SearchHotelCachesRepository
}

func NewClearExpiredCacheHandler(repo repositories.SearchHotelCachesRepository) ClearExpiredCacheHandler {
	return &clearExpiredCacheHandler{repo: repo}
}

func (h *clearExpiredCacheHandler) Handle(ctx context.Context) error {
	err := h.repo.ClearExpiredHotelRecords(ctx)
	if err != nil {
		log.Error("ClearExpiredHotelRecords err", log.Any("err", err))
	}

	return err
}
