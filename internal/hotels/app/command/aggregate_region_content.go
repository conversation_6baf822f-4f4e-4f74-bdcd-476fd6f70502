package command

import (
	"context"
	"time"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type AggregateRegionContentHandler interface {
	Handle(ctx context.Context, req *AggregateRegionContentReq) error
}

type aggregateRegionContentHandler struct {
	regionRepo repositories.RegionRepository
}

type AggregateRegionContentReq struct {
	ContentVersion  string
	Provider        commonEnum.HotelProvider
	PrefixNumber    int
	Language        string
	RateHawkContent *domain.RateHawkRegionContent
}

func NewAggregateRegionContentHandler(
	regionRepo repositories.RegionRepository,
) AggregateRegionContentHandler {
	return &aggregateRegionContentHandler{regionRepo}
}

func (h *aggregateRegionContentHandler) Handle(ctx context.Context, req *AggregateRegionContentReq) error {
	switch req.Provider {
	case commonEnum.HotelProviderRateHawk:
		return h.HandleInitContent(ctx, req)
	}

	return nil
}

func (h *aggregateRegionContentHandler) HandleInitContent(ctx context.Context, req *AggregateRegionContentReq) error {
	if req == nil {
		log.Error("HandleInitContent req nil")
		return errors.ErrInvalidInput
	}

	var region *domain.Region

	switch req.Provider {
	case commonEnum.HotelProviderRateHawk:
		if req.RateHawkContent == nil {
			log.Error("HandleInitContent req.RateHawkContent nil")
			return errors.ErrInvalidInput
		}
		region = domain.MapRateHawkRegionContentToRegion(req.RateHawkContent, req.Language)
	default:
		log.Error("HandleInitContent un-support provider")
		return domain.ErrNoProviderForOfficeID
	}

	if region == nil {
		return errors.ErrSomethingOccurred
	}

	hotelRecord, err := h.regionRepo.FindByRegionID(ctx, region.RegionID, req.Language)
	if err != nil {
		log.Error("Find region error", log.Any("err", err))
		return err
	}

	if hotelRecord != nil {
		return nil
	}

	err = h.retryCreateRegion(ctx, region)
	if err != nil {
		log.Error("retryCreateRegion error", log.Any("err", err))
		return err
	}

	return nil
}

func (h *aggregateRegionContentHandler) retryCreateRegion(ctx context.Context, region *domain.Region) error {
	var err error

	for i := 0; i < maxRetries; i++ {
		// re-generate ID
		region.ID = ""
		region.InitID()

		err := h.regionRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
			err = h.regionRepo.Create(tnxSession, region, region.Language)
			if err != nil {
				return nil, err
			}

			return nil, nil
		})
		if err == nil {
			return nil
		}

		log.Error("WithTransaction error", log.Any("err", err), log.String("region", region.ID))
		time.Sleep(retryDelay)
	}

	return nil
}
