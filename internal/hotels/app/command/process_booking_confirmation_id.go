package command

import (
	"context"

	"github.com/gammazero/workerpool"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/models"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type ProcessBookingConfirmationIDHandler interface {
	Handle(ctx context.Context) error
}

type processBookingConfirmationIDHandler struct {
	orderRepo      repositories.OrderRepository
	sessionRepo    repositories.SessionRepository
	bookingService service.BookingService
}

type ProcessBookingConfirmationIDRequest struct {
	Bookings []*domain.HubHotelOrder
}

func NewProcessBookingConfirmationIDHandler(
	orderRepo repositories.OrderRepository,
	sessionRepo repositories.SessionRepository,
	bookingService service.BookingService,
) ProcessBookingConfirmationIDHandler {
	return &processBookingConfirmationIDHandler{orderRepo, sessionRepo, bookingService}
}

const maxWorkerPool = 2

func (h *processBookingConfirmationIDHandler) Handle(ctx context.Context) error {
	wp := workerpool.New(maxWorkerPool)

	cursor, err := h.orderRepo.ListBookingWithoutHotelConfirmationID(ctx)
	if err != nil {
		log.Error("h.orderRepo.ListBookingWithoutConfirmationID err", log.Any("err", err))
		return err
	}

	defer cursor.Close(ctx)

	count := 0
	for cursor.Next(ctx) {
		var booking *models.HubHotelOrder

		err := cursor.Decode(&booking)
		if err != nil {
			log.Error("Decode err", log.Any("err", err))
			return err
		}

		wp.Submit(func() {
			bgCtx, cc := context.WithTimeout(ctx, constants.LongThirdPartyRequestTimeout)
			defer cc()

			session, err := h.sessionRepo.FindBySessionIDWithoutExpire(ctx, booking.OfficeID, booking.SessionID)
			if err != nil {
				log.Error("sessionRepo.FindBySessionID error", log.Any("error", err),
					log.String("officeID", booking.OfficeID), log.String("SessionID", booking.SessionID))
				return
			}

			err = h.bookingService.FetchBookConfirmationID(bgCtx, converts.ToDomainHubHotelOrder(booking), session)
			if err != nil {
				log.Error("ResolveBookingConfirmationID error", log.Any("error", err), log.Any("booking", booking))
			}

			log.Info("Process booking", log.Any("bookingCode", booking.OrderCode))
			count++
		})
	}

	wp.StopWait()
	log.Info("ProcessBookingConfirmationID done", log.Any("count", count))
	return nil
}
