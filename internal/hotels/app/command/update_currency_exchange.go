package command

import (
	"context"

	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type UpdateCurrencyExchangeHandler interface {
	Handle(ctx context.Context, req *domain.CurrencyExchange) error
}

type updateCurrencyExchangeHandler struct {
	currencyExchangeRepo repositories.CurrencyExchangeRepository
}

func NewUpdateCurrencyExchangeHandler(
	currencyExchangeRepo repositories.CurrencyExchangeRepository,
) UpdateCurrencyExchangeHandler {
	return &updateCurrencyExchangeHandler{
		currencyExchangeRepo: currencyExchangeRepo,
	}
}

func (h *updateCurrencyExchangeHandler) Handle(ctx context.Context, req *domain.CurrencyExchange) error {
	if req == nil {
		log.Error("UpdateCurrencyExchangeHandler.Handle error: request is nil")
		return commonErrors.ErrInvalidInput
	}

	currencyExchange, err := h.currencyExchangeRepo.FindOne(ctx, req)
	if err != nil {
		log.Error("currencyExchangeRepo.FindOne error", log.Any("error", err))
		return commonErrors.ErrSomethingOccurred
	}

	if currencyExchange != nil && currencyExchange.ID != req.ID {
		log.Error("UpdateCurrencyExchangeHandler.Handle error: duplicate currency exchange", log.String("currency", req.ID))
		return domain.ErrDuplicateCurrencyExchange
	}

	err = h.currencyExchangeRepo.Update(ctx, req)
	if err != nil {
		log.Error("currencyExchangeRepo.Update error", log.Any("error", err))
		return commonErrors.ErrSomethingOccurred
	}

	return nil
}
