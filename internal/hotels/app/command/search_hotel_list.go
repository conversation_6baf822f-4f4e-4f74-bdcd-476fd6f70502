package command

import (
	"context"

	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type SearchHotelListHandler interface {
	Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error)
}

type searchHotelListHandler struct {
	cfg           *config.Schema
	searchService service.SearchHotelService
	currencyExSvc service.CurrencyExchangeService
}

func NewSearchHotelListHandler(
	cfg *config.Schema,
	searchService service.SearchHotelService,
	currencyExSvc service.CurrencyExchangeService,
) SearchHotelListHandler {
	return &searchHotelListHandler{cfg, searchService, currencyExSvc}
}

func (h *searchHotelListHandler) Handle(ctx context.Context, req *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	if len(req.HotelIds) == 0 {
		return &domain.HubSearchHotelResponse{
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			HotelSummary:             []*domain.HotelSummary{},
			Occupancies:              req.Occupancies,
			Pagination:               req.Pagination,
		}, nil
	}

	if req.SaleChannel == "" {
		req.SaleChannel = enum.SalesChannelAgentTool
	}

	if req.TravelPurpose == "" {
		req.TravelPurpose = enum.TravelPurposeLeisure
	}

	if req.SalesEnv == "" {
		req.SalesEnv = enum.SalesEnvironmentHotelPackage
	}

	if req.RequestCurrency != "" && req.RequestCurrency != constants.VNDCurrency {
		if err := h.currencyExSvc.ValidateCurrency(ctx, req.RequestCurrency); err != nil {
			log.Error("h.currencyExSvc.ValidateCurrency err", log.Any("err", err), log.Any("currency", req.RequestCurrency))
			return nil, err
		}
	}

	hotelIdSeen := make(map[string]bool)

	req.HotelIds = lo.Filter(req.HotelIds, func(item string, index int) bool {
		if item == "" || hotelIdSeen[item] {
			return false
		}

		hotelIdSeen[item] = true

		return true
	})

	if len(req.HotelIds) > constants.HotelIDSearchLimit {
		return nil, domain.ErrExceedHotelIDsLimit
	}

	if err := req.Stay.CountDays(); err != nil {
		log.Error("SearchHotelPrice stay.DayCount err", log.Any("err", err), log.Any("stay.DayCount", req.Stay.CountDays))
		return nil, err
	}

	req.Stay.RoomCount = req.CountRooms()

	enableProvider := []commonEnum.HotelProvider{
		commonEnum.HotelProviderNone,
	}

	enableProvider = append(enableProvider, req.EnableProviders...)

	if req.ImageMode == enum.ImageModeTypeNone {
		req.ImageMode = enum.ImageModeTypeFull
	}

	// Search
	res, loadMore, err := h.searchService.SearchList(ctx, req, enableProvider)
	if err != nil {
		return nil, err
	}

	if res == nil {
		return &domain.HubSearchHotelResponse{
			ErrorRes: domain.ErrorRes{
				IsSuccess: true,
			},
			SearchHotelFilterRequest: &domain.SearchHotelFilterResponse{},
			Pagination:               req.Pagination,
			HotelSummary:             []*domain.HotelSummary{},
			Occupancies:              req.Occupancies,
		}, nil
	}

	if req.RequestCurrency != "" && req.RequestCurrency != constants.VNDCurrency {
		var exRateItem *domain.CurrencyExchange

		for _, hotel := range res {
			var err error
			var rate float64

			if exRateItem != nil && exRateItem.Rate != 0 && exRateItem.From == hotel.Price.Currency {
				rate = exRateItem.Rate
			}

			hotel.Price, exRateItem, err = h.currencyExSvc.ConvertHotelSummaryPrice(ctx, hotel.Price, req.RequestCurrency, rate)
			if err != nil {
				log.Error("currencyExSvc.ConvertHotelSummaryPrice err", log.Any("err", err))
			}
		}
	}

	totalCounts := req.Pagination.TotalRecord

	var filter *domain.SearchHotelFilterResponse
	sortedList := res

	if !req.ExcludeContent {
		filter = h.searchService.CalcFilterRequest(ctx, res)

		filterList := h.searchService.ApplyFilterRequest(ctx, res, req.SearchHotelFilterRequest)

		sortedList = h.searchService.ApplySortRequest(ctx, req, filterList)
	}

	pagedData := h.searchService.ApplyPaging(ctx, sortedList, req.Pagination)

	pagedDataWithHiddenFee := helpers.Copy(pagedData).([]*domain.HotelSummary)

	// re-gen key no need to check error
	searchKey, _ := req.GenSearchKey()
	result := &domain.HubSearchHotelResponse{
		ErrorRes: domain.ErrorRes{
			IsSuccess: true,
		},
		SearchHotelFilterRequest: filter,
		Pagination:               req.Pagination,
		HotelSummary:             pagedDataWithHiddenFee,
		Occupancies:              req.Occupancies,
	}

	result.SearchKey = searchKey

	if loadMore && result.Pagination.PageCurrent == result.Pagination.TotalPage {
		if totalCounts == 0 || totalCounts <= req.Pagination.PageLimit {
			totalCounts = constants.MaxHotelIds
		}

		result.Pagination.TotalPage = totalCounts / req.Pagination.PageLimit
		result.Pagination.TotalRecord = totalCounts
	}

	return result, nil
}
