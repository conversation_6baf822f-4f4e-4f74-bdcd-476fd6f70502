package service

import (
	"context"

	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	mongoRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	redisRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	serviceConvert "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type CurrencyExchangeService interface {
	GetRateMapping(ctx context.Context, from string) (domain.CurrencyRateMapping, error)
	ConvertCurrency(ctx context.Context, req *domain.HubCheckAvailabilityReq, provider commonEnum.HotelProvider, target []*domain.HubHotel, currency string) ([]*domain.HubHotel, error)
	ConvertHubRateDataCurrency(ctx context.Context, target *domain.HubRateData, provider commonEnum.HotelProvider, reqCurrency, currency string) (*domain.HubRateData, *domain.CurrencyExchange, error)
	ConvertHotelSummaryPrice(ctx context.Context, target *domain.Price, currency string, exchangeRate float64) (*domain.Price, *domain.CurrencyExchange, error)
	ConvertFilterPrice(ctx context.Context, target *domain.SearchHotelFilterResponse, currency string) (*domain.SearchHotelFilterResponse, *domain.CurrencyExchange, error)
	ValidateCurrency(ctx context.Context, requestCurrency string) error
}

type currencyExchangeService struct {
	repo      mongoRepo.CurrencyExchangeRepository
	redisRepo redisRepo.CurrencyExchangeRepository
}

func NewCurrencyExchangeService(
	repo mongoRepo.CurrencyExchangeRepository,
	redisRepo redisRepo.CurrencyExchangeRepository,
) CurrencyExchangeService {
	return &currencyExchangeService{
		repo,
		redisRepo,
	}
}

func (r *currencyExchangeService) GetRateMapping(ctx context.Context, from string) (domain.CurrencyRateMapping, error) {
	var (
		exList []*domain.CurrencyExchange
		err    error
	)
	// Note: Remove redis cache
	// exList, err = r.redisRepo.Get(ctx, from)
	// if err != nil {
	// 	log.Error("redisRepo.Get", log.Any("error", err), log.String("from", from))
	// 	return nil, errors.ErrSomethingOccurred
	// }

	if len(exList) == 0 {
		exList, err = r.repo.Find(ctx, from)
		if err != nil {
			log.Error("repo.Find error", log.Any("error", err), log.String("from", from))
			return nil, errors.ErrSomethingOccurred
		}

		// err := r.redisRepo.Set(ctx, from, exList)
		// if err != nil {
		// 	log.Error("redisRepo.Set", log.Any("error", err), log.Any("exList", exList))
		// }
	}

	out := map[string]float64{}

	for _, item := range exList {
		out[item.To] = item.Rate
	}

	if len(out) == 0 {
		return nil, errors.ErrNotFound
	}

	return out, nil
}

func (r *currencyExchangeService) ValidateCurrency(ctx context.Context, requestCurrency string) error {
	if requestCurrency != "" && requestCurrency != constants.VNDCurrency {
		data, err := r.repo.FindCurrencyFromTo(ctx, requestCurrency, constants.VNDCurrency)
		if err != nil {
			log.Error("h.currencyRepo.FindCurrencyFromTo err", log.Any("err", err), log.Any(" requestCurrency", requestCurrency))
			return errors.ErrSomethingOccurred
		}

		if data == nil {
			return domain.ErrCurrencyNotSupport
		}
	}

	return nil
}

func (r *currencyExchangeService) ConvertCurrency(ctx context.Context, req *domain.HubCheckAvailabilityReq, provider commonEnum.HotelProvider, target []*domain.HubHotel, currency string) ([]*domain.HubHotel, error) {
	if len(target) == 0 {
		return target, nil
	}

	currentCurrency := target[0].Currency
	shouldFilter := false

	if currentCurrency == currency {
		return target, nil
	}

	result := helpers.Copy(target).([]*domain.HubHotel)

	var err error

	for _, hotel := range result {
		hotel.Currency = currency
		for _, room := range hotel.ListRooms {
			for index, rate := range room.RateData {
				if rate.Currency == currency {
					continue
				}

				room.RateData[index], _, err = r.ConvertHubRateDataCurrency(ctx, rate, provider, currentCurrency, currency)
				if err != nil {
					log.Error("ConvertHubRateDataCurrency  err", log.Any("err", err))
					shouldFilter = true
				}
			}
		}
	}

	if shouldFilter {
		result = lo.Filter[*domain.HubHotel](result, func(item *domain.HubHotel, index int) bool {
			return item.Currency != ""
		})
	}

	return result, nil
}

func (r *currencyExchangeService) ConvertHubRateDataCurrency(ctx context.Context, target *domain.HubRateData, provider commonEnum.HotelProvider, reqCurrency, currency string) (*domain.HubRateData, *domain.CurrencyExchange, error) {
	if target == nil {
		return target, nil, nil
	}

	if reqCurrency == currency {
		return target, &domain.CurrencyExchange{
			From: reqCurrency,
			To:   currency,
			Rate: 1,
		}, nil
	}

	exchangeRateMap, err := r.GetRateMapping(ctx, reqCurrency)
	if err != nil {
		log.Error("currencyExSvc.GetRateMapping error", log.Any("error", err), log.String("reqCurrency", reqCurrency))
		return nil, nil, err
	}

	if exchangeRateMap == nil {
		log.Error("exchangeRateMap empty")
		return nil, nil, domain.ErrCurrencyNotSupport
	}

	result := helpers.Copy(target).(*domain.HubRateData)

	exchangeRate := exchangeRateMap[currency]
	if exchangeRate == 0 {
		log.Error("exchangeRateMap not support", log.String("currency", currency), log.Any("rateMap", exchangeRateMap))
		return nil, nil, domain.ErrCurrencyNotSupport
	}

	result.Currency = currency

	precision := constants.GetCurrencyPrecision(currency)

	for _, occupancyRate := range result.OccupancyRate {
		for _, night := range occupancyRate.TotalNightlyRate {
			night.RateAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*night.RateAmount, 6), precision)
			night.RateBasic = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*night.RateBasic, 6), precision)
			night.TaxAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*night.TaxAmount, 6), precision)
			night.Currency = currency
		}

		// https://www.notion.so/deeptechjsc/90e80363855a4bbaafd1b106ecabad75?v=a445560271bf4d0d99bceb3f4be8f311&p=35ff2ebe1a8b4a35bdb4f5095355c9ee&pm=s
		// if provider == commonEnum.HotelProviderExpedia {
		// 	for key, item := range occupancyRate.PayAtHotel {
		// 		occupancyRate.PayAtHotel[key] = domain.PayAtHotel{
		// 			Amount:      helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*item.Amount, 6), precision),
		// 			Description: item.Description,
		// 			Currency:    currency,
		// 		}
		// 	}
		// }

		for _, discount := range occupancyRate.RateDiscounts {
			discount.Amount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*discount.Amount, 6), precision)
			discount.Currency = currency
		}

		for _, tax := range occupancyRate.RateTaxes {
			if tax.Amount == nil {
				continue
			}

			amount := *tax.Amount
			amount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*amount, 6), precision)

			tax.Amount = &amount
			tax.Currency = currency
		}

		occupancyRate.Surcharges = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*occupancyRate.Surcharges, 6), precision)
	}

	result.TotalRateAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.TotalRateAmount, 6), precision)
	result.TotalTaxAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.TotalTaxAmount, 6), precision)
	result.TotalRateBasic = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.TotalRateBasic, 6), precision)
	result.Currency = currency

	serviceConvert.CalcRatePayNow(result, provider)

	for _, policy := range result.CancelPolicies {
		policy.RawPenaltyAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*(policy.PenaltyAmount), 6), precision)
		policy.PenaltyAmount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*(policy.PenaltyAmount), 6), precision)
		policy.PenaltyInfo.Amount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*(policy.PenaltyInfo.Amount), 6), precision)

		if !policy.Refundable {
			policy.PenaltyAmount = result.PayNow
		}

		policy.Currency = currency
	}

	return result, &domain.CurrencyExchange{
		From: reqCurrency,
		To:   currency,
		Rate: exchangeRate,
	}, nil
}

func (r *currencyExchangeService) ConvertHotelSummaryPrice(ctx context.Context, target *domain.Price, currency string, exchangeRate float64) (*domain.Price, *domain.CurrencyExchange, error) {
	if target == nil {
		return target, nil, nil
	}

	currentCurrency := target.Currency
	if currentCurrency == currency {
		return target, &domain.CurrencyExchange{
			From: currentCurrency,
			To:   currency,
			Rate: 1,
		}, nil
	}

	if exchangeRate == 0 {
		exchangeRateMap, err := r.GetRateMapping(ctx, currentCurrency)
		if err != nil {
			log.Error("currencyExSvc.GetRateMapping error", log.Any("error", err), log.String("reqCurrency", currentCurrency))
			return nil, nil, err
		}

		if exchangeRateMap == nil {
			log.Error("exchangeRateMap empty")
			return nil, nil, domain.ErrCurrencyNotSupport
		}

		exchangeRate = exchangeRateMap[currency]
		if exchangeRate == 0 {
			log.Error("exchangeRateMap not support", log.String("currency", currency), log.Any("rateMap", exchangeRateMap))
			return nil, nil, domain.ErrCurrencyNotSupport
		}
	}

	result := helpers.Copy(target).(*domain.Price)

	result.Currency = currency

	precision := constants.GetCurrencyPrecision(currency)

	result.Total = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.Total, 6), precision)
	if result.PricePerNight != nil {
		result.PricePerNight.DiscountPrice = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.PricePerNight.DiscountPrice, 6), precision)
		result.PricePerNight.OriginalPrice = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.PricePerNight.OriginalPrice, 6), precision)
	}

	// if len(result.PayAtHotel) > 0 {
	// 	totalPayAtHotel := 0.0
	// 	for _, r := range result.PayAtHotel {
	// 		r.Amount = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*r.Amount, 6), precision)
	// 		r.Currency = currency
	// 		totalPayAtHotel += r.Amount
	// 	}

	// 	result.TotalPayAtHotel = totalPayAtHotel
	// }

	result.Currency = currency

	return result, &domain.CurrencyExchange{
		From: currentCurrency,
		To:   currency,
		Rate: exchangeRate,
	}, nil
}

func (r *currencyExchangeService) ConvertFilterPrice(ctx context.Context, target *domain.SearchHotelFilterResponse, currency string) (*domain.SearchHotelFilterResponse, *domain.CurrencyExchange, error) {
	if target == nil {
		return target, nil, nil
	}

	currentCurrency := target.Currency

	if currentCurrency == "" {
		return target, nil, nil
	}

	if currentCurrency == currency {
		return target, &domain.CurrencyExchange{
			From: currentCurrency,
			To:   currency,
			Rate: 1,
		}, nil
	}

	exchangeRateMap, err := r.GetRateMapping(ctx, currentCurrency)
	if err != nil {
		log.Error("currencyExSvc.GetRateMapping error", log.Any("error", err), log.String("reqCurrency", currentCurrency))
		return nil, nil, err
	}

	if exchangeRateMap == nil {
		log.Error("exchangeRateMap empty")
		return nil, nil, domain.ErrCurrencyNotSupport
	}

	result := helpers.Copy(target).(*domain.SearchHotelFilterResponse)

	exchangeRate := exchangeRateMap[currency]
	if exchangeRate == 0 {
		log.Error("exchangeRateMap not support", log.String("currency", currency), log.Any("rateMap", exchangeRateMap))
		return nil, nil, domain.ErrCurrencyNotSupport
	}

	result.Currency = currency

	precision := constants.GetCurrencyPrecision(currency)

	result.MaxPrice = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.MaxPrice, 6), precision)
	result.MinPrice = helpers.CeilToPrecision(helpers.RoundToPrecision(exchangeRate*result.MinPrice, 6), precision)

	result.Currency = currency

	return result, &domain.CurrencyExchange{
		From: currentCurrency,
		To:   currency,
		Rate: exchangeRate,
	}, nil
}
