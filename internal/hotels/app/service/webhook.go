package service

import (
	"context"

	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/btm_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type WebhookService interface {
	BTMWebhookNotification(ctx context.Context, payload *domain.ExpediaWebhookNotificationRequest) error
}

type webhookService struct {
	cfg          *config.Schema
	hubOrderRepo repositories.OrderRepository
	btmAdapter   btm_client.BTMAdapter
}

func NewWebhookService(
	cfg *config.Schema,
	hubOrderRepo repositories.OrderRepository,
	btmAdapter btm_client.BTMAdapter,
) WebhookService {
	return &webhookService{
		cfg:          cfg,
		hubOrderRepo: hubOrderRepo,
		btmAdapter:   btmAdapter,
	}
}

func (s *webhookService) BTMWebhookNotification(ctx context.Context, payload *domain.ExpediaWebhookNotificationRequest) error {
	if payload == nil {
		return commonErrors.ErrInvalidInput
	}

	err := s.btmAdapter.SendWebhookNotification(ctx, payload)
	if err != nil {
		return err
	}

	return nil
}
