package service

import (
	"context"
	"errors"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type expediaSearchHandler struct {
	adapter expedia_client.ExpediaAdapter
}

type tourmindSearchHandler struct {
	adapter tourmind_client.TourmindAdapter
}

type rateHawkSearchHandler struct {
	adapter rate_hawk_client.RateHawkAdapter
}

type didaSearchHandler struct {
	adapter dida.Adapter
}

type hnhTASearchHandler struct {
	adapter travel_agent_client.TravelAgentAdapter
}

type mayTASearchHandler struct {
	adapter travel_agent_client.TravelAgentAdapter
}

type bztTASearchHandler struct {
	adapter travel_agent_client.TravelAgentAdapter
}

type agodaSearchHandler struct {
	adapter agoda_client.AgodaAdapter
}

type ProviderSearchHandler interface {
	search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error)
}

type ProviderSearchHandlerImp struct {
	expediaSearchHandler
	tourmindSearchHandler
	rateHawkSearchHandler
	didaSearchHandler
	hnhTASearchHandler
	mayTASearchHandler
	agodaSearchHandler
}

func NewProviderSearchHandler(
	expediaAdapter expedia_client.ExpediaAdapter,
	tourmindAdapter tourmind_client.TourmindAdapter,
	rateHawkAdapter rate_hawk_client.RateHawkAdapter,
	didaAdapter dida.Adapter,
	hnhTravelAgentAdapter travel_agent_client.TravelAgentAdapter,
	agodaAdapter agoda_client.AgodaAdapter,
) *ProviderSearchHandlerImp {
	return &ProviderSearchHandlerImp{
		expediaSearchHandler: expediaSearchHandler{
			adapter: expediaAdapter,
		},
		tourmindSearchHandler: tourmindSearchHandler{
			adapter: tourmindAdapter,
		},
		rateHawkSearchHandler: rateHawkSearchHandler{
			adapter: rateHawkAdapter,
		},
		didaSearchHandler: didaSearchHandler{
			adapter: didaAdapter,
		},
		hnhTASearchHandler: hnhTASearchHandler{
			adapter: hnhTravelAgentAdapter,
		},
		mayTASearchHandler: mayTASearchHandler{
			adapter: hnhTravelAgentAdapter,
		},
		agodaSearchHandler: agodaSearchHandler{
			adapter: agodaAdapter,
		},
	}
}

func (h *ProviderSearchHandlerImp) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	log.Error("not implemented handler for provider")
	return nil, domain.ErrProviderNotAllowed
}

func (h *ProviderSearchHandlerImp) getHandler(p commonEnum.HotelProvider) ProviderSearchHandler {
	switch p {
	case commonEnum.HotelProviderExpedia:
		return &h.expediaSearchHandler
	case commonEnum.HotelProviderTourMind:
		return &h.tourmindSearchHandler
	case commonEnum.HotelProviderRateHawk:
		return &h.rateHawkSearchHandler
	case commonEnum.HotelProviderDida:
		return &h.didaSearchHandler
	case commonEnum.HotelProviderHNHTravelAgent:
		return &h.hnhTASearchHandler
	case commonEnum.HotelProviderMayTravelAgent:
		return &h.mayTASearchHandler
	case commonEnum.HotelProviderAgoda:
		return &h.agodaSearchHandler
	}

	return h
}

func (h *hnhTASearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	hotels, err := h.adapter.SearchHotel(ctx, data, commonEnum.HotelProviderHNHTravelAgent, searchKey)
	if err != nil {
		log.Error("hnhTASearchHandler error", log.Any("error", err), log.Any("searchKey", searchKey))
		return nil, err
	}

	return &domain.HotelSearchResult{
		Provider:           commonEnum.HotelProviderHNHTravelAgent,
		ExpireAt:           time.Now().Add(constants.DefaultExpireTime).UnixMilli(),
		Hotels:             hotels,
		HotelSearchRequest: data.SearchReq.ToHotelSearchRequest(),
	}, nil
}

func (h *mayTASearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	hotels, err := h.adapter.SearchHotel(ctx, data, commonEnum.HotelProviderMayTravelAgent, searchKey)
	if err != nil {
		log.Error("mayTASearchHandler error", log.Any("error", err), log.Any("searchKey", searchKey))
		return nil, err
	}

	return &domain.HotelSearchResult{
		Provider:           commonEnum.HotelProviderMayTravelAgent,
		ExpireAt:           time.Now().Add(constants.DefaultExpireTime).UnixMilli(),
		Hotels:             hotels,
		HotelSearchRequest: data.SearchReq.ToHotelSearchRequest(),
	}, nil
}

func (h *rateHawkSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	if data.Hotels == nil || data.ProviderHotelIds == nil {
		return nil, errors.ErrUnsupported
	}

	hotel := data.Hotels[0]

	adapterReq := &domain.CheckAvaiAdapterReq{
		HubRequest:      data.SearchReq,
		ProviderHotelID: data.ProviderHotelIds[0],
		ContentRooms:    hotel.Rooms,
		Address:         hotel.Address,
		TracingID:       searchKey,
	}

	rooms, currency, err := h.adapter.CheckAvailability(ctx, adapterReq)
	if err != nil {
		log.Error("tourmindSearchHandler error", log.Any("error", err), log.Any("searchKey", searchKey), log.Any("searchKey", searchKey))
		return nil, err
	}

	out := &domain.HubHotel{
		Address:         hotel.Address,
		HotelID:         hotel.HotelID,
		ProviderHotelID: data.ProviderHotelIds[0],
		Name:            hotel.Name,
		VAT:             true,
		ListRooms:       rooms,
		Currency:        currency,
	}

	return &domain.HotelSearchResult{
		SearchKey: searchKey,
		Provider:  commonEnum.HotelProviderRateHawk,
		Hotels:    []*domain.HubHotel{out},
		ExpireAt:  time.Now().Add(time.Minute * 5).UnixMilli(),
	}, nil
}

func (h *tourmindSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	hotels, err := h.adapter.SearchHotel(ctx, data, searchKey)
	if err != nil {
		log.Error("tourmindSearchHandler error", log.Any("error", err), log.Any("searchKey", searchKey), log.Any("searchKey", searchKey))
		return nil, err
	}

	return hotels, nil
}

func (h *expediaSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	hotels, err := h.adapter.SearchHotel(ctx, data, searchKey)
	if err != nil {
		log.Error("expediaSearchHandler error", log.Any("error", err), log.Any("searchKey", searchKey), log.Any("searchKey", searchKey))
		return nil, err
	}

	return hotels, nil
}

func (h *didaSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	if data.Hotels == nil || data.ProviderHotelIds == nil {
		return nil, errors.ErrUnsupported
	}

	hotel := data.Hotels[0]

	adapterReq := &domain.CheckAvaiAdapterReq{
		HubRequest:      data.SearchReq,
		ProviderHotelID: data.ProviderHotelIds[0],
		ContentRooms:    hotel.Rooms,
		Address:         hotel.Address,
		TracingID:       searchKey,
	}

	rooms, currency, err := h.adapter.CheckAvailability(ctx, adapterReq)
	if err != nil {
		log.Error("tourmindSearchHandler error", log.Any("error", err), log.Any("searchKey", searchKey), log.Any("searchKey", searchKey))
		return nil, err
	}

	out := &domain.HubHotel{
		Address:         hotel.Address,
		HotelID:         hotel.HotelID,
		ProviderHotelID: data.ProviderHotelIds[0],
		Name:            hotel.Name,
		VAT:             true,
		ListRooms:       rooms,
		Currency:        currency,
	}

	return &domain.HotelSearchResult{
		SearchKey: searchKey,
		Provider:  commonEnum.HotelProviderDida,
		Hotels:    []*domain.HubHotel{out},
		ExpireAt:  time.Now().Add(time.Minute * 5).UnixMilli(),
	}, nil
}

func (h *agodaSearchHandler) search(ctx context.Context, data *domain.SearchHotelRequestData, searchKey string) (*domain.HotelSearchResult, error) {
	if data.Hotels == nil || data.ProviderHotelIds == nil {
		return nil, errors.ErrUnsupported
	}

	hotel := data.Hotels[0]

	adapterReq := &domain.CheckAvaiAdapterReq{
		HubRequest:      data.SearchReq,
		ProviderHotelID: data.ProviderHotelIds[0],
		ContentRooms:    hotel.Rooms,
		Address:         hotel.Address,
		TracingID:       searchKey,
	}

	rooms, currency, searchID, err := h.adapter.CheckAvailability(ctx, adapterReq)
	if err != nil {
		log.Error("tourmindSearchHandler error", log.Any("error", err), log.Any("data", data), log.Any("searchKey", searchKey))
		return nil, err
	}

	out := &domain.HubHotel{
		Address:         hotel.Address,
		HotelID:         hotel.HotelID,
		ProviderHotelID: data.ProviderHotelIds[0],
		Name:            hotel.Name,
		VAT:             true,
		ListRooms:       rooms,
		Currency:        currency,
	}

	return &domain.HotelSearchResult{
		SearchKey:     searchKey,
		Provider:      commonEnum.HotelProviderDida,
		Hotels:        []*domain.HubHotel{out},
		ExpireAt:      time.Now().Add(time.Minute * 5).UnixMilli(),
		AgodaSearchID: searchID,
	}, nil
}
