package converts

import (
	"fmt"
	"strings"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func processName(name string) string {
	name = strings.ToUpper(name)
	return helpers.RemoveAccents(name)
}

func mapRoomConfirmationIDExpedia(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	confirmationMap := map[string][]*domain.HubRetrieveConfirmationID{}

	for _, confirmID := range confirmationIDs {
		confirmationKey := fmt.Sprintf("%s-%s", confirmID.ProviderRoomID, confirmID.OccupancyType)
		if len(confirmationMap[confirmationKey]) == 0 {
			confirmationMap[confirmationKey] = []*domain.HubRetrieveConfirmationID{}
		}

		confirmationMap[confirmationKey] = append(confirmationMap[confirmationKey], confirmID)
	}

	for _, room := range rooms {
		confirmationKey := fmt.Sprintf("%s-%s", room.ProviderRoomID, room.OccupancyType)

		confirmID, ok := confirmationMap[confirmationKey]
		if !ok {
			continue
		}

		if len(confirmID) == 0 {
			continue
		}

		selectedConfirm := confirmID[0]

		if checkNames(room, selectedConfirm) {
			room.ConfirmationID = selectedConfirm.ConfirmationID
			room.HotelConfirmationID = selectedConfirm.HotelConfirmationID
			room.BookingStatus = selectedConfirm.BookStatus

			if len(confirmationMap[confirmationKey]) > 0 {
				confirmationMap[confirmationKey] = confirmationMap[confirmationKey][1:]
			}
		}
	}
}

func MapRoomConfirmationIDRateHawk(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	confirmationMap := map[string][]*domain.HubRetrieveConfirmationID{}

	for _, confirmID := range confirmationIDs {
		confirmationKey := fmt.Sprintf("%s-%s", confirmID.ProviderRoomID, confirmID.OccupancyType)
		if len(confirmationMap[confirmationKey]) == 0 {
			confirmationMap[confirmationKey] = []*domain.HubRetrieveConfirmationID{}
		}

		confirmationMap[confirmationKey] = append(confirmationMap[confirmationKey], confirmID)
	}

	for _, room := range rooms {
		confirmationKey := fmt.Sprintf("%s-%s", room.ProviderRoomID, room.OccupancyType)

		confirmID, ok := confirmationMap[confirmationKey]
		if !ok {
			continue
		}

		if len(confirmID) == 0 {
			continue
		}

		selectedConfirm := confirmID[0]

		room.ConfirmationID = selectedConfirm.ConfirmationID
		room.HotelConfirmationID = selectedConfirm.HotelConfirmationID
		room.BookingStatus = selectedConfirm.BookStatus

		if len(confirmationMap[confirmationKey]) > 0 {
			confirmationMap[confirmationKey] = confirmationMap[confirmationKey][1:]
		}
	}
}

func MapRoomConfirmationIDSubHUB(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	confirmationMap := map[string][]*domain.HubRetrieveConfirmationID{}

	for _, confirmID := range confirmationIDs {
		confirmationKey := fmt.Sprintf("%s-%s", confirmID.RoomID, confirmID.OccupancyType)
		if len(confirmationMap[confirmationKey]) == 0 {
			confirmationMap[confirmationKey] = []*domain.HubRetrieveConfirmationID{}
		}

		confirmationMap[confirmationKey] = append(confirmationMap[confirmationKey], confirmID)
	}

	for _, room := range rooms {
		confirmationKey := fmt.Sprintf("%s-%s", room.RoomID, room.OccupancyType)

		confirmID, ok := confirmationMap[confirmationKey]
		if !ok {
			continue
		}

		if len(confirmID) == 0 {
			continue
		}

		selectedConfirm := confirmID[0]

		room.ConfirmationID = selectedConfirm.ConfirmationID
		room.HotelConfirmationID = selectedConfirm.HotelConfirmationID
		room.BookingStatus = selectedConfirm.BookStatus

		if len(confirmationMap[confirmationKey]) > 0 {
			confirmationMap[confirmationKey] = confirmationMap[confirmationKey][1:]
		}
	}
}

func MapRoomConfirmationIDDida(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	confirmationMap := map[string][]*domain.HubRetrieveConfirmationID{}

	for _, confirmID := range confirmationIDs {
		confirmationKey := confirmID.ProviderRoomID
		if len(confirmationMap[confirmationKey]) == 0 {
			confirmationMap[confirmationKey] = []*domain.HubRetrieveConfirmationID{}
		}

		confirmationMap[confirmationKey] = append(confirmationMap[confirmationKey], confirmID)
	}

	for _, room := range rooms {
		confirmationKey := room.ProviderRoomID

		confirmID, ok := confirmationMap[confirmationKey]
		if !ok {
			continue
		}

		if len(confirmID) == 0 {
			continue
		}

		selectedConfirm := confirmID[0]

		room.ConfirmationID = selectedConfirm.ConfirmationID
		room.HotelConfirmationID = selectedConfirm.HotelConfirmationID
		room.BookingStatus = selectedConfirm.BookStatus

		if len(confirmationMap[confirmationKey]) > 0 {
			confirmationMap[confirmationKey] = confirmationMap[confirmationKey][1:]
		}
	}
}

func MapRoomConfirmationID(provider commonEnum.HotelProvider, rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	switch provider {
	case commonEnum.HotelProviderExpedia:
		mapRoomConfirmationIDExpedia(rooms, confirmationIDs)
	case commonEnum.HotelProviderRateHawk:
		MapRoomConfirmationIDRateHawk(rooms, confirmationIDs)
	case commonEnum.HotelProviderDida:
		MapRoomConfirmationIDDida(rooms, confirmationIDs)
	case commonEnum.HotelProviderHNHTravelAgent, commonEnum.HotelProviderMayTravelAgent:
		MapRoomConfirmationIDTA(rooms, confirmationIDs)
	case commonEnum.HotelProviderHub:
		MapRoomConfirmationIDSubHUB(rooms, confirmationIDs)
	case commonEnum.HotelProviderAgoda:
		MapRoomConfirmationIDAgoda(rooms, confirmationIDs)
	default:
		log.Error("Unsupported provider error", log.Int("type", int(provider)))
	}
}

func checkNames(room *domain.HubOrderRoomItem, confirmID *domain.HubRetrieveConfirmationID) bool {
	if confirmID.GivenName == "" && confirmID.Surname == "" {
		return true
	}

	return processName(room.GivenName) == processName(confirmID.GivenName) && processName(room.Surname) == processName(confirmID.Surname)
}

func MapRoomConfirmationIDTA(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	confirmationMap := map[string][]*domain.HubRetrieveConfirmationID{}

	for _, confirmID := range confirmationIDs {
		confirmationKey := confirmID.ProviderRoomID
		if len(confirmationMap[confirmationKey]) == 0 {
			confirmationMap[confirmationKey] = []*domain.HubRetrieveConfirmationID{}
		}

		confirmationMap[confirmationKey] = append(confirmationMap[confirmationKey], confirmID)
	}

	for _, room := range rooms {
		confirmationKey := room.ProviderRoomID

		confirmID, ok := confirmationMap[confirmationKey]
		if !ok {
			continue
		}

		if len(confirmID) == 0 {
			continue
		}

		selectedConfirm := confirmID[0]

		room.ConfirmationID = selectedConfirm.ConfirmationID
		room.HotelConfirmationID = selectedConfirm.HotelConfirmationID
		room.BookingStatus = selectedConfirm.BookStatus

		if len(confirmationMap[confirmationKey]) > 0 {
			confirmationMap[confirmationKey] = confirmationMap[confirmationKey][1:]
		}
	}
}

func MapRoomConfirmationIDAgoda(rooms []*domain.HubOrderRoomItem, confirmationIDs []*domain.HubRetrieveConfirmationID) {
	for _, room := range rooms {
		for _, confirmID := range confirmationIDs {
			if room.ProviderRoomID == confirmID.ProviderRoomID &&
				room.HotelConfirmationID == "" {
				room.ConfirmationID = confirmID.ConfirmationID
				room.HotelConfirmationID = confirmID.HotelConfirmationID
				room.BookingStatus = confirmID.BookStatus
			}
		}
	}
}
