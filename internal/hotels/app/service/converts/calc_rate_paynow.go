package converts

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func ExpediaCheckExtraBedAndNonSmoking(amenities []*domain.HubAmenity) (hasExtraBed bool, isNonSmoking bool) {
	isNonSmoking = true

	for _, amenity := range amenities {
		if amenity.ID == "**********" {
			hasExtraBed = true
		}

		if amenity.ID == "6212" {
			isNonSmoking = false
		}

		if hasExtraBed && isNonSmoking {
			break
		}
	}

	return hasExtraBed, isNonSmoking
}

func CalcRatePayNow(in *domain.HubRateData, provider commonEnum.HotelProvider) {
	if in == nil {
		return
	}
	totalPayNow := float64(0)
	totalPayAtHotel := float64(0)

	totalRateBasic := float64(0)
	totalTaxAmount := float64(0)

	payAtHotelCur := ""

	for _, occupancyRate := range in.OccupancyRate {
		for _, nightly := range occupancyRate.TotalNightlyRate {
			totalRateBasic += nightly.RateBasic * float64(occupancyRate.RoomQuantity)
			totalTaxAmount += nightly.TaxAmount * float64(occupancyRate.RoomQuantity)

			totalPayNow += (nightly.RateBasic + nightly.TaxAmount) * float64(occupancyRate.RoomQuantity)
		}

		for _, payAtHotel := range occupancyRate.PayAtHotel {
			totalPayAtHotel += payAtHotel.Amount * float64(occupancyRate.RoomQuantity)
			payAtHotelCur = payAtHotel.Currency
		}

		for _, occuTax := range occupancyRate.RateTaxes {
			if occuTax == nil {
				continue
			}
			occuTaxWithRoomQuantity := *occuTax.Amount * float64(occupancyRate.RoomQuantity)

			totalPayNow += occuTaxWithRoomQuantity
			totalTaxAmount += occuTaxWithRoomQuantity
		}

		totalPayNow += occupancyRate.Surcharges * float64(occupancyRate.RoomQuantity)
		totalTaxAmount += occupancyRate.Surcharges * float64(occupancyRate.RoomQuantity)
	}

	in.TotalPayAtHotel = helpers.CeilToPrecision(totalPayAtHotel, 3)
	in.PayAtHotelCurrency = payAtHotelCur
	in.PayNow = helpers.CeilToPrecision(totalPayNow, 3)

	if provider == commonEnum.HotelProviderExpedia {
		hasExtraBed, isNonSmoking := ExpediaCheckExtraBedAndNonSmoking(in.Amenities)

		in.HasExtraBed = hasExtraBed
		in.NonSmoking = isNonSmoking
	}

	in.TotalRateAmount = helpers.CeilToPrecision(totalRateBasic+totalTaxAmount, 3)
	in.TotalRateBasic = helpers.CeilToPrecision(totalRateBasic, 3)
	in.TotalTaxAmount = helpers.CeilToPrecision(totalTaxAmount, 3)
}

func CalcPayNowForSearchResult(in *domain.HotelSearchResult, stay domain.HubSearchStay) {
	if in == nil {
		return
	}

	err := stay.CountDays()
	if err != nil {
		log.Error("count days err", log.Any("err", err))
		return
	}

	stayNights := float64(stay.DayCount)

	for _, hotel := range in.Hotels {
		for _, room := range hotel.ListRooms {
			for _, rate := range room.RateData {
				CalcRatePayNow(rate, in.Provider)

				if len(rate.NonrefundableDateRange) == 0 && len(rate.CancelPolicies) == 0 {
				} else {
					rate.Refundable = CalcRefundable(rate.NonrefundableDateRange, stay)
					if rate.Refundable {
						rate.Refundable = CalcPenalty(rate.CancelPolicies, rate.PayNow, stayNights)
					}
				}

				for _, policy := range rate.CancelPolicies {
					if !policy.Refundable {
						policy.PenaltyAmount = rate.PayNow
					} else if policy.PenaltyAmount >= rate.PayNow {
						policy.Refundable = false
						policy.PenaltyAmount = rate.PayNow
					}
				}
			}
		}
	}
}

// BuildRateGroupKey generates a unique string key for grouping rate data based on selected RateDataGroup fields.
func BuildRateGroupKey(rate *domain.HubRateData, fields []enum.RateDataGroup) string {
	var builder strings.Builder

	for _, field := range fields {
		switch field {
		case enum.GroupRefundable:
			builder.WriteString(fmt.Sprintf("refundable:%t;", rate.Refundable))
		case enum.GroupHasBreakfast:
			builder.WriteString(fmt.Sprintf("has_breakfast:%t;", rate.HasBreakfast))
		case enum.GroupHasExtraBed:
			builder.WriteString(fmt.Sprintf("has_extra_bed:%t;", rate.HasExtraBed))
		case enum.GroupNonSmoking:
			builder.WriteString(fmt.Sprintf("non_smoking:%t;", rate.NonSmoking))
		}
	}

	return builder.String()
}

// GroupRateData filters and groups room rate data based on specified RateDataGroup fields.
func GroupRateData(hotels []*domain.HubHotel, groupBy []enum.RateDataGroup) {
	if len(groupBy) == 0 {
		return
	}

	now := time.Now()

	for _, hotel := range hotels {
		if hotel == nil {
			continue
		}

		for _, room := range hotel.ListRooms {
			if room == nil {
				continue
			}
			bestRateMap := make(map[string]*domain.HubRateData)

			for _, rate := range room.RateData {
				// Check cancel policy if refundable
				if rate.Refundable {
					skipRate := false

					for _, policy := range rate.CancelPolicies {
						startDate, err := time.Parse(constants.HubDateFormat, policy.StartDate)
						if err != nil {
							log.Error("Error parsing StartDate:", log.Any("err", err))
							continue
						}

						if !startDate.After(now) {
							nights := 0

							if policy.PenaltyInfo.NumberOfNights != "" {
								if parsedNights, err := strconv.Atoi(policy.PenaltyInfo.NumberOfNights); err == nil {
									nights = parsedNights
								}
							}

							if policy.PenaltyInfo.Amount > 0 ||
								policy.PenaltyInfo.Percent > 0 ||
								nights > 0 {
								skipRate = true
								break
							}
						}
					}

					if skipRate {
						continue
					}
				}

				// Build group key
				key := BuildRateGroupKey(rate, groupBy)

				// Choose the best rate (lowest PayNow)
				current := bestRateMap[key]
				if current == nil || rate.PayNow < current.PayNow {
					bestRateMap[key] = rate
				}
			}

			room.RateData = make([]*domain.HubRateData, 0, len(bestRateMap))
			for _, r := range bestRateMap {
				room.RateData = append(room.RateData, r)
			}
		}
	}
}

func CalcRefundable(nonRefundableDateRange []*domain.HubNonrefundableDateRange, stay domain.HubSearchStay) bool {
	if len(nonRefundableDateRange) == 0 {
		return true
	}

	checkIn, err := time.Parse("2006-01-02", stay.CheckIn)
	if err != nil {
		fmt.Println("Error parsing CheckIn date:", err)
		return false
	}

	checkOut, err := time.Parse("2006-01-02", stay.CheckOut)
	if err != nil {
		fmt.Println("Error parsing CheckOut date:", err)
		return false
	}

	stayDates := make([]time.Time, 0)
	for d := checkIn; d.Before(checkOut); d = d.AddDate(0, 0, 1) {
		stayDates = append(stayDates, d)
	}

	for _, nonRefundable := range nonRefundableDateRange {
		nonStart, err := time.Parse("2006-01-02", nonRefundable.StartDate)
		if err != nil {
			log.Error("Error parsing non-refundable StartDate:", log.Any("err", err))
			return false
		}

		nonEnd, err := time.Parse("2006-01-02", nonRefundable.EndDate)
		if err != nil {
			log.Error("Error parsing non-refundable EndDate:", log.Any("err", err))
			return false
		}

		for _, stayDate := range stayDates {
			if !stayDate.Before(nonStart) && !stayDate.After(nonEnd) {
				return false
			}
		}
	}

	return true
}

func CalcPenalty(policies []*domain.HubCancelPolicy, payNow, stayNights float64) bool {
	if len(policies) == 0 {
		return false
	}

	minPol, err := domain.GetMinPol(policies)
	if err != nil {
		log.Error("CalcRefundable  GetMinPol err", log.Any("err", err))
		return false
	}

	if time.Now().Before(minPol.StartDt) {
		return true
	}

	return minPol.Refundable
}
