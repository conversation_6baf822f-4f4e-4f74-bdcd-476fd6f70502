package converts

import (
	"fmt"
	"strconv"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonHelper "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func FlattenHubRoomByOccupancies(in *domain.HubRoom, occupancies []*domain.HubSearchOccupancy) []*domain.HubRoom {
	result := []*domain.HubRoom{}

	for _, occupancy := range occupancies {
		if occupancy != nil {
			for i := uint(0); i < occupancy.Rooms; i++ {
				item := &domain.HubRoom{
					RoomID:         in.RoomID,
					ProviderRoomID: in.ProviderRoomID,
					Name:           in.Name,
					NameEn:         in.NameEn,
					Provider:       in.Provider,
					OccupancyIndex: occupancy.OccupancyIndex + i,
					OccupancyType:  occupancy.GenOccupancyType(),
				}

				result = append(result, item)
			}
		}
	}

	return result
}

func FromHubHotelToHubOrderRoomItem(in []*domain.HubRoom, bedOption *domain.BedOption) []*domain.HubOrderRoomItem {
	result := []*domain.HubOrderRoomItem{}

	for _, room := range in {
		item := &domain.HubOrderRoomItem{
			RoomID:            room.RoomID,
			ProviderRoomID:    room.ProviderRoomID,
			Name:              room.Name,
			NameEn:            room.NameEn,
			Provider:          room.Provider,
			OccupancyIndex:    room.OccupancyIndex,
			OccupancyType:     room.OccupancyType,
			OldProviderRoomID: room.OldProviderRoomID,
		}

		if bedOption != nil {
			item.BedOption = &domain.RoomBedOption{
				OptionID:   bedOption.OptionID,
				Name:       bedOption.Name,
				Quantity:   bedOption.Quantity,
				BedConfigs: bedOption.BedConfigs,
			}
		}

		result = append(result, item)
	}

	return result
}

func FromHubHotelToHubOrderHotelItem(in *domain.HubHotel, occupancies []*domain.HubSearchOccupancy, bedOption *domain.BedOption) *domain.HubOrderHotelItem {
	result := &domain.HubOrderHotelItem{
		HotelID:         in.HotelID,
		ProviderHotelID: in.ProviderHotelID,
		OldProviderID:   in.OldProviderID,
		Name:            in.Name,
		NameEn:          in.NameEn,
		VAT:             in.VAT,
		Currency:        in.Currency,
		CheckInTime:     in.CheckInTime,
		CheckOutTime:    in.CheckOutTime,
	}

	if len(in.ListRooms) > 0 {
		result.ListRooms = FromHubHotelToHubOrderRoomItem(in.ListRooms, bedOption)
	}

	return result
}

func FromHubHotelToHotelSummaries(in []*domain.Hotel, hotelImageMap map[string]*domain.HotelSummaryImage, searchPlace *domain.Place, providers []commonEnum.HotelProvider) ([]*domain.HotelSummary, error) {
	result := make([]*domain.HotelSummary, 0, len(in))

	centerName := ""

	if searchPlace != nil {
		centerName = searchPlace.Name
	}

	for _, hotel := range in {
		pIds := map[commonEnum.HotelProvider]string{}

		for _, p := range providers {
			if hotel.ProviderIds[p] != "" {
				pIds[p] = hotel.ProviderIds[p]
			}
		}

		if len(pIds) == 0 {
			continue
		}

		address := ""

		countryCode := ""
		if hotel.Address != nil {
			countryCode = hotel.Address.CountryCode
			address = fmt.Sprintf("%s %s ,%s", hotel.Address.Line1, hotel.Address.StateProvinceName, hotel.Address.City)
		}

		var review *domain.HotelReview
		rating := float64(0)

		if hotel.Ratings != nil {
			if hotel.Ratings.Property != nil {
				rate, err := strconv.ParseFloat(hotel.Ratings.Property.Rating, 64)
				if err != nil {
					log.Error("FromHubHotelToHotelSummaries ParseFloat err", log.Any("err", err), log.String("value", hotel.Ratings.Property.Rating))
					rate = 0
				}
				rating = rate
			}

			if hotel.Ratings.Guest != nil && hotel.Ratings.Guest.Overall != "" {
				overall, err := strconv.ParseFloat(hotel.Ratings.Guest.Overall, 64)
				if err != nil {
					log.Error("FromHubHotelToHotelSummaries ParseFloat err", log.Any("err", err), log.String("value", hotel.Ratings.Guest.Overall))
					overall = 0
				}

				review = &domain.HotelReview{
					ReviewCount: int32(hotel.Ratings.Guest.Count),
					Rate:        overall,
				}
			}
		}

		categoryName := ""
		if hotel.Category != nil {
			categoryName = hotel.Category.Name
		}

		thumbnailURL := make([]*domain.HotelSummaryImage, 0, len(hotel.Images))
		if len(hotel.Images) != 0 {
			for _, imageData := range hotel.Images {
				imageLink := domain.HotelSummaryImageLink{
					Px70:   "",
					Px200:  "",
					Px350:  "",
					Px1000: "",
				}

				for key, value := range imageData.Links {
					if value != nil {
						switch key {
						case "70px":
							imageLink.Px70 = value.Href
						case "350px":
							imageLink.Px350 = value.Href
						case "200px":
							imageLink.Px200 = value.Href
						case "1000px":
							imageLink.Px1000 = value.Href
						}
					}
				}

				thumbnailURL = append(thumbnailURL, &domain.HotelSummaryImage{
					HeroImage: imageData.HeroImage,
					Category:  imageData.Category,
					Links:     imageLink,
					Caption:   imageData.Caption,
				})
			}
		} else {
			image, ok := hotelImageMap[hotel.HotelID]
			if ok {
				thumbnailURL = append(thumbnailURL, image)
			}
		}

		summary := &domain.HotelSummary{
			ID:           hotel.HotelID,
			ProviderIds:  pIds,
			Name:         hotel.Name,
			NameEn:       hotel.NameEn,
			Location:     address,
			Review:       review,
			Amenities:    hotel.Amenities,
			CategoryType: categoryName,
			ThumbnailURL: thumbnailURL,
			Rating:       rating,
			CenterInfo: &domain.CenterInfo{
				DistanceToCenter: commonHelper.RoundFloat(hotel.Distance/1000, 3),
				Unit:             "km", // hard code
			},
			CountryCode: countryCode,
		}

		if centerName != "" && summary.CenterInfo != nil {
			summary.CenterInfo.CenterName = centerName
		} else {
			summary.CenterInfo = nil
		}

		if hotel.CheckIn != nil {
			summary.CheckIn = hotel.CheckIn.BeginTime
		}

		if hotel.Checkout != nil {
			summary.CheckOut = hotel.Checkout.Time
		}

		result = append(result, summary)
	}

	return result, nil
}

func FromDomainHotel(in *domain.Hotel) *domain.Place {
	place := &domain.Place{
		PlaceID: in.HotelID,
		Type:    enum.PlaceTypeHotel,
		Name:    in.Name,
		Lang:    in.Language,
	}

	if in.Address != nil {
		place.Address = fmt.Sprintf("%s, %s,%s", in.Address.Line1, in.Address.StateProvinceName, in.Address.City)
		place.CountryCode = in.Address.CountryCode
	}

	if in.Location != nil && in.Location.Coordinates != nil {
		place.Location = &domain.Coordinates{
			Latitude:  in.Location.Coordinates.Latitude,
			Longitude: in.Location.Coordinates.Longitude,
		}
	}

	return place
}

func FromDomainRegion(in *domain.Region) *domain.Place {
	place := &domain.Place{
		PlaceID:     in.RegionID,
		Type:        enum.PlaceTypeValue[in.Type],
		CountryCode: in.CountryCode,
		Name:        in.Name,
		Address:     in.FullName,
		Lang:        in.Language,
	}

	if in.Coordinates != nil {
		place.Location = &domain.Coordinates{
			Latitude:  in.Coordinates.CenterLatitude,
			Longitude: in.Coordinates.CenterLongitude,
		}
	}

	return place
}

func FromHubHotelToHotelSummary(in *domain.HubHotel, stayDayCount int, p commonEnum.HotelProvider) *domain.HotelSummary {
	if in == nil {
		return nil
	}

	result := &domain.HotelSummary{
		ProviderHotelID:       in.ProviderHotelID,
		ID:                    in.HotelID,
		Name:                  in.Name,
		Provider:              p,
		MatchKey:              commonEnum.HotelProviderToHash(p),
		ApplicableNationality: in.ApplicableNationality,
	}

	roomLeft := 0
	var minRate *domain.HubRateData

	for _, room := range in.ListRooms {
		for _, rate := range room.RateData {
			CalcRatePayNow(rate, p)

			if rate == nil {
				continue
			}

			if minRate == nil {
				minRate = rate
				continue
			}

			if rate.PayNow < minRate.PayNow {
				minRate = rate
			}
		}
	}

	if minRate == nil {
		return result
	}

	if minRate.Available != "" {
		roomAvailable, err := strconv.ParseInt(minRate.Available, 0, 32)
		if err != nil {
			log.Error("FromHubHotelToHotelSummary strconv.ParseInt", log.Any("err", err), log.String("value", minRate.Available))
			return result
		}

		roomLeft = int(roomAvailable)
	}

	discountPrice := float64(0)
	originalPrice := float64(0)

	roomCount := len(minRate.OccupancyRate)

	payAtHotel := []*domain.PayAtHotel{}

	for _, item := range minRate.OccupancyRate {
		for _, discount := range item.RateDiscounts {
			if discount.Name == enum.RateDiscountNameName[enum.RateDiscountNameBase] {
				originalPrice += discount.Amount
			}
		}

		for _, total := range item.TotalNightlyRate {
			originalPrice += total.RateBasic
			discountPrice += total.RateBasic
		}

		payAtHotel = append(payAtHotel, item.PayAtHotel...)
	}

	if stayDayCount == 0 {
		stayDayCount = 1
	}

	if roomCount == 0 {
		roomCount = 1
	}

	result.Price = &domain.Price{
		PricePerNight: &domain.PricePerNight{
			DiscountPrice: helpers.CeilToPrecision(discountPrice/float64(roomCount*stayDayCount), 2),
			OriginalPrice: helpers.CeilToPrecision(originalPrice/float64(roomCount*stayDayCount), 2),
		},
		// Total:        minRate.PayNow + minRate.TotalPayAtHotel,
		// https://www.notion.so/deeptechjsc/90e80363855a4bbaafd1b106ecabad75?v=a445560271bf4d0d99bceb3f4be8f311&p=cb93120951ec42d79e47657d1f1f4150&pm=s
		Total:           minRate.PayNow,
		IsIncludeTax:    true,
		Currency:        minRate.Currency,
		SaleScenario:    minRate.SaleScenario,
		PayAtHotel:      payAtHotel,
		TotalPayAtHotel: minRate.TotalPayAtHotel,
		HasBreakfast:    minRate.HasBreakfast,
		HasExtraBed:     minRate.HasExtraBed,
		NonSmoking:      minRate.NonSmoking,
		Refundable:      minRate.Refundable,
	}
	result.Available = true
	result.RoomLeft = roomLeft

	return result
}
