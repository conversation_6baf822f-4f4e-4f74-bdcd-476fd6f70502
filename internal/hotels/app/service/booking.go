package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	commonConstants "gitlab.deepgate.io/apps/common/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonErrors "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mq"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	taclient "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type BookingService interface {
	Book(ctx context.Context, provider commonEnum.HotelProvider, req *domain.HubBookReq, session *domain.HotelSession, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error)
	MarkBookingFail(ctx context.Context, hubOrder *domain.HubHotelOrder) error
	GenOrderCode(ctx context.Context) (code string, err error)
	ResolvePendingBook(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) error
	FetchBookConfirmationID(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) error
	SendUnknownPendingNotify(ctx context.Context, hotelOrder *domain.HubHotelOrder)
	Refund(ctx context.Context, hotelOrder *domain.HubHotelOrder) error
}

type bookingService struct {
	cfg                      *config.Schema
	bookingRedis             redis.BookingRepository
	hubOrderRepo             repositories.OrderRepository
	expediaAdapter           expedia_client.ExpediaAdapter
	tourmindAdapter          tourmind_client.TourmindAdapter
	rateHawkAdapter          rate_hawk_client.RateHawkAdapter
	taAdapter                taclient.TAAdapter
	didaAdapter              dida.Adapter
	notificationClient       notification.NotificationServiceClient
	partnerClient            partner.PartnerClient
	mqClient                 mq.Adapter
	cancelBookingService     CancelBookingService
	hnhTravelAgentClient     travel_agent_client.TravelAgentAdapter
	webhookClient            webhook.WebhookAdapter
	hubAdapter               hub_provider.HubAdapter // Hub Provider adapter cho SUB Hub
	checkAvailabilityRedis   redis.CheckAvailabilityRepository
	providerSearchHotelsRepo repositories.ProviderSearchHotelsRepository
	searchCacheHotelRepo     repositories.SearchHotelCachesRepository
	agodaAdapter             agoda_client.AgodaAdapter
}

func NewBookingService(
	cfg *config.Schema,
	bookingRedis redis.BookingRepository,
	hubOrderRepo repositories.OrderRepository,
	expediaAdapter expedia_client.ExpediaAdapter,
	tourmindAdapter tourmind_client.TourmindAdapter,
	rateHawkAdapter rate_hawk_client.RateHawkAdapter,
	taAdapter taclient.TAAdapter,
	didaAdapter dida.Adapter,
	notificationClient notification.NotificationServiceClient,
	partnerClient partner.PartnerClient,
	mqClient mq.Adapter,
	cancelBookingService CancelBookingService,
	hnhTravelAgentClient travel_agent_client.TravelAgentAdapter,
	webhookClient webhook.WebhookAdapter,
	hubAdapter hub_provider.HubAdapter,
	checkAvailabilityRedis redis.CheckAvailabilityRepository,
	providerSearchHotelsRepo repositories.ProviderSearchHotelsRepository,
	searchCacheHotelRepo repositories.SearchHotelCachesRepository,
	agodaAdapter agoda_client.AgodaAdapter,
) BookingService {
	return &bookingService{
		cfg:                      cfg,
		bookingRedis:             bookingRedis,
		hubOrderRepo:             hubOrderRepo,
		expediaAdapter:           expediaAdapter,
		tourmindAdapter:          tourmindAdapter,
		notificationClient:       notificationClient,
		rateHawkAdapter:          rateHawkAdapter,
		partnerClient:            partnerClient,
		taAdapter:                taAdapter,
		didaAdapter:              didaAdapter,
		mqClient:                 mqClient,
		cancelBookingService:     cancelBookingService,
		hnhTravelAgentClient:     hnhTravelAgentClient,
		webhookClient:            webhookClient,
		hubAdapter:               hubAdapter,
		checkAvailabilityRedis:   checkAvailabilityRedis,
		providerSearchHotelsRepo: providerSearchHotelsRepo,
		searchCacheHotelRepo:     searchCacheHotelRepo,
		agodaAdapter:             agodaAdapter,
	}
}

func (s *bookingService) normalizeRequestHolder(input *domain.HubHolderInfo) {
	for _, item := range input.HolderDetail {
		item.GivenName = strings.ToUpper(strings.TrimSpace(item.GivenName))
		item.Surname = strings.ToUpper(strings.TrimSpace(item.Surname))
	}
}

func (s *bookingService) assignHolderToHotelRoom(in *domain.HubBookReq, rooms []*domain.HubOrderRoomItem) {
	if in == nil || in.Holder == nil || len(in.Holder.HolderDetail) == 0 || len(rooms) == 0 {
		log.Error("AssignHolderToHotelRoom input empty")
		return
	}

	defaultHolder := in.Holder.HolderDetail[0]

	for _, room := range rooms {
		useDefault := true

		for _, holder := range in.Holder.HolderDetail {
			if room.OccupancyIndex != holder.OccupancyIndex {
				continue
			}
			useDefault = false
			room.Email = holder.Email
			room.GivenName = holder.GivenName
			room.Surname = holder.Surname
			room.SpecialRequest = holder.SpecialRequest
		}

		if useDefault {
			room.Email = defaultHolder.Email
			room.GivenName = defaultHolder.GivenName
			room.Surname = defaultHolder.Surname
			room.SpecialRequest = defaultHolder.SpecialRequest
		}
	}
}

func (s *bookingService) Book(ctx context.Context, provider commonEnum.HotelProvider, req *domain.HubBookReq, session *domain.HotelSession, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error) {
	// 1. Check if this is SUB Hub request - Forward to Hub Provider
	isSubHubRequest := ctx.Value(constants.IsSubHubRequestKey)
	if isSubHubRequest != nil && isSubHubRequest.(bool) {
		return s.handleSubHubBooking(ctx, provider, req, session, order)
	}

	// 2. Normal MAIN Hub logic continues...
	if session == nil || order == nil || order.Hotel == nil {
		return "", nil, enum.BookingStatusNone, "", false, domain.ErrInvalidValue
	}

	providerBookingStatus := ""
	reservationCode := ""
	confirmationIDs := []*domain.HubRetrieveConfirmationID{}
	bookingStatus := enum.BookingStatusNone
	unknownPending := false

	var err error

	s.normalizeRequestHolder(req.Holder)
	s.assignHolderToHotelRoom(req, order.Hotel.ListRooms)

	if provider == commonEnum.HotelProviderHNHTravelAgent {
		isHNHTravelAgentActive, err := isInActiveTime(s.cfg.HNHTravelAgentServiceActiveTime, s.cfg.HNHTravelAgentSkipDayOfWeek)
		if err != nil {
			log.Error("isInActiveTime error", log.Any("error", err), log.String("HNHTravelAgentServiceActiveTime", s.cfg.HNHTravelAgentServiceActiveTime))
			isHNHTravelAgentActive = false
		}

		if !isHNHTravelAgentActive {
			return "", nil, enum.BookingStatusNone, "", unknownPending, domain.ErrRoomSoldOut
		}
	}

	if provider == commonEnum.HotelProviderMayTravelAgent {
		isMayTravelAgentActive, err := isInActiveTime(s.cfg.MayTravelAgentServiceActiveTime, "")
		if err != nil {
			log.Error("isInActiveTime error", log.Any("error", err), log.String("MayTravelAgentServiceActiveTime", s.cfg.MayTravelAgentServiceActiveTime))
			isMayTravelAgentActive = false
		}

		if !isMayTravelAgentActive {
			return "", nil, enum.BookingStatusNone, "", unknownPending, domain.ErrRoomSoldOut
		}
	}

	switch provider {
	case commonEnum.HotelProviderExpedia:
		{
			reservationCode, confirmationIDs, bookingStatus, unknownPending, err = s.expediaBook(ctx, req, session.ExpediaSessionInfo, order)
		}
	case commonEnum.HotelProviderHNHTravelAgent, commonEnum.HotelProviderMayTravelAgent, commonEnum.HotelProviderBZTTravelAgent:
		{
			reservationCode, confirmationIDs, bookingStatus, err = s.hnhTravelAgentClient.Booking(ctx, req, provider, session.BasicSessionInfo.BookingKey, order.OrderCode)
		}
	case commonEnum.HotelProviderRateHawk:
		{
			reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err = s.rateHawkBook(ctx, req, session.BasicSessionInfo, order)
		}
	case commonEnum.HotelProviderTA:
		{
			reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, err = s.taBook(ctx, req.SessionID, order)
		}
	case commonEnum.HotelProviderDida:
		{
			reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err = s.didaBook(ctx, req, session.DidaSessionInfo, order)
		}
	case commonEnum.HotelProviderAgoda:
		{
			reservationCode, confirmationIDs, bookingStatus, unknownPending, err = s.agodaBook(ctx, req, session.AgodaSessionInfo, order)
		}
	default:
		log.Error("Unsupported provider error", log.Int("type", int(provider)))
		return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, commonErrors.ErrInvalidInput
	}

	if err != nil {
		log.Error("Booking error", log.Any("error", err))
		return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err
	}

	return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err
}

func (s *bookingService) ResolvePendingBook(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) error {
	if hotelOrder == nil ||
		hotelOrder.BookingStatus != enum.BookingStatusPending {
		return nil
	}

	err := s.bookingRedis.AcquireResolvePendingLock(hotelOrder.OrderCode)
	if err != nil {
		return err
	}

	defer s.bookingRedis.ReleaseResolvePendingLock(hotelOrder.OrderCode)

	forceFail := false
	bookingStatus := enum.BookingStatusNone
	confirmationIDs := []*domain.HubRetrieveConfirmationID{}

	initialStatus := hotelOrder.ProviderBookingStatus

	if !forceFail && !hotelOrder.ManualIssuing {
		confirmationIDs, bookingStatus, err = s.fetchBookingStatus(ctx, hotelOrder, session)
		if err != nil {
			log.Error("Fetch booking error", log.Any("error", err), log.Any("order_code", hotelOrder.OrderCode))
			return err
		}

		if bookingStatus == enum.BookingStatusFailed {
			forceFail = true

			needToRemoveCacheProvider := []commonEnum.HotelProvider{
				commonEnum.HotelProviderHNHTravelAgent,
				commonEnum.HotelProviderMayTravelAgent,
			}

			if lo.Contains(needToRemoveCacheProvider, hotelOrder.Provider) {
				hotel := hotelOrder.Hotel
				if hotel == nil || len(hotel.ListRooms) == 0 || hotelOrder.RateData == nil {
					log.Error("hotelOrder is invalid", log.Any("hotelOrder", hotelOrder))
					return errors.ErrSomethingOccurred
				}

				s.removeCheckAvailabilityCache(ctx, hotelOrder.Hotel.HotelID, hotel.ListRooms[0].RoomID, hotelOrder.RateData.RateID, hotelOrder.SearchKey)
				err = s.searchCacheHotelRepo.RemoveSoldOutCache(ctx, hotel.HotelID, hotelOrder.Provider)
				if err != nil {
					log.Error("s.searchCacheHotelRepo.RemoveSoldOutCache err", log.Any("HotelID", hotel.HotelID), log.Any("err", err))
				}
			}
		}
	}

	if bookingStatus == enum.BookingStatusSuccess || initialStatus != hotelOrder.ProviderBookingStatus || hotelOrder.ShouldUpdate {
		// hotelOrder.ConfirmationID = confirmationID
		if bookingStatus == enum.BookingStatusSuccess {
			converts.MapRoomConfirmationID(hotelOrder.Provider, hotelOrder.Hotel.ListRooms, confirmationIDs)
			hotelOrder.BookingStatus = bookingStatus
			hotelOrder.PendingHotelConfirmationDeadline = time.Now().Add(constants.PendingHotelConfirmationDeadline).UnixMilli()
		}

		err := s.hubOrderRepo.UpdateOne(ctx, hotelOrder.ID, hotelOrder)
		if err != nil {
			log.Error("UpdateOne error", log.Any("error", err))
			return err
		}
	}

	if bookingStatus == enum.BookingStatusPending && time.Now().UnixMilli() > hotelOrder.PendingDeadline {
		// https://www.notion.so/deeptechjsc/C-p-nh-t-Pending-HOTEL-HUB-229437ea5a9980109fddfffe3cc75664?source=copy_link#22c437ea5a9980a18b7de46dfd4b8f8f
		forceFail = true

		go func() {
			teleCtx, cc := context.WithTimeout(context.Background(), time.Minute)
			defer cc()

			location, err := time.LoadLocation("Asia/Ho_Chi_Minh")
			if err != nil {
				log.Error("failed to load location:", log.Any("err", err))
				return
			}

			if hotelOrder.BookingRequest == nil ||
				hotelOrder.RequestHolder == nil ||
				len(hotelOrder.RequestHolder.HolderDetail) == 0 {
				log.Error("Invalid Booking info", log.Any("hotelOrder", hotelOrder))
				return
			}

			partnerInfo, err := s.partnerClient.GetOfficeInfo(teleCtx, hotelOrder.OfficeID, "")
			if err != nil {
				log.Error("GetOfficeInfo", log.Any("err", err))
				return
			}

			if partnerInfo == nil {
				log.Error("partnerInfo not found")
				return
			}

			err = s.notificationClient.SendMessage(teleCtx, &notification.SendMessageRequest{
				EntityType:  commonConstants.NotificationEntityPartnership,
				EntityID:    hotelOrder.PartnershipID,
				ServiceCode: commonConstants.NotificationServiceCodeHUB,
				Action:      commonConstants.TelegramNotificationActionHotelBookingPending,
				Data: map[string]string{
					"PaxName":       fmt.Sprintf("%s %s", hotelOrder.RequestHolder.HolderDetail[0].Surname, hotelOrder.RequestHolder.HolderDetail[0].GivenName),
					"CreatedAt":     time.UnixMilli(hotelOrder.CreatedAt).In(location).Format("02/01/2006 15:04:05"),
					"OrderCode":     hotelOrder.OrderCode,
					"BookingStatus": enum.BookingStatusName[hotelOrder.BookingStatus],
					"PlatForm":      partnerInfo.Name,
					"HotelName":     hotelOrder.Hotel.Name,
					"Provider":      commonEnum.HotelProviderName[hotelOrder.Provider],
				},
			})
			if err != nil {
				log.Error("SendMessage error", log.Any("err", err))
			}
		}()
	}

	if forceFail {
		hotelOrder.BookingStatus = enum.BookingStatusFailed

		err = s.Refund(ctx, hotelOrder)
		if err != nil {
			log.Error("bookingService.Refund error", log.Any("error", err))
			return err
		}
	}

	return nil
}

func (s *bookingService) SendUnknownPendingNotify(ctx context.Context, hotelOrder *domain.HubHotelOrder) {
	if hotelOrder == nil ||
		hotelOrder.BookingStatus != enum.BookingStatusPending {
		return
	}

	go func() {
		teleCtx, cc := context.WithTimeout(context.Background(), time.Minute)
		defer cc()

		location, err := time.LoadLocation("Asia/Ho_Chi_Minh")
		if err != nil {
			log.Error("failed to load location:", log.Any("err", err))
			return
		}

		if hotelOrder.BookingRequest == nil ||
			hotelOrder.RequestHolder == nil ||
			len(hotelOrder.RequestHolder.HolderDetail) == 0 {
			log.Error("Invalid Booking info", log.Any("hotelOrder", hotelOrder))
			return
		}

		partnerInfo, err := s.partnerClient.GetOfficeInfo(teleCtx, hotelOrder.OfficeID, "")
		if err != nil {
			log.Error("GetOfficeInfo", log.Any("err", err))
			return
		}

		if partnerInfo == nil {
			log.Error("partnerInfo not found")
			return
		}

		err = s.notificationClient.SendMessage(teleCtx, &notification.SendMessageRequest{
			EntityType:  commonConstants.NotificationEntityPartnership,
			EntityID:    hotelOrder.PartnershipID,
			ServiceCode: commonConstants.NotificationServiceCodeHUB,
			Action:      commonConstants.TelegramNotificationActionHotelBookingPending,
			Data: map[string]string{
				"PaxName":       fmt.Sprintf("%s %s", hotelOrder.RequestHolder.HolderDetail[0].Surname, hotelOrder.RequestHolder.HolderDetail[0].GivenName),
				"CreatedAt":     time.UnixMilli(hotelOrder.CreatedAt).In(location).Format("02/01/2006 15:04:05"),
				"OrderCode":     hotelOrder.OrderCode,
				"BookingStatus": enum.BookingStatusName[hotelOrder.BookingStatus],
				"PlatForm":      partnerInfo.Name,
				"HotelName":     hotelOrder.Hotel.Name,
				"Provider":      commonEnum.HotelProviderName[hotelOrder.Provider],
			},
		})
		if err != nil {
			log.Error("SendMessage error", log.Any("err", err))
		}
	}()
}

func (s *bookingService) FetchBookConfirmationID(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) error {
	if hotelOrder == nil ||
		hotelOrder.BookingStatus != enum.BookingStatusSuccess {
		return nil
	}

	err := s.bookingRedis.AcquireResolvePendingLock(hotelOrder.OrderCode)
	if err != nil {
		return err
	}

	defer s.bookingRedis.ReleaseResolvePendingLock(hotelOrder.OrderCode) //nolint

	confirmationIDs, bookingStatus, err := s.fetchBookingStatus(ctx, hotelOrder, session)
	if err != nil {
		return err
	}

	if bookingStatus != enum.BookingStatusSuccess {
		log.Error("booking status not match", log.Any("hotelOrder", hotelOrder.OrderCode), log.Any("bookingStatus", bookingStatus))
		return domain.ErrInvalidBookingStatus
	}

	converts.MapRoomConfirmationID(hotelOrder.Provider, hotelOrder.Hotel.ListRooms, confirmationIDs)

	err = s.hubOrderRepo.UpdateConfirmationIDs(ctx, hotelOrder.ID, hotelOrder.BookingStatus, hotelOrder.Hotel.ListRooms)
	if err != nil {
		log.Error("UpdateConfirmationIDs error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode))
		return err
	}

	// Check fetch success
	isSuccessRetrieve := true

	for _, confirmData := range confirmationIDs {
		if confirmData.ConfirmationID == "" || confirmData.HotelConfirmationID == "" {
			isSuccessRetrieve = false
			break
		}
	}

	if isSuccessRetrieve {
		shopInfo, err := s.partnerClient.GetOfficeInfo(ctx, hotelOrder.OfficeID, "")
		if err != nil {
			log.Error("GetOfficeInfo error", log.Any("error", err), log.String("officeID", hotelOrder.OfficeID))
			return err
		}

		err = s.webhookClient.UpdateConfirmationID(ctx, hotelOrder, s.cfg.HubPartnershipID, shopInfo.WebhookCfg.WebhookKey, shopInfo.WebhookCfg.WebhookURLCfg.ConfirmationID)
		if err != nil {
			return err
		}
	} else {
		log.Info("Fetch booking confirmation no result", log.Any("booking", hotelOrder.OrderCode))
	}

	return nil
}

func (s *bookingService) fetchBookingStatus(ctx context.Context, hotelOrder *domain.HubHotelOrder, session *domain.HotelSession) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	confirmationIDs := []*domain.HubRetrieveConfirmationID{}
	bookingStatus := enum.BookingStatusNone

	if hotelOrder == nil || session == nil {
		return confirmationIDs, bookingStatus, domain.ErrInvalidValue
	}

	var err error

	switch hotelOrder.Provider {
	case commonEnum.HotelProviderExpedia:
		confirmationIDs, bookingStatus, err = s.expediaAdapter.Retrieve(ctx, hotelOrder, hotelOrder.OrderCode)
	case commonEnum.HotelProviderRateHawk:
		confirmationIDs, bookingStatus, err = s.rateHawkAdapter.Retrieve(ctx, hotelOrder)
	case commonEnum.HotelProviderHNHTravelAgent, commonEnum.HotelProviderMayTravelAgent, commonEnum.HotelProviderBZTTravelAgent:
		var hotelConfirmIDs []string
		var confirmID string
		hotelConfirmIDs, confirmID, bookingStatus, err = s.hnhTravelAgentClient.RetrieveBooking(ctx, &domain.HubRetrieveBookingReq{
			OrderCode: hotelOrder.OrderCode,
		}, hotelOrder.Provider, hotelOrder.OrderCode)

		if bookingStatus == enum.BookingStatusSuccess {
			if hotelOrder.Hotel == nil {
				log.Error("fetchBookingStatus hotelOrder mapping confirmIDs err", log.Any("confirmID", confirmID), log.Any("hotelConfirmIDs", hotelConfirmIDs))
				return confirmationIDs, bookingStatus, nil
			}

			for index, room := range hotelOrder.Hotel.ListRooms {
				confirmID := confirmID
				hotelConfirmID := ""

				if index < len(hotelConfirmIDs) {
					hotelConfirmID = hotelConfirmIDs[index]
				}

				confirmationIDs = append(confirmationIDs, &domain.HubRetrieveConfirmationID{
					ProviderRoomID:      room.RoomID,
					ConfirmationID:      confirmID,
					HotelConfirmationID: hotelConfirmID,
					OccupancyType:       room.OccupancyType,
					GivenName:           room.GivenName,
					Surname:             room.Surname,
					BedOptionID:         room.BedOption.OptionID,
					BookStatus:          bookingStatus,
					Used:                true,
				})
			}
		}
	case commonEnum.HotelProviderTA:
		{
			confirmationIDs, bookingStatus = s.fetchBookingStatusTA(ctx, hotelOrder)
		}
	case commonEnum.HotelProviderDida:
		{
			confirmationIDs, bookingStatus, err = s.didaAdapter.Retrieve(ctx, hotelOrder, hotelOrder.OrderCode)
		}
	case commonEnum.HotelProviderHub:
		{
			var res *domain.HubHotelOrder

			res, err = s.hubOrderRepo.FindOneByOrderCodeWithPartnership(ctx, "", hotelOrder.ReservationCode, s.cfg.HubPartnershipID)
			if res != nil {
				confirmationIDs = ConvertHubRetrieveBookingResToConfirmationIDs(res)
				bookingStatus = res.BookingStatus
			}
		}
	case commonEnum.HotelProviderAgoda:
		{
			_, confirmationIDs, bookingStatus, _, err = s.agodaAdapter.RetrieveBooking(ctx, hotelOrder, hotelOrder.OrderCode)
		}
	default:
		log.Error("Unsupported provider error", log.Any("type", hotelOrder.Provider))
	}

	if err != nil {
		log.Error("Retrieve Booking error", log.Any("error", err))
		return confirmationIDs, bookingStatus, nil
	}

	return confirmationIDs, bookingStatus, nil
}

func (s *bookingService) fetchBookingStatusTA(ctx context.Context, hotelOrder *domain.HubHotelOrder) ([]*domain.HubRetrieveConfirmationID, enum.BookingStatus) {
	if hotelOrder.ProviderBookingStatus == domain.TABookingStatusCompleted {
		return nil, enum.BookingStatusSuccess
	}

	// Rebook due to book fail
	if hotelOrder.ReservationCode == "" {
		if err := s.mqClient.BookOldProvider(ctx, hotelOrder.OrderCode); err != nil {
			log.Error("mqClient.BookOldProvider error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode))
			return nil, hotelOrder.BookingStatus
		}

		return nil, enum.BookingStatusPending
	}

	// Webhook complete status timeout
	cfIds, status, err := s.taAdapter.Retrieve(ctx, hotelOrder, hotelOrder.OrderCode)
	if err != nil {
		log.Error("taAdapter.Retrieve error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode))

		if time.Since(time.UnixMilli(hotelOrder.PendingStartAt)).Minutes() > 7 {
			status = enum.BookingStatusPending
		} else {
			return nil, enum.BookingStatusPending
		}
	}

	if status == enum.BookingStatusSuccess {
		return cfIds, status
	}

	if status == enum.BookingStatusPending {
		hotelOrder.CancelReason = "completed webhook timeout"
		hotelOrder.ShouldUpdate = true

		if err := s.taAdapter.Cancel(ctx, hotelOrder.ReservationCode, hotelOrder.OrderCode); err != nil {
			log.Error("taAdapter.Cancel error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode), log.Any("status", status))
			return nil, hotelOrder.BookingStatus
		}
	}

	if err := s.mqClient.BookOldProvider(ctx, hotelOrder.OrderCode); err != nil {
		log.Error("mqClient.BookOldProvider error", log.Any("error", err), log.String("orderCode", hotelOrder.OrderCode))
		return nil, hotelOrder.BookingStatus
	}

	return nil, enum.BookingStatusPending
}

func (s *bookingService) Refund(ctx context.Context, hotelOrder *domain.HubHotelOrder) error {
	err := s.hubOrderRepo.WithTransaction(ctx, func(tnxSession context.Context) (interface{}, error) {
		hotelOrder.Refunded = true
		hotelOrder.ExchangedRefundData = &domain.RefundData{
			RefundAmount: hotelOrder.ExchangedRateDataCf.PayNow,
			Currency:     hotelOrder.ExchangedRateDataCf.Currency,
		}

		if hotelOrder.RequestCurrencyRateDataCf != nil {
			hotelOrder.RequestCurrencyRefundData = &domain.RefundData{
				RefundAmount: hotelOrder.RequestCurrencyRateDataCf.PayNow,
				Currency:     hotelOrder.RequestCurrencyRateDataCf.Currency,
			}
		}

		err := s.hubOrderRepo.UpdateOne(tnxSession, hotelOrder.ID, hotelOrder)
		if err != nil {
			log.Error("Refund update refund orderRepo.UpdateOne", log.Any("err", err))
			return nil, domain.ErrBookingCancelFailed
		}

		_, err = s.cancelBookingService.RefundBooking(tnxSession, nil, hotelOrder.PartnershipID, hotelOrder.OfficeID, hotelOrder.LastTransactionID, hotelOrder.OrderCode, commonEnum.HotelProviderName[hotelOrder.Provider], hotelOrder.ExchangedRefundData.RefundAmount)
		if err != nil {
			log.Error("RefundBooking err", log.Any("err", err))
			return nil, commonErrors.ErrSomethingOccurred
		}

		return nil, nil
	})
	if err != nil {
		return err
	}

	return nil
}

func (h *bookingService) GenOrderCode(ctx context.Context) (code string, err error) {
	const (
		bookingCodeLength = 12
		retry             = 3
	)

	for i := 0; i < retry; i++ {
		code = helpers.GenerateBookingCode()

		check, err := h.hubOrderRepo.FindOneByOrderCode(ctx, "", code)
		if err != nil {
			log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err), log.String("code", code))
			return "", err
		}

		if check != nil || code == "" {
			continue
		}

		break
	}

	return
}

func (s *bookingService) MarkBookingFail(ctx context.Context, hubOrder *domain.HubHotelOrder) error {
	if hubOrder == nil {
		return domain.ErrInvalidValue
	}

	if hubOrder.BookingStatus == enum.BookingStatusSuccess {
		log.Error("[MarkBookingFail] booking confirmed,cannot mark fail")
		return domain.ErrInvalidValue
	}

	if hubOrder.BookingStatus == enum.BookingStatusCancel || hubOrder.HubOrderStatus == enum.HubOrderStatusCancelled {
		return domain.ErrBookingCanceled
	}

	hubOrder.BookingStatus = enum.BookingStatusFailed

	err := s.hubOrderRepo.UpdateOne(ctx, hubOrder.ID, hubOrder)
	if err != nil {
		log.Error("[MarkBookingFail] hubOrderRepo.UpdateOne err", log.Any("err", err))
		return err
	}

	return nil
}

// func (s *bookingService) tourmindBook(ctx context.Context, req *domain.HubBookReq, session *domain.TourmindSessionInfo, order *domain.HubHotelOrder) (string, string, enum.BookingStatus, error) {
// 	if req == nil || req.Holder == nil {
// 		return "", "", enum.BookingStatusNone, domain.ErrInvalidValue
// 	}

// 	reservationCode, confirmationID, bookingStatus, err := s.tourmindAdapter.Book(ctx, req, session, order, req.SessionID)
// 	if err != nil {
// 		log.Error("tourmindBook error", log.Any("error", err), log.Any("req", req), log.String("sessionID", req.SessionID))
// 	}

// 	return reservationCode, confirmationID, bookingStatus, err
// }

func (s *bookingService) expediaBook(ctx context.Context, req *domain.HubBookReq, session *domain.ExpediaSessionInfo, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, false, domain.ErrInvalidValue
	}

	req.Holder.PhoneCode = helpers.RemoveNonDigitCharacters(req.Holder.PhoneCode)

	reservationCode, confirmationIDs, bookingStatus, unknownPending, err := s.expediaAdapter.Book(ctx, req, session, order.Hotel.ListRooms, req.SessionID)
	if err != nil {
		log.Error("expediaBook error", log.Any("error", err))
	}

	return reservationCode, confirmationIDs, bookingStatus, unknownPending, err
}

func (s *bookingService) rateHawkBook(ctx context.Context, req *domain.HubBookReq, session *domain.BasicSessionInfo, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, "", false, domain.ErrInvalidValue
	}

	req.Holder.PhoneCode = helpers.RemoveNonDigitCharacters(req.Holder.PhoneCode)

	reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err := s.rateHawkAdapter.Book(ctx, req, session, order, req.SessionID)
	if err != nil {
		log.Error("rateHawkBook error", log.Any("error", err), log.String("sessionID", req.SessionID), log.Any("req", req))
		return "", nil, enum.BookingStatusNone, "", unknownPending, err
	}

	return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err
}

func (s *bookingService) taBook(ctx context.Context, sessionID string, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, error) {
	if order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, "", domain.ErrInvalidValue
	}

	reservationCode, confirmationIDs, bookingStatus, err := s.taAdapter.Book(ctx, order, sessionID)
	if err != nil {
		log.Error("rateHawkBook error", log.Any("error", err), log.String("sessionID", sessionID))
		return "", nil, enum.BookingStatusNone, "", err
	}

	return reservationCode, confirmationIDs, bookingStatus, "", err
}

func (s *bookingService) didaBook(ctx context.Context, req *domain.HubBookReq, session *domain.DidaSessionInfo, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, "", false, domain.ErrInvalidValue
	}

	reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err := s.didaAdapter.Book(ctx, req, session, order, req.SessionID)
	if err != nil {
		log.Error("didaBook error", log.Any("error", err), log.String("sessionID", req.SessionID), log.Any("req", req))
		return "", nil, enum.BookingStatusNone, "", unknownPending, err
	}

	return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, unknownPending, err
}

func (s *bookingService) agodaBook(ctx context.Context, req *domain.HubBookReq, session *domain.AgodaSessionInfo, order *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	if req == nil || req.Holder == nil || order == nil || order.Hotel == nil || order.HotelSearchRequest == nil {
		return "", nil, enum.BookingStatusNone, false, domain.ErrInvalidValue
	}

	reservationCode, confirmationIDs, bookingStatus, unknownPending, err := s.agodaAdapter.Booking(ctx, req, order, session, order.OrderCode)
	if err != nil {
		log.Error("agodaBook error", log.Any("error", err), log.String("sessionID", req.SessionID), log.Any("req", req))
		return "", nil, bookingStatus, unknownPending, err
	}

	return reservationCode, confirmationIDs, bookingStatus, unknownPending, err
}

// handleSubHubBooking xử lý SUB Hub booking request bằng cách forward qua Hub Provider.
func (s *bookingService) handleSubHubBooking(
	ctx context.Context,
	provider commonEnum.HotelProvider,
	req *domain.HubBookReq,
	session *domain.HotelSession,
	order *domain.HubHotelOrder,
) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error) {
	// Forward request to MAIN Hub Provider (SUB Hub chỉ có provider Hub duy nhất)
	s.assignHolderToHotelRoom(req, order.Hotel.ListRooms)

	req.SessionID = session.HubSessionInfo.SessionID
	req.ClientOrderCode = order.OrderCode

	response, err := s.hubAdapter.Book(ctx, req)
	if err != nil {
		log.Error("SUB Hub booking failed",
			log.String("error", err.Error()),
			log.String("session_id", req.SessionID),
			log.String("order_code", order.OrderCode),
		)

		return "", nil, enum.BookingStatusNone, "", false, err
	}

	if response == nil || response.ErrorCode != "" {
		return "", nil, enum.BookingStatusFailed, "", false, domain.ErrBookingFailed
	}

	// Convert HubBookRes to booking service format
	// HubBookRes chỉ có OrderCode, BookingStatus, PendingDeadline
	// SUB Hub không trả về reservation code và confirmation IDs ngay lập tức
	reservationCode := response.OrderCode                    // Sẽ được set sau khi retrieve
	confirmationIDs := []*domain.HubRetrieveConfirmationID{} // Sẽ được set sau khi retrieve
	bookingStatus := response.BookingStatus

	// Force pending after book
	if response.BookingStatus == enum.BookingStatusSuccess {
		bookingStatus = enum.BookingStatusPending
	}

	providerBookingStatus := enum.BookingStatusName[response.BookingStatus]

	// Log successful response
	log.Info("SUB Hub booking completed successfully",
		log.String("order_code", response.OrderCode),
		log.String("session_id", req.SessionID),
		log.String("original_order_code", order.OrderCode),
		log.String("booking_status", enum.BookingStatusName[bookingStatus]),
	)

	return reservationCode, confirmationIDs, bookingStatus, providerBookingStatus, false, nil
}

func ConvertHubRetrieveBookingResToConfirmationIDs(res *domain.HubHotelOrder) []*domain.HubRetrieveConfirmationID {
	if res == nil || res.Hotel == nil || len(res.Hotel.ListRooms) == 0 {
		return []*domain.HubRetrieveConfirmationID{}
	}

	confirmationIDs := make([]*domain.HubRetrieveConfirmationID, len(res.Hotel.ListRooms))
	for i, room := range res.Hotel.ListRooms {
		confirmationIDs[i] = &domain.HubRetrieveConfirmationID{
			RoomID:              room.RoomID,
			ConfirmationID:      room.ConfirmationID,
			HotelConfirmationID: room.HotelConfirmationID,
			OccupancyType:       room.OccupancyType,
			GivenName:           room.GivenName,
			Surname:             room.Surname,
			BookStatus:          room.BookingStatus,
		}

		if room.BedOption != nil {
			confirmationIDs[i].BedOptionID = room.BedOption.OptionID
		}
	}

	return confirmationIDs
}

func (s *bookingService) removeCheckAvailabilityCache(ctx context.Context, hotelID, roomID, rateID, searchKey string) {
	s.checkAvailabilityRedis.AcquireCachingLock(searchKey)
	defer s.checkAvailabilityRedis.ReleaseCachingLock(searchKey)

	searchRes, err := s.providerSearchHotelsRepo.FindByID(ctx, searchKey, hotelID, roomID, rateID)
	if err != nil {
		log.Error("s.providerSearchHotelsRepo.FindByRoomID",
			log.String("error", err.Error()),
			log.String("searchKey", searchKey),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
		)
	}

	if searchRes == nil {
		log.Warn("searchRes nil",
			log.String("searchKey", searchKey),
			log.String("hotelID", hotelID),
			log.String("roomID", roomID),
			log.String("rateID", rateID),
		)
		return
	}

	for _, hotel := range searchRes.Hotels {
		if hotel.HotelID == hotelID {
			for _, room := range hotel.ListRooms {
				if room.RoomID == roomID {
					for _, rate := range room.RateData {
						rate.IsSoldOut = true

						err := s.providerSearchHotelsRepo.UpdateRateInDocument(ctx, searchRes.ID, hotelID, roomID, rate.RateID, rate)
						if err != nil {
							log.Error("s.providerSearchHotelsRepo.UpdateRateInDocument",
								log.String("error", err.Error()),
								log.String("searchKey", searchKey),
								log.String("hotelID", hotelID),
								log.String("roomID", roomID),
								log.String("rateID", rateID),
							)
						}
					}

					break
				}
			}

			break
		}
	}
}
