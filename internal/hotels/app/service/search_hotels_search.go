package service

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/tracing"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *searchHotelService) filterInActiveHotels(ctx context.Context, areaCache *domain.HotelAreaCacheItem, reqProviders []commonEnum.HotelProvider) (*domain.HotelAreaCacheItem, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.filterInActiveHotels")
	defer span.End()

	if areaCache == nil {
		return areaCache, nil
	}

	activeHotelIDs := []string{}
	for _, item := range areaCache.Hotels {
		activeHotelIDs = append(activeHotelIDs, item.HotelID)
	}

	inActiveHotels, err := s.inActiveHotelsRepo.FindByHotelIDs(ctx, activeHotelIDs)
	if err != nil {
		log.Error("s.inActiveHotelsRepo.FindByHotelIDs error", log.Any("error", err), log.Any("hotelIDs", activeHotelIDs))
		return nil, err
	}

	inActiveHotelIDs := []string{}
	for _, item := range inActiveHotels {
		isInActive := false
		for _, provider := range item.Providers {
			if lo.Contains(reqProviders, provider) {
				isInActive = true
			}
		}

		if isInActive {
			inActiveHotelIDs = append(inActiveHotelIDs, item.HotelID)
		}
	}

	areaCache.Hotels = lo.Filter(areaCache.Hotels, func(item domain.AreaCacheHotelInfo, _ int) bool {
		return item.HotelID == areaCache.AreaID || !lo.Contains(inActiveHotelIDs, item.HotelID)
	})

	return areaCache, nil
}

func (s *searchHotelService) filterFirstHotelPrice(ctx context.Context, hotel *domain.HotelSummary) error {
	if hotel == nil {
		return nil
	}

	inActiveHotels, err := s.inActiveHotelsRepo.FindByHotelIDs(ctx, []string{hotel.ID})
	if err != nil {
		log.Error("s.inActiveHotelsRepo.FindByHotelIDs error", log.Any("error", err), log.Any("hotel.ID", hotel.ID))
		return err
	}

	if len(inActiveHotels) != 0 {
		hotel.Price = nil
		hotel.Available = false
		hotel.ExchangedPrice = nil
		hotel.RoomLeft = 0
		hotel.MatchKey = ""
	}

	return nil
}

func (s *searchHotelService) Search(
	ctx context.Context,
	req *domain.HubSearchHotelRequest,
	reqProviders []commonEnum.HotelProvider,
) (
	_ []*domain.HotelSummary,
	_ bool,
	err error,
) {
	// 1. Check if this is SUB Hub request - Forward to Hub Provider
	isSubHubRequest := ctx.Value(constants.IsSubHubRequestKey)
	if isSubHubRequest != nil && isSubHubRequest.(bool) {
		return s.handleSubHubSearch(ctx, req, reqProviders)
	}

	// 2. Normal MAIN Hub logic continues...
	// Validate & side logic
	if len(reqProviders) == 0 {
		return []*domain.HotelSummary{}, false, nil
	}

	if req.Language == "" {
		req.Language = constants.DefaultLanguage
	}

	if req.CountryCode == "" {
		req.CountryCode = "VN"
	}

	err = s.validateSearchHotelRequest(req)
	if err != nil {
		return nil, false, err
	}
	// Begin
	firstBatchSize := int64(req.Pagination.PageLimit)

	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("GenSearchKey error", log.Any("error", err), log.Any("req", req))
		return nil, false, commonErrs.ErrSomethingOccurred
	}

	// t := time.Now()

	isHNHTravelAgentActive, err := isInActiveTime(s.cfg.HNHTravelAgentServiceActiveTime, s.cfg.HNHTravelAgentSkipDayOfWeek)
	if err != nil {
		log.Error("isInActiveTime error", log.Any("error", err), log.String("HNHTravelAgentServiceActiveTime", s.cfg.HNHTravelAgentServiceActiveTime))
		isHNHTravelAgentActive = false
	}

	isMayTravelAgentActive, err := isInActiveTime(s.cfg.MayTravelAgentServiceActiveTime, "")
	if err != nil {
		log.Error("isInActiveTime error", log.Any("error", err), log.String("MayTravelAgentServiceActiveTime", s.cfg.MayTravelAgentServiceActiveTime))
		isMayTravelAgentActive = false
	}

	isBZTTravelAgentActive, err := isInActiveTime(s.cfg.BZTTravelAgentServiceActiveTime, "")
	if err != nil {
		log.Error("isInActiveTime error", log.Any("error", err), log.String("BZTTravelAgentServiceActiveTime", s.cfg.BZTTravelAgentServiceActiveTime))
		isBZTTravelAgentActive = false
	}

	if !isBZTTravelAgentActive {
		reqProviders = lo.Without(reqProviders, commonEnum.HotelProviderBZTTravelAgent)
	}

	if !isHNHTravelAgentActive {
		reqProviders = lo.Without(reqProviders, commonEnum.HotelProviderHNHTravelAgent)
	}

	if !isMayTravelAgentActive {
		reqProviders = lo.Without(reqProviders, commonEnum.HotelProviderMayTravelAgent)
	}

	dataProviderMap := map[commonEnum.HotelProvider][]*domain.HotelSummary{}
	foundProviders := []commonEnum.HotelProvider{}

	if !req.NoCache {
		dataProviderMap, foundProviders, err = s.SearchCached(ctx, req, reqProviders, searchKey)
		if err != nil {
			log.Error("s.SearchCached error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
			return nil, false, err
		}
	}

	// Retun first batch size
	shouldWaitFullData := true
	loadMore := false
	totalCount := 0

	missingProviders, _ := lo.Difference(reqProviders, foundProviders)

	if len(missingProviders) > 0 {
		lock, err := s.redisLock.AcquireLock(searchKey)
		if err != nil {
			log.Error("AcquireLock error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, false, err
		}

		if !lock {
			err = s.redisLock.WaitForLockToRelease(searchKey, time.Second, constants.CacheSearchResultTimeoutSecs)
			if err != nil {
				log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("searchKey", searchKey))
				return nil, false, err
			}

			return s.Search(ctx, req, reqProviders)
		}

		// Find data from provider
		areaCache, err := s.hotelAreaCacheRepo.FindByArea(ctx, req.Place.PlaceID, req.Place.Type)
		if err != nil {
			log.Error("s.hotelAreaCacheRepo.FindByArea error", log.Any("error", err), log.Any("req", req))
			return nil, false, err
		}

		areaCache, err = s.filterInActiveHotels(ctx, areaCache, reqProviders)
		if err != nil {
			log.Error("s.filterInActiveHotels error", log.Any("error", err), log.Any("areaCache", areaCache))
			return nil, false, err
		}

		if firstBatchSize == 20 && req.Pagination.PageCurrent == 1 {
			shouldWaitFullData = false

			getOutputSearchReq := &getOutputSearchReq{
				batchSize:        int(firstBatchSize),
				shouldGetFull:    shouldWaitFullData,
				providers:        missingProviders,
				areaCacheItem:    areaCache,
				searchKey:        searchKey,
				isFirstBatchOnly: true,
				timeout:          3,
			}

			var dataProviderMapTemp map[commonEnum.HotelProvider][]*domain.HotelSummary

			dataProviderMapTemp, loadMore, _, err = s.getOutputSearchHotels(ctx, req, getOutputSearchReq, false)
			if err != nil {
				log.Error("s.getOutputSearchForBatchHotel error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
				return nil, false, err
			}

			for provider, data := range dataProviderMapTemp {
				dataProviderMap[provider] = data
			}
		}

		if loadMore || shouldWaitFullData {
			waitChanFullData := make(chan map[commonEnum.HotelProvider][]*domain.HotelSummary)
			// var err error

			// Get full & cache
			go func() {
				bgCtx, cc := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
				defer cc()

				var fullDataMap map[commonEnum.HotelProvider][]*domain.HotelSummary

				defer func() {
					if shouldWaitFullData {
						waitChanFullData <- fullDataMap
					}
				}()

				if areaCache == nil {
					areaCache, err = s.hotelAreaCacheRepo.FindByArea(bgCtx, req.Place.PlaceID, req.Place.Type)
					if err != nil {
						log.Error("s.hotelAreaCacheRepo.FindByArea error", log.Any("error", err), log.Any("req", req))
						return
					}
				}

				getOutputSearchReq := &getOutputSearchReq{
					batchSize:        int(firstBatchSize),
					shouldGetFull:    shouldWaitFullData,
					providers:        missingProviders,
					areaCacheItem:    areaCache,
					searchKey:        searchKey,
					isFirstBatchOnly: false,
					timeout:          5,
				}

				fullDataMap, _, _, err = s.getOutputSearchHotels(bgCtx, req, getOutputSearchReq, false)
				if err != nil {
					log.Error("s.getOutputSearchFull error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
					s.redisLock.ReleaseLock(searchKey)

					return
				}

				if len(fullDataMap) > 0 {
					for key, val := range fullDataMap {
						fullDataMap[key] = simpleFilterHotels(val, req)
					}

					go func(data map[commonEnum.HotelProvider][]*domain.HotelSummary) {
						defer s.redisLock.ReleaseLock(searchKey)

						// // TA Hotels need not to be cached
						// delete(data, enum.HotelProviderHNHTravelAgent)
						// delete(data, enum.HotelProviderMayTravelAgent)

						if len(data) == 0 {
							log.Error("Prepare cache search results empty data error")
							return
						}

						cacheCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
						defer cancel()

						s.cacheSearchResults(cacheCtx, searchKey, data)
					}(fullDataMap)
				} else {
					s.redisLock.ReleaseLock(searchKey)
				}
			}()

			if shouldWaitFullData {
				dataProviderMapTemp := <-waitChanFullData

				for provider, data := range dataProviderMapTemp {
					dataProviderMap[provider] = data
				}
				//	if err != nil {
				//		return nil, false, err
				//	}
			}
		} else {
			s.redisLock.ReleaseLock(searchKey)
		}
	}

	data := s.ProcessProviderDataMap(ctx, req.PartnershipID, req.OfficeID, uint32(req.Stay.RoomCount*req.Stay.DayCount), dataProviderMap, req.PriceConditionConfig)

	data = simpleFilterHotels(data, req)

	if loadMore && totalCount != 0 {
		req.Pagination.TotalRecord = int64(totalCount)
	}

	if len(data) != 0 {
		err := s.filterFirstHotelPrice(ctx, data[0])
		if err != nil {
			log.Error("filterFirstHotelPrice error", log.Any("error", err), log.String("hotelID", data[0].ID))
			return nil, false, err
		}
	}

	return data, loadMore, nil
}

func (s *searchHotelService) ProcessProviderDataMap(ctx context.Context, partnershipID, officeID string, multiplier uint32, dataMap map[commonEnum.HotelProvider][]*domain.HotelSummary, config *domain.HotelPriceConditionConfig) []*domain.HotelSummary {
	var defaultHotel *domain.HotelSummary
	var wg sync.WaitGroup

	if len(dataMap[commonEnum.HotelProviderNone]) > 0 {
		defaultHotel = dataMap[commonEnum.HotelProviderNone][0]
		delete(dataMap, commonEnum.HotelProviderNone)
	}

	outMap := map[string][]*domain.HotelSummary{}

	for p, val := range dataMap {
		var exRateItem *domain.CurrencyExchange

		for _, hotelSum := range val {
			if hotelSum != nil && hotelSum.Price != nil {
				var err error
				var rate float64

				if exRateItem != nil && exRateItem.Rate != 0 && exRateItem.From == hotelSum.Price.Currency {
					rate = exRateItem.Rate
				}

				hotelSum.Price, exRateItem, err = s.currencyExSvc.ConvertHotelSummaryPrice(ctx, hotelSum.Price, constants.VNDCurrency, rate)
				if err != nil {
					log.Error("currencyExSvc.ConvertHotelSummaryPrice err", log.Any("err", err))
					return nil
				}
			}

			hotelSum.Provider = p
		}
	}

	for p, val := range dataMap {
		wg.Add(1)

		go func(provider commonEnum.HotelProvider, hotelSummaries []*domain.HotelSummary) {
			defer wg.Done()

			err := s.hiddenFeeSvc.CalculateHotelSearchPrices(ctx, &domain.CalculateHotelSearchPricesReq{
				Multiplier:     multiplier,
				PartnershipID:  partnershipID,
				OfficeID:       officeID,
				HotelSummaries: hotelSummaries,
				Provider:       provider,
			})

			if err != nil {
				log.Error("hiddenFeeSvc.CalculateHotelSearchPrices err", log.Any("err", err), log.Any("provider", provider))
			}
		}(p, val)
	}

	wg.Wait()

	for _, val := range dataMap {
		for _, hotelSum := range val {
			outMap[hotelSum.ID] = append(outMap[hotelSum.ID], hotelSum)
		}
	}

	out := make([]*domain.HotelSummary, 0, len(outMap)+1)

	if defaultHotel != nil && len(outMap[defaultHotel.ID]) == 0 {
		out = append(out, defaultHotel)
	}

	temp := s.FilterByPriceConditionConfig(ctx, outMap, config)
	out = append(out, temp...)

	return out
}

func (s *searchHotelService) FilterByPriceConditionConfig(_ context.Context, dataMap map[string][]*domain.HotelSummary, config *domain.HotelPriceConditionConfig) []*domain.HotelSummary {
	out := make([]*domain.HotelSummary, 0, len(dataMap))

	for _, iHotels := range dataMap {
		hotels := lo.Filter(iHotels, func(item *domain.HotelSummary, _ int) bool { return item != nil && item.Price != nil })

		if len(hotels) == 0 {
			continue
		}

		if len(hotels) == 1 {
			out = append(out, hotels[0])
			continue
		}

		sort.Slice(hotels, func(i, j int) bool {
			return hotels[i].Price.PricePerNight.DiscountPrice < hotels[j].Price.PricePerNight.DiscountPrice
		})

		if !config.Active {
			out = append(out, hotels[0])
			continue
		}

		temp := config.SelectHotelProviderByOrder(hotels)
		if temp != nil {
			out = append(out, temp)
		}
	}

	return out
}

func (s *searchHotelService) TestFilterPrice() {
	dataMap := map[string][]*domain.HotelSummary{
		"123": {
			{
				Price: &domain.Price{
					PricePerNight: &domain.PricePerNight{
						DiscountPrice: 1000000,
					},
				},
				Provider: commonEnum.HotelProviderExpedia,
			},
			{
				Price: &domain.Price{
					PricePerNight: &domain.PricePerNight{
						DiscountPrice: 1000000,
					},
				},
				Provider: commonEnum.HotelProviderRateHawk,
			},
			{
				Price: &domain.Price{
					PricePerNight: &domain.PricePerNight{
						DiscountPrice: 1000000,
					},
				},
				Provider: commonEnum.HotelProviderDida,
			},
			{
				Price: &domain.Price{
					PricePerNight: &domain.PricePerNight{
						DiscountPrice: 1000000,
					},
				},
				Provider: commonEnum.HotelProviderExpedia,
			},
			{
				Price: &domain.Price{
					PricePerNight: &domain.PricePerNight{
						DiscountPrice: 1210000,
					},
				},
				Provider: commonEnum.HotelProviderTA,
			},
			{
				Price: &domain.Price{
					PricePerNight: &domain.PricePerNight{
						DiscountPrice: 1200000,
					},
				},
				Provider: commonEnum.HotelProviderHNHTravelAgent,
			},
			{
				Price: &domain.Price{
					PricePerNight: &domain.PricePerNight{
						DiscountPrice: 1200000,
					},
				},
				Provider: commonEnum.HotelProviderAgoda,
			},
		},
	}

	config := &domain.HotelPriceConditionConfig{
		ProviderPriceRange: map[int64]domain.HotelPriceConditionDetail{
			11: {
				Percent: 0.017,
			},
		},
		ProviderOrder: []int64{14, 16, 11, 17, 18},
		Active:        true,
	}

	result := s.FilterByPriceConditionConfig(context.Background(), dataMap, config)

	utils.WriteStructToJSONFile(result, "./logs/price_condition_test.json")
}

func simpleFilterHotels(hotels []*domain.HotelSummary, req *domain.HubSearchHotelRequest) []*domain.HotelSummary {
	listHotelWithPrices := []*domain.HotelSummary{}

	for index, hotel := range hotels {
		if req.Place.Type == enum.PlaceTypeHotel && index == 0 {
			listHotelWithPrices = append(listHotelWithPrices, hotel)
			continue
		}

		if hotel != nil && hotel.Price != nil {
			listHotelWithPrices = append(listHotelWithPrices, hotel)
		}
	}

	return listHotelWithPrices
}

type getOutputSearchReq struct {
	batchSize        int
	shouldGetFull    bool
	providers        []commonEnum.HotelProvider
	areaCacheItem    *domain.HotelAreaCacheItem
	searchKey        string
	isFirstBatchOnly bool
	timeout          int
}

func (s *searchHotelService) getOutputSearchHotels(ctx context.Context, req *domain.HubSearchHotelRequest, getReq *getOutputSearchReq, isSearchList bool) (map[commonEnum.HotelProvider][]*domain.HotelSummary, bool, int, error) { //nolint
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelService.getOutputSearchHotels")
	defer span.End()

	areaCacheItem := getReq.areaCacheItem
	batchSize := getReq.batchSize
	providers := getReq.providers
	firstBatchOnly := getReq.isFirstBatchOnly
	loadMore := false

	var hotels []*domain.Hotel
	hotelImageMap := map[string]*domain.HotelSummaryImage{}

	var err error
	providerPricesMap := map[commonEnum.HotelProvider][]*domain.HotelSummary{}
	var totalCount int

	if !isSearchList && areaCacheItem != nil {
		areaCacheItem, err = s.filterInActiveHotels(ctx, areaCacheItem, providers)
		if err != nil {
			log.Error("s.filterInActiveHotels error", log.Any("error", err), log.Any("areaCacheItem", areaCacheItem))
			return nil, false, 0, err
		}

		totalCount = len(areaCacheItem.Hotels)
		hIds := make([]string, 0, totalCount)
		providerIdsMap := map[commonEnum.HotelProvider][]string{}
		hotelDistanceMap := map[string]float64{}

		// case page 1 and selection hotel not in promo list
		if firstBatchOnly && areaCacheItem.AreaType == enum.PlaceTypeHotel {
			hIds = append(hIds, areaCacheItem.AreaID)
			for _, hotel := range areaCacheItem.Hotels {
				if hotel.HotelID == areaCacheItem.AreaID {
					for _, provider := range providers {
						if providerID, ok := hotel.ProviderIds[provider]; ok && providerID != "" {
							providerIdsMap[provider] = append(providerIdsMap[provider], providerID)
						}
					}
				}
			}
		}

		for _, hotel := range areaCacheItem.Hotels {
			found := false

			// skip selection hotel
			if hotel.HotelID == areaCacheItem.AreaID && areaCacheItem.AreaType == enum.PlaceTypeHotel {
				continue
			}

			for _, provider := range providers {
				if providerID, ok := hotel.ProviderIds[provider]; ok && providerID != "" {
					providerIdsMap[provider] = append(providerIdsMap[provider], providerID)
					found = true
				}
			}

			if found {
				hIds = append(hIds, hotel.HotelID)
				hotelDistanceMap[hotel.HotelID] = hotel.HotelDistance
			}

			if len(providers) == 1 && len(providerIdsMap[providers[0]]) < batchSize {
				continue
			}

			if firstBatchOnly && len(hIds) >= batchSize {
				break
			}
		}

		if firstBatchOnly && len(areaCacheItem.Hotels) > len(hIds) {
			loadMore = true
		}

		wait := make(chan struct{})
		go func() {
			defer close(wait)

			// Find provider hotelIDs
			if len(providerIdsMap) > 0 {
				providerPricesMap = s.prepareSearchHotelPrice(ctx, providerIdsMap, req, getReq.timeout, getReq.searchKey)
			}
		}()

		isRequestFullImage := req.ImageMode == enum.ImageModeTypeFull

		languageHotels, err := s.hotelRepo.FindByHotelIDsWithDefault(ctx, hIds, req.Language, req.ExcludeContent, isRequestFullImage)
		if err != nil {
			log.Error("s.hotelRepo.FindByHotelIDsWithDefault error", log.Any("error", err), log.Any("req", req))
			return nil, false, 0, err
		}

		hotels = utils.GetHotelContent(languageHotels, req.Language, hIds)

		// Sort to keep order
		hotelIndexMap := map[string]int{}
		for i, val := range hIds {
			hotelIndexMap[val] = i
		}

		for _, hotel := range hotels {
			hotel.Distance = hotelDistanceMap[hotel.HotelID]
		}

		sort.Slice(hotels, func(i, j int) bool {
			return hotelIndexMap[hotels[i].HotelID] < hotelIndexMap[hotels[j].HotelID]
		})

		<-wait
	} else {
		maxHotelLimit := 1000
		if firstBatchOnly {
			maxHotelLimit = batchSize + 1
		}

		var fullHotels []*domain.Hotel

		if isSearchList {
			fullHotels, hotelImageMap, err = s.SearchHotelByHotelIDs(ctx, req, getReq.searchKey)
			if err != nil {
				log.Error("s.SearchHotelByHotelId error", log.Any("error", err), log.Any("req", req))
				return nil, false, 0, err
			}
		} else {
			fullHotels, err = s.SearchHotelByPlace(ctx, req, maxHotelLimit, getReq.searchKey, firstBatchOnly)
			if err != nil {
				log.Error("s.SearchHotelByPlace error", log.Any("error", err), log.Any("req", req))
				return nil, false, 0, err
			}

			if !firstBatchOnly {
				go s.cachingAreaHotels(req.Place.PlaceID, req.Place.Type, fullHotels)
			}
		}

		if len(fullHotels) > batchSize {
			loadMore = true
			fullHotels = fullHotels[:batchSize]
		}

		hotels = fullHotels
	}

	hotelsWithContent, err := converts.FromHubHotelToHotelSummaries(hotels, hotelImageMap, req.Place, providers)
	if err != nil {
		log.Error("converts.FromHubHotelToHotelSummaries error", log.Any("error", err), log.Any("provider", providers), log.Any("req", req))
		return nil, false, 0, err
	}

	if len(providerPricesMap) == 0 {
		providerIdsMap := map[commonEnum.HotelProvider][]string{}

		// Find provider hotelIDs
		for _, hotel := range hotels {
			for _, p := range providers {
				if providerID, ok := hotel.ProviderIds[p]; ok && providerID != "" {
					providerIdsMap[p] = append(providerIdsMap[p], providerID)
				}
			}
		}

		providerPricesMap = s.prepareSearchHotelPrice(ctx, providerIdsMap, req, getReq.timeout, getReq.searchKey)
	}

	if !isSearchList && req.Place.Type == enum.PlaceTypeHotel && len(hotelsWithContent) > 0 {
		providerPricesMap[commonEnum.HotelProviderNone] = []*domain.HotelSummary{hotelsWithContent[0]}
	}

	s.mappingContentToPricesMap(hotelsWithContent, providerPricesMap)

	return providerPricesMap, loadMore, totalCount, nil
}

func (s *searchHotelService) expediaSearchHotel(ctx context.Context, req *domain.HubSearchHotelRequest, propertyIds []string, searchKey string) ([]*domain.HotelSummary, error) {
	searchReq := &domain.SearchHotelRequestData{
		ProviderHotelIds: propertyIds,
		SearchReq: &domain.HubCheckAvailabilityReq{
			Stay:          req.Stay,
			Occupancies:   req.Occupancies,
			CountryCode:   "VN",
			Language:      req.Language,
			SaleChannel:   req.SaleChannel,
			TravelPurpose: req.TravelPurpose,
			RatePlanCount: 1,
			SalesEnv:      req.SalesEnv,
		},
	}

	searchResult, err := s.expediaAdapter.SearchHotel(ctx, searchReq, searchKey)
	if err != nil {
		log.Error("s.expediaSearchHandler.SearchHotel error", log.Any("error", err))
		return nil, err
	}

	if searchResult == nil {
		log.Error("s.expediaSearchHandler.SearchHotel search empty", log.Any("error", err))
		return nil, domain.ErrRoomSoldOut
	}

	out := []*domain.HotelSummary{}

	for _, item := range searchResult.Hotels {
		searchHotelData := converts.FromHubHotelToHotelSummary(item, req.Stay.DayCount, commonEnum.HotelProviderExpedia)
		out = append(out, searchHotelData)
	}

	return out, nil
}

func (s *searchHotelService) hnhTASearchHotel(ctx context.Context, req *domain.HubSearchHotelRequest, propertyIds []string, provider commonEnum.HotelProvider, searchKey string) ([]*domain.HotelSummary, error) {
	searchReq := &domain.SearchHotelRequestData{
		ProviderHotelIds: propertyIds,
		SearchReq: &domain.HubCheckAvailabilityReq{
			Stay:          req.Stay,
			Occupancies:   req.Occupancies,
			CountryCode:   "VN",
			Language:      req.Language,
			SaleChannel:   req.SaleChannel,
			TravelPurpose: req.TravelPurpose,
			RatePlanCount: 1,
			SalesEnv:      req.SalesEnv,
		},
	}

	searchResult, err := s.hnhTravelAgentAdapter.SearchHotel(ctx, searchReq, provider, searchKey)
	if err != nil {
		log.Error("s.expediaSearchHandler.SearchHotel error", log.Any("error", err))
		return nil, err
	}

	if searchResult == nil {
		log.Error("s.expediaSearchHandler.SearchHotel search empty", log.Any("error", err))
		return nil, domain.ErrRoomSoldOut
	}

	out := []*domain.HotelSummary{}

	for _, item := range searchResult {
		searchHotelData := converts.FromHubHotelToHotelSummary(item, req.Stay.DayCount, provider)
		out = append(out, searchHotelData)
	}

	return out, nil
}

func (s *searchHotelService) cachingAreaHotels(placeID string, placeType enum.PlaceType, hotels []*domain.Hotel) {
	bgCtx, cc := context.WithTimeout(context.Background(), constants.DefaultCtxTimeout)
	defer cc()

	cacheItem := &domain.HotelAreaCacheItem{
		Hotels:   []domain.AreaCacheHotelInfo{},
		AreaType: placeType,
		AreaID:   placeID,
	}

	for _, hotel := range hotels {
		cacheItem.Hotels = append(cacheItem.Hotels, domain.AreaCacheHotelInfo{
			HotelID:       hotel.HotelID,
			ProviderIds:   hotel.ProviderIds,
			HotelDistance: hotel.Distance,
		})
	}

	if placeType == enum.PlaceTypeHotel && len(cacheItem.Hotels) == 1 {
		return
	}

	if err := s.hotelAreaCacheRepo.InsertOne(bgCtx, cacheItem); err != nil {
		log.Error("s.hotelAreaCacheRepo.InsertOne error", log.Any("error", err), log.Any("cacheItem", cacheItem))
	}
}

// SUB Hub chỉ có một provider duy nhất là Hub Provider, không cần xử lý multiple providers.
func (s *searchHotelService) handleSubHubSearch(
	ctx context.Context,
	req *domain.HubSearchHotelRequest,
	reqProviders []commonEnum.HotelProvider,
) ([]*domain.HotelSummary, bool, error) {
	// Forward request to MAIN Hub Provider (SUB Hub chỉ có provider Hub duy nhất)
	tempRequestCurrency := req.RequestCurrency
	req.RequestCurrency = ""

	response, err := s.hubAdapter.SearchHotel(ctx, req)
	if err != nil {
		log.Error("SUB Hub search failed",
			log.String("error", err.Error()),
		)

		return nil, false, fmt.Errorf("SUB Hub search failed: %w", err)
	}

	// Set back request currency
	req.RequestCurrency = tempRequestCurrency

	// Process response qua price service cho SUB Hub partnership
	processedHotels, err := s.processSubHubPricing(ctx, response.HotelSummary, req)
	if err != nil {
		log.Error("SUB Hub price processing failed",
			log.String("error", err.Error()),
		)

		return nil, false, fmt.Errorf("SUB Hub price processing failed: %w", err)
	}

	// Determine load more based on pagination
	loadMore := false
	if response.Pagination != nil {
		loadMore = response.Pagination.PageCurrent < response.Pagination.TotalPage
	}

	return processedHotels, loadMore, nil
}

// processSubHubPricing xử lý pricing cho SUB Hub partnership sau khi nhận response từ MAIN Hub.
func (s *searchHotelService) processSubHubPricing(
	ctx context.Context,
	hotels []*domain.HotelSummary,
	req *domain.HubSearchHotelRequest,
) ([]*domain.HotelSummary, error) {
	if len(hotels) == 0 {
		return hotels, nil
	}

	multiplier := uint32(req.Stay.RoomCount * req.Stay.DayCount)

	err := s.hiddenFeeSvc.CalculateHotelSearchPrices(ctx, &domain.CalculateHotelSearchPricesReq{
		Multiplier:     multiplier,
		PartnershipID:  req.PartnershipID, // SUB Hub partnership ID
		OfficeID:       req.OfficeID,      // SUB Hub office ID
		HotelSummaries: hotels,
	})
	if err != nil {
		log.Error("Hidden fee calculation failed for SUB Hub",
			log.Any("error", err),
			log.String("partnership_id", req.PartnershipID),
		)
	}

	return hotels, nil
}
