package service

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/price"
	redisRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type HiddenFeeService interface {
	CalculateHotelSearchPrices(ctx context.Context, req *domain.CalculateHotelSearchPricesReq) error
	CalculateHotelDetailPrices(ctx context.Context, req *domain.CalculateHotelDetailPricesReq) error
	CalculateHotelRateDataPrices(ctx context.Context, req *domain.CalculateHotelRateDataPricesReq) error
}

type hiddenFeeService struct {
	priceClient price.PriceClient
	redisRepo   redisRepo.HiddenServiceFeeRepository
}

func NewHiddenFeeService(
	priceClient price.PriceClient,
	redisRepo redisRepo.HiddenServiceFeeRepository,
) HiddenFeeService {
	return &hiddenFeeService{
		priceClient,
		redisRepo,
	}
}

func (r *hiddenFeeService) CalculateHotelSearchPrices(ctx context.Context, req *domain.CalculateHotelSearchPricesReq) error {
	if req == nil {
		return errors.ErrInvalidInput
	}

	err := r.priceClient.CalculateHotelSearchPrices(ctx, req)
	if err != nil {
		log.Error("CalculateHotelSearchPrices err", log.Any("err", err), log.Any("officeID", req.OfficeID))
		return err
	}

	return nil
}

func (r *hiddenFeeService) CalculateHotelDetailPrices(ctx context.Context, req *domain.CalculateHotelDetailPricesReq) error {
	if req == nil {
		return errors.ErrInvalidInput
	}

	err := r.priceClient.CalculateHotelDetailPrices(ctx, req)
	if err != nil {
		log.Error("CalculateHotelDetailPrices err", log.Any("err", err), log.Any("officeID", req.OfficeID))
		return err
	}

	return nil
}

func (r *hiddenFeeService) CalculateHotelRateDataPrices(ctx context.Context, req *domain.CalculateHotelRateDataPricesReq) error {
	if req == nil {
		return nil
	}

	err := r.priceClient.CalculateHotelRateDataPrices(ctx, req)
	if err != nil {
		log.Error("CalculateHotelRateDataPrices err", log.Any("err", err), log.Any("officeID", req.OfficeID))
		return err
	}

	return nil
}
