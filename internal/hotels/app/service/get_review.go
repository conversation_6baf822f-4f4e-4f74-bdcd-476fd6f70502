package service

import (
	"context"
	"fmt"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type GetReviewService interface {
	GetHotelReview(ctx context.Context, req *domain.HubHotelReviewReq) ([]*domain.HubHotelReviewItem, error)
}

type getReviewService struct {
	cfg            *config.Schema
	expediaAdapter expedia_client.ExpediaAdapter
}

func NewGetReviewService(
	cfg *config.Schema,
	expediaAdapter expedia_client.ExpediaAdapter,
) GetReviewService {
	return &getReviewService{
		cfg:            cfg,
		expediaAdapter: expediaAdapter,
	}
}

func (f *getReviewService) GetHotelReview(ctx context.Context, req *domain.HubHotelReviewReq) ([]*domain.HubHotelReviewItem, error) {
	if req == nil {
		log.Error("getHotelGetReviewRes input nil", log.Any("req", req))
		return nil, errors.ErrInvalidInput
	}

	if req.Language == "" {
		req.Language = constants.DefaultLanguage
	}

	reviews := []*domain.HubHotelReviewItem{}
	var err error

	switch req.Provider {
	case commonEnum.HotelProviderExpedia:
		reviews, err = f.expediaAdapter.GetReviews(ctx, req, fmt.Sprintf("%s_%s", req.HotelID, req.Language))
	}

	if err != nil {
		log.Error("GetHotelReview err", log.Any("err", err), log.Any("provider", req.Provider))
		return nil, err
	}

	return reviews, nil
}
