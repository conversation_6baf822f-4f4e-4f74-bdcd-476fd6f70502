package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/tracing"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.opentelemetry.io/otel/codes"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	redisLock "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type CheckAvailabilityService interface {
	Search(
		ctx context.Context,
		req *domain.HubCheckAvailabilityReq,
		reqProviders []commonEnum.HotelProvider,
	) (
		_ *domain.HubCheckAvailabilityRes,
		_ []commonEnum.HotelProvider,
		err error,
	)
}

type checkAvailabilityService struct {
	cfg              *config.Schema
	searchHotelsRepo repositories.ProviderSearchHotelsRepository
	searchHandler    *ProviderSearchHandlerImp
	redisLock        redisLock.CheckAvailabilityRepository
	hotelRepo        repositories.HotelRepository
	roomRepo         repositories.RoomRepository
	hubAdapter       hub_provider.HubAdapter // Hub Provider adapter cho SUB Hub
	hiddenFeeSvc     HiddenFeeService        // Price service cho SUB Hub pricing
}

func NewCheckAvailabilityService(
	cfg *config.Schema,
	searchHotelsRepo repositories.ProviderSearchHotelsRepository,
	searchHandler *ProviderSearchHandlerImp,
	redisRepo redisLock.SearchHotelsRepository,
	hotelRepo repositories.HotelRepository,
	roomRepo repositories.RoomRepository,
	hubAdapter hub_provider.HubAdapter,
	hiddenFeeSvc HiddenFeeService,
) CheckAvailabilityService {
	return &checkAvailabilityService{
		cfg:              cfg,
		searchHotelsRepo: searchHotelsRepo,
		searchHandler:    searchHandler,
		redisLock:        redisRepo,
		hotelRepo:        hotelRepo,
		roomRepo:         roomRepo,
		hubAdapter:       hubAdapter,
		hiddenFeeSvc:     hiddenFeeSvc,
	}
}

func (s *checkAvailabilityService) validateCheckAvailabilityRequest(req *domain.HubCheckAvailabilityReq) error {
	if req == nil {
		return commonErrs.ErrInvalidInput
	}

	roomCount := 0
	for _, room := range req.Occupancies {
		roomCount += int(room.Rooms)
	}

	if roomCount > constants.MaxRoomSearchAllowed {
		return domain.ErrInvalidRequestRoomAmount
	}

	checkIn, err := time.Parse(constants.HubSearchDateFormat, req.Stay.CheckIn)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-in", req.Stay.CheckIn))
		return domain.ErrCheckInDateInvalid
	}

	checkOut, err := time.Parse(constants.HubSearchDateFormat, req.Stay.CheckOut)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-out", req.Stay.CheckOut))
		return domain.ErrCheckOutDateInvalid
	}

	limitDate := time.Now().Add(365 * 24 * time.Hour) // 1 year

	if checkIn.Before(time.Now().Add(-24*time.Hour)) || checkIn.After(limitDate) {
		return domain.ErrCheckInDateInvalid
	}

	if checkOut.Before(checkIn) || checkOut.Before(time.Now()) || checkOut.After(limitDate) {
		return domain.ErrCheckOutDateInvalid
	}

	return nil
}

func (s *checkAvailabilityService) getEnableProviders(req []commonEnum.HotelProvider) map[commonEnum.HotelProvider]ProviderSearchHandler {
	providerSearchHandlersMap := map[commonEnum.HotelProvider]ProviderSearchHandler{}

	for _, p := range req {
		providerSearchHandlersMap[p] = s.searchHandler.getHandler(p)
	}

	return providerSearchHandlersMap
}

func (s *checkAvailabilityService) cacheSearchResults(ctx context.Context, searchKey string, data map[commonEnum.HotelProvider]*domain.HotelSearchResult) {
	cacheData := []*domain.HotelSearchResult{}

	for key, val := range data {
		val.ID = primitive.NewObjectID().Hex()
		val.Provider = key
		val.SearchKey = searchKey
		cacheData = append(cacheData, val)
	}

	if err := s.searchHotelsRepo.InsertMany(ctx, cacheData); err != nil {
		log.Error("searchHotelsRepo.InsertMany error", log.Any("error", err))
		return
	}
}

func (s *checkAvailabilityService) ToHubHotelResponse(in map[commonEnum.HotelProvider]*domain.HotelSearchResult) (*domain.HubCheckAvailabilityRes, []commonEnum.HotelProvider, error) {
	hotelRes := []*domain.HubHotel{}
	providers := []commonEnum.HotelProvider{}

	for provider, result := range in {
		if result != nil && len(result.Hotels) > 0 {
			providers = append(providers, provider)
			hotelRes = append(hotelRes, result.Hotels...)
			// for _, hotel := range result.Hotels {
			// if provider == commonEnum.HotelProviderTourMind {
			// 	seenTourmindHotel[hotel.HotelID] = true
			// } else if seenTourmindHotel[hotel.HotelID] {
			// 	continue
			// }
			// TODO merge hotel + room for comparing price
			// }
		}
	}

	return &domain.HubCheckAvailabilityRes{
		IsSuccess:            true,
		Hotels:               hotelRes,
		AvailableHotelReturn: uint(len(hotelRes)),
	}, providers, nil
}

func (s *checkAvailabilityService) bindingContentDataToHotel(in *domain.HotelSearchResult, content []*domain.Hotel, provider commonEnum.HotelProvider, rooms map[string][]*domain.Room) {
	if in == nil {
		return
	}

	for _, hotelContent := range content {
		contentRooms := rooms[hotelContent.HotelID]

		for _, hotel := range in.Hotels {
			var hotelID string

			switch in.Provider {
			case commonEnum.HotelProviderExpedia, commonEnum.HotelProviderExpediaManual:
				hotelID = hotelContent.ProviderIds[commonEnum.HotelProviderExpedia]
			default:
				hotelID = hotelContent.ProviderIds[provider]
			}

			if hotel.ProviderHotelID == hotelID {
				hotel.HotelID = hotelContent.HotelID
				hotel.Name = hotelContent.Name
				hotel.NameEn = hotelContent.NameEn
				hotel.Address = hotelContent.Address

				if hotelContent.Ratings != nil &&
					hotelContent.Ratings.Property != nil &&
					strings.EqualFold(hotelContent.Ratings.Property.Type, "star") {
					rating, err := strconv.ParseFloat(hotelContent.Ratings.Property.Rating, 32)
					if err != nil {
						log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("value", hotelContent.Ratings.Property.Rating))
						rating = 0
					}
					hotel.Rating = rating
				}

				if hotelContent.CheckIn != nil {
					hotel.CheckInTime = hotelContent.CheckIn.BeginTime
				}

				if hotelContent.Checkout != nil {
					hotel.CheckOutTime = hotelContent.Checkout.Time
				}

				if hotelContent.Category != nil {
					hotel.HotelType = hotelContent.Category.Name
				}

				roomProviderIDMap := map[string]*domain.Room{}

				for _, contentRoom := range contentRooms {
					providerRoomID := contentRoom.ProviderIDs[provider]
					roomProviderIDMap[providerRoomID] = contentRoom
				}

				var isFlagMappingHasExtraBedFromContent, hasExtraBed, isNonSmoking bool

				for _, room := range hotel.ListRooms {
					contentRoom := roomProviderIDMap[room.ProviderRoomID]
					if contentRoom == nil {
						room.RoomID = "" // skip room not found in content.
						continue
					}

					if provider == commonEnum.HotelProviderExpedia {
						isFlagMappingHasExtraBedFromContent = true
						hasExtraBed, isNonSmoking = ExpediaCheckExtraBedAndNonSmoking(contentRoom)
					}

					if provider != commonEnum.HotelProviderAgoda {
						room.Name = contentRoom.Name
					}

					room.NameEn = contentRoom.NameEn
					room.RoomID = contentRoom.RoomID
					room.Views = contentRoom.Views
					room.Area = contentRoom.Area
					room.Amenities = contentRoom.Amenities
					room.Images = contentRoom.Images
					// room.BedGroups = contentRoom.BedGroups
					room.Occupancy = contentRoom.Occupancy
					room.Descriptions = contentRoom.Descriptions

					for _, rate := range room.RateData {
						if rate.ProviderRateID == "" {
							rate.ProviderRateID = rate.RateID
						}

						rate.RateID = fmt.Sprintf("%d%s", in.Provider, rate.RateID)
						if isFlagMappingHasExtraBedFromContent {
							rate.HasExtraBed = hasExtraBed
							rate.NonSmoking = isNonSmoking
						}

						if provider == commonEnum.HotelProviderHNHTravelAgent || provider == commonEnum.HotelProviderMayTravelAgent {
							for _, bedOption := range rate.BedOptions {
								for _, group := range contentRoom.BedGroups {
									if bedOption.OptionID == group.ID {
										bedOption.Name = group.Description
										bedOption.BedConfigs = group.Configuration
									}
								}
							}
						}

						// vì agoda không có bed options nên trả mặc định
						if provider == commonEnum.HotelProviderAgoda {
							rate.BedOptions = append(rate.BedOptions, &domain.BedOption{
								OptionID: "999999",
								Name:     "",
								Quantity: 1,
								BedConfigs: []*domain.BedConfiguration{
									{
										Quantity: 1,
										Size:     "unknown",
										Type:     "unknown",
									},
								},
							})

							roomCount := len(in.HotelSearchRequest.Occupancies)
							if roomCount > 0 {
								totalPeople, adultCount, childCount := ProcessOccupancies(roomCount, in.HotelSearchRequest.Occupancies)

								occ := &domain.ContentOccupancy{
									MaxAllowed: &domain.MaxAllowed{
										Total:    float64(totalPeople) / float64(roomCount),
										Adults:   float64(adultCount) / float64(roomCount),
										Children: float64(childCount) / float64(roomCount),
									},
								}

								room.Occupancy = occ
							}

						}
					}
				}

				hotel.ListRooms = lo.Filter(hotel.ListRooms, func(item *domain.HubRoom, _ int) bool {
					return item.RoomID != ""
				})

				break
			}
		}
	}
}

func ExpediaCheckExtraBedAndNonSmoking(room *domain.Room) (hasExtraBed bool, isNonSmoking bool) {
	if room == nil {
		return false, false
	}
	isNonSmoking = true

	for _, amenity := range room.Amenities {
		if amenity.ID == "**********" {
			hasExtraBed = true
		}

		if amenity.ID == "6212" {
			isNonSmoking = false
		}

		if hasExtraBed && isNonSmoking {
			break
		}
	}

	return hasExtraBed, isNonSmoking
}

func (s *checkAvailabilityService) Search(
	ctx context.Context,
	req *domain.HubCheckAvailabilityReq,
	reqProviders []commonEnum.HotelProvider,
) (
	_ *domain.HubCheckAvailabilityRes,
	_ []commonEnum.HotelProvider,
	err error,
) {
	// 1. Check if this is SUB Hub request - Forward to Hub Provider
	isSubHubRequest := ctx.Value(constants.IsSubHubRequestKey)
	if isSubHubRequest != nil && isSubHubRequest.(bool) {
		return s.handleSubHubCheckAvailability(ctx, req, reqProviders)
	}

	// 2. Normal MAIN Hub logic continues...
	var (
		wg    sync.WaitGroup
		mutex sync.Mutex
		lock  bool
	)

	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("Search GenSearchKey err", log.Any("err", err))
		return nil, nil, err
	}

	defer func() {
		if err != nil && lock {
			s.redisLock.ReleaseLock(searchKey)
		}
	}()

	data := map[commonEnum.HotelProvider]*domain.HotelSearchResult{}

	err = s.validateCheckAvailabilityRequest(req)
	if err != nil {
		return nil, nil, err
	}

	lock, err = s.redisLock.AcquireLock(searchKey)
	if err != nil {
		log.Error("AcquireLock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	if !lock {
		err = s.redisLock.WaitForLockToRelease(searchKey, time.Second, 70)
		if err != nil {
			log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, nil, commonErrs.ErrSomethingOccurred
		}
	}

	hotels, cachedProviders, err := s.SearchCached(ctx, req, reqProviders)
	if err != nil {
		log.Error("s.SearchCached error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
		return nil, nil, err
	}

	for _, hotel := range hotels {
		data[hotel.Provider] = hotel
	}

	if len(cachedProviders) == len(reqProviders) {
		defer s.redisLock.ReleaseLock(searchKey)

		response, responseProviders, err := s.ToHubHotelResponse(data)
		if err != nil {
			log.Error("ToHubHotelResponse error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, nil, err
		}

		response.SearchKey = searchKey

		return response, responseProviders, nil
	}

	var missingProviders []commonEnum.HotelProvider

	for _, provider := range reqProviders {
		if !lo.Contains(cachedProviders, provider) {
			missingProviders = append(missingProviders, provider)
		}
	}

	// Modify searchHandler
	providerSearchHandlersMap := s.getEnableProviders(missingProviders)

	if req.Language == "" {
		req.Language = constants.DefaultLanguage
	}

	languageHotels, err := s.hotelRepo.FindByHotelIDsWithDefault(ctx, req.ListHotels, req.Language, false, true)
	if err != nil {
		log.Error("Search FindByHotelIDsWithDefault err", log.Any("err", err), log.Any("hotelIds", req.ListHotels))
		return nil, nil, domain.ErrPlaceNotFound
	}

	hotelContents := utils.GetHotelContent(languageHotels, req.Language, req.ListHotels)

	if len(hotelContents) == 0 {
		log.Error("Search hotelContent empty")
		return nil, nil, domain.ErrHotelPropertyNotFound
	}

	providersMapReqData, err := s.populateSearchRequestDataForProvider(ctx, req, hotelContents, reqProviders)
	if err != nil {
		return nil, nil, err
	}

	for iProvider, iHandler := range providerSearchHandlersMap {
		if iProvider == commonEnum.HotelProviderNone {
			continue
		}

		provider := iProvider
		handler := iHandler

		if provider == commonEnum.HotelProviderHNHTravelAgent {
			isHNHTravelAgentActive, err := isInActiveTime(s.cfg.HNHTravelAgentServiceActiveTime, s.cfg.HNHTravelAgentSkipDayOfWeek)
			if err != nil {
				log.Error("isInActiveTime error", log.Any("error", err), log.String("HNHTravelAgentServiceActiveTime", s.cfg.HNHTravelAgentServiceActiveTime))
				isHNHTravelAgentActive = false
			}

			if !isHNHTravelAgentActive {
				return &domain.HubCheckAvailabilityRes{
					IsSuccess: true,
					SearchKey: searchKey,
					Hotels:    []*domain.HubHotel{},
				}, nil, nil
			}
		}

		if provider == commonEnum.HotelProviderMayTravelAgent {
			isMayTravelAgentActive, err := isInActiveTime(s.cfg.MayTravelAgentServiceActiveTime, "")
			if err != nil {
				log.Error("isInActiveTime error", log.Any("error", err), log.String("MayTravelAgentServiceActiveTime", s.cfg.MayTravelAgentServiceActiveTime))
				isMayTravelAgentActive = false
			}

			if !isMayTravelAgentActive {
				return &domain.HubCheckAvailabilityRes{
					IsSuccess: true,
					SearchKey: searchKey,
					Hotels:    []*domain.HubHotel{},
				}, nil, nil
			}
		}

		if provider == commonEnum.HotelProviderBZTTravelAgent {
			isBZTTravelAgentActive, err := isInActiveTime(s.cfg.BZTTravelAgentServiceActiveTime, "")
			if err != nil {
				log.Error("isInActiveTime error", log.Any("error", err), log.String("BZTTravelAgentServiceActiveTime", s.cfg.BZTTravelAgentServiceActiveTime))
				isBZTTravelAgentActive = false
			}

			if !isBZTTravelAgentActive {
				return &domain.HubCheckAvailabilityRes{
					IsSuccess: true,
					SearchKey: searchKey,
					Hotels:    []*domain.HubHotel{},
				}, nil, nil
			}
		}

		providerData := providersMapReqData[provider]

		if providerData == nil || providerData.ProviderHotelIds == nil || providerData.SearchReq == nil {
			continue
		}

		wg.Add(1)

		go func() {
			defer wg.Done()

			ctxTimeout := time.Second * time.Duration(s.cfg.SearchHotelCtxTimeout)

			searchCtx, cc := context.WithTimeout(ctx, ctxTimeout)
			defer cc()

			searchCtx, span := tracing.StartSpanFromContext(searchCtx, fmt.Sprintf("searchHotelsService.Search.%s", commonEnum.HotelProviderName[provider]))
			defer span.End()
			tracing.AddAttributes(span, searchCtx, providerData, searchKey)

			var providerRF *domain.HotelSearchResult
			var err error

			providerRF, err = handler.search(searchCtx, providerData, searchKey)
			if err != nil {
				err, ok := reflect.ValueOf(err).Interface().(error)
				if ok {
					span.SetStatus(codes.Error, fmt.Sprintf("s.SearchHotelsService.Search.%s failed", commonEnum.HotelProviderName[provider]))
					span.RecordError(err)
				}

				log.Error("handler.search error", log.Any("error", err), log.Int("provider", int(provider)), log.String("searchKey", searchKey))

				return
			}

			providerRF.HotelSearchRequest = req.ToHotelSearchRequest()

			mutex.Lock()

			s.bindingContentDataToHotel(providerRF, hotelContents, provider, providerData.Rooms)
			err = req.Stay.CountDays()
			if err != nil {
				log.Error("req.Stay.CountDays error", log.Any("error", err))
				return
			}

			converts.CalcPayNowForSearchResult(providerRF, req.Stay)

			if req.Configs == nil || !req.Configs.IsOriginalRate {
				s.UpdateRateRefundable(providerRF)
			}

			data[provider] = providerRF
			mutex.Unlock()
		}()
	}

	wg.Wait()

	// Temporary lock and cache search results
	lock, err = s.redisLock.AcquireCachingLock(searchKey)
	if err != nil {
		log.Error("redisRepo.AcquireCachingLock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	if !lock {
		log.Error("redisRepo.AcquireCachingLock acquire lock error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	go func(data map[commonEnum.HotelProvider]*domain.HotelSearchResult) {
		defer s.redisLock.ReleaseLock(searchKey)
		defer s.redisLock.ReleaseCachingLock(searchKey)

		if len(data) == 0 {
			log.Error("Prepare cache search results empty data error")
			return
		}

		bgCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
		defer cancel()

		s.cacheSearchResults(bgCtx, searchKey, data)
	}(data)

	response, responseProviders, err := s.ToHubHotelResponse(data)
	if err != nil {
		log.Error("ToHubHotelResponse error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, err
	}

	response.SearchKey = searchKey

	return response, responseProviders, nil
}

func (s *checkAvailabilityService) SearchCached(
	ctx context.Context,
	req *domain.HubCheckAvailabilityReq,
	reqProviders []commonEnum.HotelProvider,
) (
	_ []*domain.HotelSearchResult,
	_ []commonEnum.HotelProvider,
	err error,
) {
	returnProviders := []commonEnum.HotelProvider{}

	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("GenSearchKey error", log.Any("error", err), log.Any("req", req))
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	cachedHotels, err := s.searchHotelsRepo.FindByKey(ctx, searchKey, reqProviders)
	if err != nil {
		log.Error("searchHotelsRepo.Find error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	for _, item := range cachedHotels {
		returnProviders = append(returnProviders, item.Provider)
	}

	returnProviders = lo.Uniq[commonEnum.HotelProvider](returnProviders)

	return cachedHotels, returnProviders, nil
}

func (s *checkAvailabilityService) populateSearchRequestDataForProvider(
	ctx context.Context,
	req *domain.HubCheckAvailabilityReq,
	hotelContents []*domain.Hotel,
	enableProvider []commonEnum.HotelProvider) (
	map[commonEnum.HotelProvider]*domain.SearchHotelRequestData, error,
) {
	out := map[commonEnum.HotelProvider]*domain.SearchHotelRequestData{}

	for _, hotel := range hotelContents {
		roomsMap, err := s.getProviderHotelRooms(ctx, enableProvider, hotel, req.Language) // temp use default language
		if err != nil {
			return nil, err
		}

		// Common logic for all
		for _, provider := range enableProvider {
			if hotel.ProviderIds[provider] != "" {
				if out[provider] == nil {
					out[provider] = &domain.SearchHotelRequestData{
						Hotels:           []*domain.Hotel{hotel},
						ProviderHotelIds: []string{hotel.ProviderIds[provider]},
						Rooms:            map[string][]*domain.Room{hotel.HotelID: roomsMap[provider]},
					}
				} else {
					out[provider].Hotels = append(out[provider].Hotels, hotel)
					out[provider].ProviderHotelIds = append(out[provider].ProviderHotelIds, hotel.ProviderIds[provider])
					out[provider].Rooms[hotel.HotelID] = roomsMap[provider]
				}
			}
		}
	}

	// Bind search request
	for key := range out {
		out[key].SearchReq = helpers.Copy(req).(*domain.HubCheckAvailabilityReq)
		out[key].SearchReq.ListHotels = out[key].ProviderHotelIds

		if key == commonEnum.HotelProviderHNHTravelAgent || key == commonEnum.HotelProviderMayTravelAgent {
			out[key].SearchReq.RatePlanCount = 250
		}
	}

	return out, nil
}

func (s *checkAvailabilityService) getProviderHotelRooms(ctx context.Context, providers []commonEnum.HotelProvider, hotel *domain.Hotel, language string) (map[commonEnum.HotelProvider][]*domain.Room, error) {
	languageRooms, err := s.roomRepo.FindByHotelIDsWithDefault(ctx, hotel.HotelID, language)
	if err != nil {
		log.Error("roomRepo.FindByRoomIDs error", log.Any("error", err), log.Any("hotel", hotel))
		return nil, err
	}

	rooms := utils.GetRoomContent(languageRooms, language)

	out := map[commonEnum.HotelProvider][]*domain.Room{}

	for _, provider := range providers {
		for _, room := range rooms {
			if room.ProviderIDs[provider] != "" {
				out[provider] = append(out[provider], room)
			}
		}
	}

	return out, nil
}

func (s *checkAvailabilityService) UpdateRateRefundable(items *domain.HotelSearchResult) {
	for _, hotel := range items.Hotels {
		for _, room := range hotel.ListRooms {
			allRefundable := true

			for _, rate := range room.RateData {
				if !rate.Refundable {
					allRefundable = false
					break
				}
			}

			if allRefundable {
				var minRate *domain.HubRateData
				for _, rate := range room.RateData {
					if minRate == nil || rate.PayNow < minRate.PayNow {
						minRate = rate
					}
				}

				if minRate != nil {
					clonedRate := *minRate
					clonedRate.RouteRateID = clonedRate.RateID
					clonedRate.RateID = fmt.Sprintf("%x", md5.Sum([]byte(uuid.NewString())))
					clonedRate.Refundable = false
					clonedRate.FakeNonRefund = true

					room.RateData = append(room.RateData, &clonedRate)
				}
			}
		}
	}
}

// SUB Hub chỉ có một provider duy nhất là Hub Provider, không cần xử lý multiple providers.
func (s *checkAvailabilityService) handleSubHubCheckAvailability(
	ctx context.Context,
	req *domain.HubCheckAvailabilityReq,
	_ []commonEnum.HotelProvider,
) (*domain.HubCheckAvailabilityRes, []commonEnum.HotelProvider, error) {
	tempRequestCurrency := req.RequestCurrency
	req.RequestCurrency = ""

	response, err := s.hubAdapter.CheckAvailability(ctx, req)
	if err != nil {
		log.Error("SUB Hub check availability failed",
			log.String("error", err.Error()),
		)

		return nil, nil, fmt.Errorf("SUB Hub check availability failed: %w", err)
	}
	// Set back request currency
	req.RequestCurrency = tempRequestCurrency

	// Return response with Hub provider
	return response, []commonEnum.HotelProvider{commonEnum.HotelProviderHub}, nil
}

func ProcessOccupancies(rooms int, occupancies []*domain.HubSearchOccupancy) (totalPeople, adultCount, childCount int) {
	if len(occupancies) == 0 {
		return 0, 0, 0
	}

	maxPeople := 0
	maxAdults := 0

	for _, occ := range occupancies {
		roomAdults := int(occ.Adults)
		roomChildren := 0
		if occ.Children != nil {
			roomChildren = int(occ.Children.Number)
		}

		totalPeople := roomAdults + roomChildren

		if totalPeople > maxPeople {
			maxPeople = totalPeople
		}
		if roomAdults > maxAdults {
			maxAdults = roomAdults
		}
	}

	childCount = maxPeople - maxAdults
	adultCount = maxAdults * rooms
	childCount = childCount * rooms

	return adultCount + childCount, adultCount, childCount
}
