package service

import (
	"context"
	"time"

	"github.com/google/uuid"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type SessionService interface {
	New(ctx context.Context, officeID string, provider commonEnum.HotelProvider, data *domain.HotelSessionData) (string, error)
	Verify(ctx context.Context, officeID, sessionID string) (bool, error)
	VerifyBulk(ctx context.Context, sessionIDs []string) (map[string]bool, error)
	Close(ctx context.Context, sessionID string) error
}

type sessionService struct {
	sessionRepo repositories.SessionRepository
}

func NewSessionService(sessionRepo repositories.SessionRepository) SessionService {
	return &sessionService{sessionRepo}
}

func (s *sessionService) VerifyBulk(ctx context.Context, sessionIDs []string) (map[string]bool, error) {
	out := map[string]bool{}

	sessions, err := s.sessionRepo.FindBySessionIDs(ctx, sessionIDs)
	if err != nil {
		log.Error("sessionRepo.FindBySessionIDs error", log.Any("error", err), log.Any("sessions", sessionIDs))
		return nil, err
	}

	for _, item := range sessions {
		out[item.SessionID] = item.ExpiredAt > time.Now().UnixMilli()
	}

	return out, nil
}

func (s *sessionService) Close(ctx context.Context, sessionID string) error {
	err := s.sessionRepo.UpdateExpireTime(ctx, sessionID, 0)
	if err != nil {
		log.Error("sessionRepo.UpdateExpireTime error", log.Any("error", err), log.String("sessionID", sessionID))
		return errors.ErrSomethingOccurred
	}

	return nil
}

func (s *sessionService) New(ctx context.Context, officeID string, provider commonEnum.HotelProvider, data *domain.HotelSessionData) (string, error) {
	req := &domain.HotelSession{
		SessionID: s.generateSessionID(),
		OfficeID:  officeID,
		ExpiredAt: time.Now().Add(constants.SessionExpireTime).UnixMilli(),
	}

	if data != nil {
		if data.ExpediaSessionInfo != nil {
			req.ExpediaSessionInfo = data.ExpediaSessionInfo
		}

		if data.TourmindSessionInfo != nil {
			req.TourmindSessionInfo = data.TourmindSessionInfo
		}

		if data.BasicSessionInfo != nil {
			req.BasicSessionInfo = data.BasicSessionInfo
		}

		if data.DidaSessionInfo != nil {
			req.DidaSessionInfo = data.DidaSessionInfo
		}

		if data.HubSessionInfo != nil {
			req.HubSessionInfo = data.HubSessionInfo
		}

		if data.AgodaSessionInfo != nil {
			req.AgodaSessionInfo = data.AgodaSessionInfo
		}
	}

	err := s.sessionRepo.InsertOne(ctx, req)
	if err != nil {
		log.Error("InsertOne error", log.Any("error", err), log.Any("req", req))
		return "", errors.ErrSomethingOccurred
	}

	return req.SessionID, nil
}

func (*sessionService) generateSessionID() string {
	return uuid.New().String()
}

func (s *sessionService) Verify(ctx context.Context, officeID, sessionID string) (bool, error) {
	session, err := s.sessionRepo.FindBySessionID(ctx, officeID, sessionID)
	if err != nil {
		log.Error("[s *sessionService] Verify error", log.Any("error", err), log.String("SessionID", sessionID), log.String("OfficeID", officeID))
		return false, errors.ErrSomethingOccurred
	}

	if session == nil {
		return false, nil
	}

	return true, nil
}
