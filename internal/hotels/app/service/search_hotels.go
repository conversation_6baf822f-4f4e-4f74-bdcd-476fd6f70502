package service

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/samber/lo"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/tracing"
	"golang.org/x/sync/errgroup"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	redisLock "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service/converts"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

type SearchHotelService interface {
	Search(
		ctx context.Context,
		req *domain.HubSearchHotelRequest,
		reqProviders []commonEnum.HotelProvider,
	) (
		_ []*domain.HotelSummary,
		_ bool,
		err error,
	)
	SearchList(
		ctx context.Context,
		req *domain.HubSearchHotelRequest,
		reqProviders []commonEnum.HotelProvider,
	) (
		_ []*domain.HotelSummary,
		_ bool,
		err error,
	)
	ApplyFilterRequest(_ context.Context, in []*domain.HotelSummary, filter *domain.SearchHotelFilterRequest) []*domain.HotelSummary
	ApplyPaging(_ context.Context, in []*domain.HotelSummary, paging *commonDomain.Pagination) []*domain.HotelSummary
	CalcFilterRequest(_ context.Context, in []*domain.HotelSummary) *domain.SearchHotelFilterResponse
	ApplySortRequest(_ context.Context, req *domain.HubSearchHotelRequest, in []*domain.HotelSummary) []*domain.HotelSummary
	FilterByPriceConditionConfig(_ context.Context, dataMap map[string][]*domain.HotelSummary, config *domain.HotelPriceConditionConfig) []*domain.HotelSummary
	ProcessProviderDataMap(ctx context.Context, partnershipID, officeID string, multiplier uint32, dataMap map[commonEnum.HotelProvider][]*domain.HotelSummary, config *domain.HotelPriceConditionConfig) []*domain.HotelSummary
}

type searchHotelService struct {
	cfg                   *config.Schema
	searchHotelsRepo      repositories.SearchHotelCachesRepository
	redisLock             redisLock.SearchHotelsRepository
	hotelRepo             repositories.HotelRepository
	roomRepo              repositories.RoomRepository
	regionRepo            repositories.RegionRepository
	expediaAdapter        expedia_client.ExpediaAdapter
	rateHawkAdapter       rate_hawk_client.RateHawkAdapter
	didaAdapter           dida.Adapter
	hotelAreaCacheRepo    repositories.HotelAreaCacheRepository
	currencyExSvc         CurrencyExchangeService
	hnhTravelAgentAdapter travel_agent_client.TravelAgentAdapter
	hiddenFeeSvc          HiddenFeeService
	hubAdapter            hub_provider.HubAdapter // NEW: Hub Provider adapter cho SUB Hub
	inActiveHotelsRepo    repositories.InActiveHotelsRepository
	agodaAdapter          agoda_client.AgodaAdapter
	promoHotelRepo        repositories.HotelPrioritiesRepository
	placeRepo             repositories.RequestPlaceRepository
	hotelImageRepo        repositories.DisplayImageRepository
}

func NewSearchHotelService(
	cfg *config.Schema,
	searchHotelsRepo repositories.SearchHotelCachesRepository,
	redisRepo redisLock.SearchHotelsRepository,
	hotelRepo repositories.HotelRepository,
	roomRepo repositories.RoomRepository,
	regionRepo repositories.RegionRepository,
	expediaAdapter expedia_client.ExpediaAdapter,
	rateHawkAdapter rate_hawk_client.RateHawkAdapter,
	didaAdapter dida.Adapter,
	hotelAreaCacheRepo repositories.HotelAreaCacheRepository,
	currencyExSvc CurrencyExchangeService,
	hnhTravelAgentAdapter travel_agent_client.TravelAgentAdapter,
	hiddenFeeSvc HiddenFeeService,
	hubAdapter hub_provider.HubAdapter, // NEW: Hub Provider adapter
	inActiveHotelsRepo repositories.InActiveHotelsRepository,
	agodaAdapter agoda_client.AgodaAdapter,
	promoHotelRepo repositories.HotelPrioritiesRepository,
	placeRepo repositories.RequestPlaceRepository,
	hotelImageRepo repositories.DisplayImageRepository,
) SearchHotelService {
	return &searchHotelService{
		cfg:                   cfg,
		searchHotelsRepo:      searchHotelsRepo,
		redisLock:             redisRepo,
		hotelRepo:             hotelRepo,
		roomRepo:              roomRepo,
		regionRepo:            regionRepo,
		expediaAdapter:        expediaAdapter,
		rateHawkAdapter:       rateHawkAdapter,
		didaAdapter:           didaAdapter,
		hotelAreaCacheRepo:    hotelAreaCacheRepo,
		currencyExSvc:         currencyExSvc,
		hnhTravelAgentAdapter: hnhTravelAgentAdapter,
		hiddenFeeSvc:          hiddenFeeSvc,
		hubAdapter:            hubAdapter, // NEW: Hub Provider adapter
		inActiveHotelsRepo:    inActiveHotelsRepo,
		agodaAdapter:          agodaAdapter,
		promoHotelRepo:        promoHotelRepo,
		placeRepo:             placeRepo,
		hotelImageRepo:        hotelImageRepo,
	}
}

func (s *searchHotelService) validateSearchHotelRequest(req *domain.HubSearchHotelRequest) error {
	if req == nil {
		return commonErrs.ErrInvalidInput
	}

	roomCount := req.CountRooms()

	if roomCount > constants.MaxRoomSearchAllowed {
		return domain.ErrInvalidRequestRoomAmount
	}

	checkIn, err := time.Parse(constants.HubSearchDateFormat, req.Stay.CheckIn)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-in", req.Stay.CheckIn))
		return domain.ErrCheckInDateInvalid
	}

	checkOut, err := time.Parse(constants.HubSearchDateFormat, req.Stay.CheckOut)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-out", req.Stay.CheckOut))
		return domain.ErrCheckOutDateInvalid
	}

	limitDate := time.Now().Add(365 * 24 * time.Hour) // 1 year

	if checkIn.Before(time.Now().Add(-24*time.Hour)) || checkIn.After(limitDate) {
		return domain.ErrCheckInDateInvalid
	}

	if checkOut.Before(checkIn) || checkOut.Before(time.Now()) || checkOut.After(limitDate) {
		return domain.ErrCheckOutDateInvalid
	}

	return nil
}

func (s *searchHotelService) cacheSearchResults(ctx context.Context, searchKey string, data map[commonEnum.HotelProvider][]*domain.HotelSummary) {
	if err := s.searchHotelsRepo.Insert(ctx, searchKey, data); err != nil {
		log.Error("searchHotelsRepo.Insert error", log.Any("error", err))
		return
	}
}

func (s *searchHotelService) CalcFilterRequest(_ context.Context, in []*domain.HotelSummary) *domain.SearchHotelFilterResponse {
	result := &domain.SearchHotelFilterResponse{
		Ratings:            []float64{},
		AccommodationTypes: []string{},
		Amenities:          []string{},
		MinPrice:           0,
		MaxPrice:           0,
	}

	for _, item := range in {
		price := item.Price
		if item.ExchangedPrice != nil {
			price = item.ExchangedPrice
		}

		if price != nil && price.PricePerNight != nil {
			if price.PricePerNight.DiscountPrice > result.MaxPrice {
				result.MaxPrice = price.PricePerNight.DiscountPrice
			}

			if price.PricePerNight.DiscountPrice < result.MinPrice || result.MinPrice == 0 {
				result.MinPrice = price.PricePerNight.DiscountPrice
			}

			result.Currency = price.Currency
		}

		if len(item.Amenities) > 0 {
			arrStr := make([]string, 0, len(item.Amenities))

			for _, amenity := range item.Amenities {
				arrStr = append(arrStr, amenity.Name)
			}

			result.Amenities = append(result.Amenities, arrStr...)
		}

		rating := math.Floor(item.Rating)
		if rating == 0 {
			result.Ratings = append(result.Ratings, 1)
		} else {
			result.Ratings = append(result.Ratings, rating)
		}

		for key, valueRange := range enum.CustomerRatingFilterOptionRange {
			if key == enum.CustomerRatingFilterOptionNone {
				continue
			}

			if item.Review == nil ||
				len(valueRange) != 2 ||
				item.Review.Rate < valueRange[0] ||
				item.Review.Rate > valueRange[1] {
				continue
			}

			result.CustomerRatings = append(result.CustomerRatings, key)
		}

		result.AccommodationTypes = append(result.AccommodationTypes, item.CategoryType)
	}

	result.CustomerRatings = lo.Uniq[enum.CustomerRatingFilterOption](result.CustomerRatings)
	result.Ratings = lo.Uniq[float64](result.Ratings)
	result.AccommodationTypes = lo.Uniq[string](result.AccommodationTypes)
	result.Amenities = lo.Uniq[string](result.Amenities)

	return result
}

func (s *searchHotelService) matchesFilter(hotel *domain.HotelSummary, filter *domain.SearchHotelFilterRequest) bool {
	if filter == nil {
		return true
	}

	if len(filter.Ratings) > 0 {
		matched := false

		for _, rating := range filter.Ratings {
			if rating == 1 {
				matched = hotel.Rating < 2
				if matched {
					break
				}
			}

			// Filter 2 is matched with [2-2.9]
			floorRating := math.Floor(hotel.Rating)
			if floorRating == rating {
				matched = true
				break
			}
		}

		if !matched {
			return false
		}
	}

	if len(filter.AccommodationTypes) > 0 {
		matched := false

		for _, accType := range filter.AccommodationTypes {
			if hotel.CategoryType == accType {
				matched = true
				break
			}
		}

		if !matched {
			return false
		}
	}

	if len(filter.Amenities) > 0 {
		for _, requiredAmenity := range filter.Amenities {
			found := false

			for _, hotelAmenity := range hotel.Amenities {
				if hotelAmenity.Name == requiredAmenity {
					found = true
					break
				}
			}

			if !found {
				return false
			}
		}
	}

	price := hotel.Price
	if hotel.ExchangedPrice != nil {
		price = hotel.ExchangedPrice
	}

	if price != nil && price.PricePerNight != nil {
		if filter.MinPrice > 0 && price.PricePerNight.DiscountPrice < filter.MinPrice {
			return false
		}

		if filter.MaxPrice > 0 && price.PricePerNight.DiscountPrice > filter.MaxPrice {
			return false
		}
	}

	if len(filter.CustomerRatings) > 0 {
		for _, rating := range filter.CustomerRatings {
			if rating == enum.CustomerRatingFilterOptionNone {
				continue
			}

			value, ok := enum.CustomerRatingFilterOptionRange[rating]
			if !ok || len(value) != 2 {
				continue
			}

			if hotel.Review != nil &&
				hotel.Review.Rate >= value[0] &&
				hotel.Review.Rate <= value[1] {
				break
			}

			return false
		}
	}

	return true
}

func (s *searchHotelService) ApplyPaging(_ context.Context, in []*domain.HotelSummary, paging *commonDomain.Pagination) []*domain.HotelSummary {
	if paging == nil {
		paging = &commonDomain.Pagination{
			PageLimit:   100,
			PageCurrent: 1,
		}
	}

	if paging.PageCurrent < 1 {
		paging.PageCurrent = 1
	}

	start := int((paging.PageCurrent - 1) * paging.PageLimit)
	end := start + int(paging.PageLimit)

	paging.SetTotalRecord(int64(len(in)))

	if start > len(in) {
		return []*domain.HotelSummary{}
	}

	if end > len(in) {
		end = len(in)
	}

	return in[start:end]
}

func (s *searchHotelService) ApplyFilterRequest(_ context.Context, in []*domain.HotelSummary, filter *domain.SearchHotelFilterRequest) []*domain.HotelSummary {
	filteredHotels := []*domain.HotelSummary{}

	for _, hotel := range in {
		if s.matchesFilter(hotel, filter) {
			filteredHotels = append(filteredHotels, hotel)
		}
	}

	return filteredHotels
}

func CheckPriceNil(price *domain.Price) bool {
	return price == nil || price.PricePerNight == nil
}

func SortHotels(req *domain.HubSearchHotelRequest, hotels []*domain.HotelSummary, field commonEnum.SearchHotelSortItemType, desc bool) {
	if field == commonEnum.SearchHotelSortItemTypeNone {
		desc = true
	}

	sort.Slice(hotels, func(i, j int) bool {
		if req.Place != nil && req.Place.Type == enum.PlaceTypeHotel {
			if hotels[i].ID == req.Place.PlaceID {
				return true
			}

			if hotels[j].ID == req.Place.PlaceID {
				return false
			}
		}

		var less bool

		switch field {
		case commonEnum.SearchHotelSortItemTypeNone:
			{
				if hotels[i].Rating == hotels[j].Rating {
					iRC := 0
					jRC := 0

					if hotels[i].Review != nil {
						iRC = int(hotels[i].Review.ReviewCount)
					}

					if hotels[j].Review != nil {
						jRC = int(hotels[j].Review.ReviewCount)
					}

					less = iRC < jRC
				} else {
					less = hotels[i].Rating < hotels[j].Rating
				}
			}
		case commonEnum.SearchHotelSortItemTypeRating:
			{
				less = hotels[i].Rating < hotels[j].Rating
			}
		case commonEnum.SearchHotelSortItemTypePrice:
			if CheckPriceNil(hotels[i].Price) && CheckPriceNil(hotels[j].Price) {
				return false
			} else if CheckPriceNil(hotels[i].Price) {
				return false
			} else if CheckPriceNil(hotels[j].Price) {
				return true
			}

			less = hotels[i].Price.PricePerNight.DiscountPrice < hotels[j].Price.PricePerNight.DiscountPrice
		case commonEnum.SearchHotelSortItemTypeDistanceFromCenter:
			if hotels[i].CenterInfo == nil && hotels[j].CenterInfo == nil {
				return false
			} else if hotels[i].CenterInfo == nil {
				return false
			} else if hotels[j].CenterInfo == nil {
				return true
			}

			less = hotels[i].CenterInfo.DistanceToCenter < hotels[j].CenterInfo.DistanceToCenter
		default:
			return false
		}

		if desc {
			return !less
		}

		return less
	})
}

func (s *searchHotelService) ApplySortRequest(_ context.Context, req *domain.HubSearchHotelRequest, in []*domain.HotelSummary) []*domain.HotelSummary {
	if len(in) <= 1 {
		return in
	}

	if len(req.Sort) == 0 {
		req.Sort = []*domain.SearchHotelSortReq{
			{
				SortType: commonEnum.SearchHotelSortItemTypeNone,
				Desc:     true,
			},
		}
	}

	sortReq := req.Sort

	for _, sortCond := range sortReq {
		SortHotels(req, in, sortCond.SortType, sortCond.Desc)
	}

	return in
}

// Return hotels with content by place.
func (s *searchHotelService) SearchHotelByPlace(ctx context.Context, req *domain.HubSearchHotelRequest, limit int, searchKey string, firstBatchOnly bool) ([]*domain.Hotel, error) {
	if req == nil || req.Place == nil {
		return nil, commonErrs.ErrInvalidInput
	}

	place := req.Place

	hotels := []*domain.Hotel{}
	centerPoint := &domain.Coordinates{}

	if place.Lang == "" {
		place.Lang = constants.DefaultLanguage
	}

	if place.Source == commonEnum.PlaceSourceNone {
		place.Source = commonEnum.PlaceSourceES
	}

	promoList, err := s.promoHotelRepo.FindAll(ctx)
	if err != nil {
		log.Error("[SearchHotelByHotelIDs] promoHotelRepo.FindAll err", log.Any("err", err))
		return nil, errors.ErrSomethingOccurred
	}

	promoHotelIDs := make([]string, 0, len(promoList))

	for _, item := range promoList {
		promoHotelIDs = append(promoHotelIDs, item.HotelID)
	}

	switch place.Source {
	case commonEnum.PlaceSourceES:
		if place.Type == enum.PlaceTypeHotel {
			selectHotel, err := s.hotelRepo.FindByHotelID(ctx, place.PlaceID, place.Lang)
			if err != nil {
				log.Error("[SearchHotelByPlace] hotelContentRepo.FindByHotelID err", log.Any("err", err), log.String("ID", place.PlaceID))
				return nil, domain.ErrPlaceNotFound
			}

			if selectHotel == nil || selectHotel.Location == nil || selectHotel.Location.Coordinates == nil {
				log.Error("[SearchHotelByPlace] hotelContentRepo.FindByHotelID Coordinates nil", log.String("ID", place.PlaceID))
				return nil, domain.ErrPlaceNotFound
			}

			req.Place = converts.FromDomainHotel(selectHotel)

			hotels = append(hotels, selectHotel)

			nearbyHotels, err := s.hotelRepo.FindHotelsByRadius(ctx,
				selectHotel.Location.Coordinates.Longitude,
				selectHotel.Location.Coordinates.Latitude,
				constants.DefaultSearchDistance, place.Lang, selectHotel.HotelID, limit)
			if err != nil {
				log.Error("[SearchHotelByPlace] hotelContentRepo.FindRegionsByRadius err", log.Any("err", err), log.String("ID", place.PlaceID))
				// return nil, domain.ErrPlaceNotFound
			}

			hotels = append(hotels, nearbyHotels...)

			centerPoint.Latitude = selectHotel.Location.Coordinates.Latitude
			centerPoint.Longitude = selectHotel.Location.Coordinates.Longitude
		} else {
			regionData, err := s.regionRepo.FindByRegionID(ctx, place.PlaceID, place.Lang)
			if err != nil {
				log.Error("[SearchHotelByPlace] hotelContentRepo.FindByHotelID err", log.Any("err", err), log.String("ID", place.PlaceID))
				return nil, domain.ErrPlaceNotFound
			}

			if regionData == nil || regionData.Coordinates == nil || regionData.Type != enum.PlaceTypeName[place.Type] {
				log.Error("[SearchHotelByPlace] regionRepo.FindByHotelID err", log.String("ID", place.PlaceID), log.Any("Type", place.Type))
				return nil, domain.ErrPlaceNotFound
			}

			if !commonHelpers.Contains(constants.HighLevelPlaceType, place.Type) { // Radius 5km case
				hotels, err = s.hotelRepo.FindHotelsByRadius(ctx,
					regionData.Coordinates.CenterLongitude,
					regionData.Coordinates.CenterLatitude,
					constants.DefaultSearchDistance,
					place.Lang, "", limit)
				if err != nil {
					log.Error("[SearchHotelByPlace] hotelContentRepo.FindRegionsByRadius err", log.Any("err", err), log.String("ID", place.PlaceID))
					return nil, err
				}
			} else { // List hotels
				if len(regionData.PropertyIDs) > 0 {
					propertyIDs := regionData.PropertyIDs

					if firstBatchOnly {
						sort.Slice(propertyIDs, func(i, j int) bool {
							containI := commonHelpers.Contains(promoHotelIDs, propertyIDs[i])
							containJ := commonHelpers.Contains(promoHotelIDs, propertyIDs[j])

							return containI && !containJ
						})
					}

					nearbyHotels, err := s.hotelRepo.FindHotelIDForCache(ctx, propertyIDs, req.Language, limit)
					if err != nil {
						log.Error("[SearchHotelByPlace] hotelContentRepo.FindByProviderHotelID err", log.Any("err", err), log.String("ID", place.PlaceID))
						return nil, domain.ErrPlaceNotFound
					}

					hotels = append(hotels, nearbyHotels...)
				}

				req.Place = converts.FromDomainRegion(regionData)
				centerPoint.Latitude = regionData.Coordinates.CenterLatitude
				centerPoint.Longitude = regionData.Coordinates.CenterLongitude
			}
		}
	case commonEnum.PlaceSourceGooglePlace:
		if place.Location == nil && !place.Location.IsValid() {
			return nil, domain.ErrInvalidValue
		}

		nearbyHotels, err := s.hotelRepo.FindHotelsByRadius(ctx,
			place.Location.Longitude,
			place.Location.Latitude,
			constants.DefaultSearchDistance, place.Lang, "", limit)
		if err != nil {
			log.Error("[SearchHotelByPlace] hotelContentRepo.FindRegionsByRadius err", log.Any("err", err), log.String("ID", place.PlaceID))
			// return nil, domain.ErrPlaceNotFound
		}

		hotels = append(hotels, nearbyHotels...)

		centerPoint.Latitude = place.Location.Latitude
		centerPoint.Longitude = place.Location.Longitude
		if s.cfg.ShouldLogRequestPlace {
			_, err := s.placeRepo.LogPlace(ctx, place)
			if err != nil {
				log.Error("[LogPlace] placeRepo.LogPlace err", log.Any("err", err), log.Any("place", place))
			}
		}
	}

	// cache Hotels
	hotelIds := []string{}
	hotelDistanceMap := map[string]float64{}

	for _, hotel := range hotels {
		hotelDistanceMap[hotel.HotelID] = hotel.Distance
		hotelIds = append(hotelIds, hotel.HotelID)
	}

	isRequestFullImage := req.ImageMode == enum.ImageModeTypeFull

	languageHotels, err := s.hotelRepo.FindByHotelIDsWithDefault(ctx, hotelIds, req.Language, req.ExcludeContent, isRequestFullImage)
	if err != nil {
		log.Error("[SearchHotelByHotelIDs] hotelContentRepo.FindByHotelIDs err", log.Any("err", err), log.Any("hotelIds", hotelIds))
		return nil, domain.ErrPlaceNotFound
	}

	result := utils.GetHotelContent(languageHotels, req.Language, hotelIds)

	for _, hotel := range result {
		hotel.Distance = hotelDistanceMap[hotel.HotelID]
	}

	sort.Slice(result, func(i, j int) bool {
		if result[i].Distance == 0 && result[j].Distance != 0 {
			return true
		}

		if result[i].Distance != 0 && result[j].Distance == 0 {
			return false
		}

		containI := commonHelpers.Contains(promoHotelIDs, result[i].HotelID)
		containJ := commonHelpers.Contains(promoHotelIDs, result[j].HotelID)

		return containI && !containJ
	})

	return result, nil
}

// Output send to channel out chan []*domain.HotelSummary.
func (s *searchHotelService) prepareSearchHotelPrice(ctx context.Context, providerMap map[commonEnum.HotelProvider][]string, req *domain.HubSearchHotelRequest, timeout int, searchKey string) map[commonEnum.HotelProvider][]*domain.HotelSummary {
	_, span := tracing.StartSpanFromContext(ctx, "searchHotelService.prepareSearchHotelPrice")
	defer span.End()

	out := map[commonEnum.HotelProvider][]*domain.HotelSummary{}
	var mu sync.Mutex
	g := errgroup.Group{}

	for provider, providerIds := range providerMap {
		var maxChunkSize int

		switch provider {
		case commonEnum.HotelProviderAgoda:
			maxChunkSize = 100
		default:
			maxChunkSize = 250
		}

		chunkHotelIDs := ChunkStringArray(providerIds, maxChunkSize)

		for _, _chuckedIds := range chunkHotelIDs {
			if len(_chuckedIds) == 0 {
				continue
			}

			chuckedIds := _chuckedIds

			g.Go(func() error {
				searchCtx, cc := context.WithTimeout(ctx, constants.DefaultCtxTimeout)
				defer cc()

				var temp []*domain.HotelSummary
				var err error

				switch provider {
				case commonEnum.HotelProviderHNHTravelAgent, commonEnum.HotelProviderMayTravelAgent, commonEnum.HotelProviderBZTTravelAgent:
					{
						temp, err = s.hnhTASearchHotel(searchCtx, req, chuckedIds, provider, searchKey)
						if err != nil {
							log.Error("s.hnhTASearchHotel error", log.Any("error", err), log.Any("req", req), log.Any("chuckedIds", chuckedIds), log.Any("searchKey", searchKey))
							return err
						}
					}
				case commonEnum.HotelProviderRateHawk:
					{
						adapterReq := &domain.SearchAdapterReq{
							HubRequest:       req,
							ProviderHotelIds: chuckedIds,
							Timeout:          timeout,
						}

						temp, err = s.rateHawkAdapter.SearchHotel(searchCtx, adapterReq, searchKey)
						if err != nil {
							log.Error("s.rateHawkAdapter.SearchHotel error", log.Any("error", err), log.Any("req", adapterReq))
							return err
						}
					}
				case commonEnum.HotelProviderExpedia:
					{
						temp, err = s.expediaSearchHotel(searchCtx, req, chuckedIds, searchKey)
						if err != nil {
							log.Error("s.expediaSearchHotel error", log.Any("error", err), log.Any("req", req), log.Any("chuckedIds", chuckedIds), log.Any("searchKey", searchKey))
							return err
						}
					}
				case commonEnum.HotelProviderDida:
					{
						adapterReq := &domain.SearchAdapterReq{
							HubRequest:       req,
							ProviderHotelIds: chuckedIds,
							Timeout:          timeout,
						}

						temp, err = s.didaAdapter.SearchHotel(searchCtx, adapterReq, searchKey)
						if err != nil {
							log.Error("s.didaAdapter.SearchHotel error", log.Any("error", err), log.Any("req", req), log.Any("chuckedIds", chuckedIds), log.Any("searchKey", searchKey))
							return err
						}
					}
				case commonEnum.HotelProviderAgoda:
					{
						agodaReq := &domain.SearchAdapterReq{
							HubRequest:       req,
							ProviderHotelIds: chuckedIds,
							Timeout:          timeout,
						}

						temp, err = s.agodaAdapter.SearchHotel(searchCtx, agodaReq, searchKey)
						if err != nil {
							return err
						}
					}
				default:
					return fmt.Errorf("unknown provider: %d", provider)
				}

				mu.Lock()
				out[provider] = append(out[provider], temp...)
				mu.Unlock()

				return nil
			})
		}
	}

	g.Wait()

	return out
}

func (s *searchHotelService) SearchHotelPrice(
	ctx context.Context,
	hotelWithContent []*domain.HotelSummary,
	hotelIDs []string,
	req *domain.HubSearchHotelRequest,
	provider commonEnum.HotelProvider,
	searchKey string,
) error {
	if req == nil {
		return commonErrs.ErrInvalidInput
	}
	var err error

	var hPrices []*domain.HotelSummary

	start := time.Now().UnixMilli()

	switch provider {
	case commonEnum.HotelProviderExpedia:
		{
			hPrices, err = s.expediaSearchHotel(ctx, req, hotelIDs, searchKey)
			if err != nil {
				log.Error("s.expediaSearchHotel error", log.Any("error", err), log.Any("req", req), log.Any("hotelIDs", hotelIDs), log.Any("searchKey", searchKey))
				return err
			}

			s.mappingHotelPricesToContents(hotelWithContent, hPrices)
		}
	case commonEnum.HotelProviderRateHawk:
		{
			adapterReq := &domain.SearchAdapterReq{
				HubRequest:       req,
				ProviderHotelIds: hotelIDs,
			}
			hPrices, err = s.rateHawkAdapter.SearchHotel(ctx, adapterReq, searchKey)
			if err != nil {
				log.Error("s.rateHawkAdapter.SearchHotel error", log.Any("error", err), log.Any("req", adapterReq))
				return err
			}

			s.mappingHotelPricesToContents(hotelWithContent, hPrices)
		}
	default:
		return fmt.Errorf("unsupported provider: %d", provider)
	}

	fmt.Println("SearchHotelPrice exec time: ", time.Now().UnixMilli()-start)

	return nil
}

func (s *searchHotelService) mappingContentToPricesMap(contents []*domain.HotelSummary, pricesMap map[commonEnum.HotelProvider][]*domain.HotelSummary) {
	genKey := func(provider commonEnum.HotelProvider, id string) string {
		return fmt.Sprintf("%d-%s", provider, id)
	}

	providerContentMap := map[string]*domain.HotelSummary{}

	for _, item := range contents {
		for key, val := range item.ProviderIds {
			mapKey := genKey(key, val)
			providerContentMap[mapKey] = item
		}
	}

	for key, val := range pricesMap {
		if key == commonEnum.HotelProviderNone {
			continue
		}

		for index, pHotelSum := range val {
			mapKey := genKey(key, pHotelSum.ProviderHotelID)

			content := providerContentMap[mapKey]
			if content != nil {
				cloneContent := helpers.Copy(content).(*domain.HotelSummary)
				cloneContent.Price = pHotelSum.Price
				cloneContent.Available = pHotelSum.Available
				cloneContent.RoomLeft = pHotelSum.RoomLeft
				cloneContent.Provider = pHotelSum.Provider
				cloneContent.MatchKey = pHotelSum.MatchKey
				cloneContent.ApplicableNationality = pHotelSum.ApplicableNationality

				val[index] = cloneContent
			} else {
				log.Error("mappingContentToPricesMap content not found", log.String("mapKey", mapKey))
			}
		}
	}
}

func (s *searchHotelService) mappingHotelPricesToContents(contents []*domain.HotelSummary, hPrices []*domain.HotelSummary) {
	hotelSumMap := map[string]*domain.HotelSummary{}
	for _, item := range hPrices {
		hotelSumMap[item.ProviderHotelID] = item
	}

	for _, item := range contents {
		if hotelSum, ok := hotelSumMap[item.ProviderHotelID]; ok {
			item.Price = hotelSum.Price
			item.Available = hotelSum.Available
			item.RoomLeft = hotelSum.RoomLeft
		}
	}
}

func ChunkStringArray(arr []string, chunkSize int) [][]string {
	var chunks [][]string

	for i := 0; i < len(arr); i += chunkSize {
		end := i + chunkSize
		if end > len(arr) {
			end = len(arr)
		}

		chunks = append(chunks, arr[i:end])
	}

	return chunks
}

// (0 = Sunday, 1 = Monday, ..., 6 = Saturday).
func isInActiveTime(manualTimeRange string, skipDayOfWeek string) (bool, error) {
	saigonLoc, err := time.LoadLocation(constants.HCMTimezone)
	if err != nil {
		return false, err
	}

	nowInSaigon := time.Now().In(saigonLoc)

	if skipDayOfWeek != "" {
		currentDay := int(nowInSaigon.Weekday())

		skipDays := make(map[int]int)

		for _, dayStr := range strings.Split(skipDayOfWeek, ",") {
			if day, err := strconv.Atoi(dayStr); err == nil {
				skipDays[day] = day
			}
		}

		if _, exists := skipDays[currentDay]; exists {
			return false, nil
		}
	}

	ok, err := utils.CheckTimeInRange(manualTimeRange, nowInSaigon)
	if err != nil {
		return false, err
	}

	return ok, nil
}

func (s *searchHotelService) SearchCached(
	ctx context.Context,
	req *domain.HubSearchHotelRequest,
	reqProviders []commonEnum.HotelProvider,
	searchKey string,
) (
	map[commonEnum.HotelProvider][]*domain.HotelSummary,
	[]commonEnum.HotelProvider,
	error,
) {
	returnProviders := []commonEnum.HotelProvider{
		commonEnum.HotelProviderNone,
	}

	out := map[commonEnum.HotelProvider][]*domain.HotelSummary{}

	cachedHotelParents, err := s.searchHotelsRepo.FindParentIDByKey(ctx, searchKey, reqProviders)
	if err != nil {
		log.Error("searchHotelsRepo.FindParentIDByKey error", log.Any("error", err), log.String("searchKey", searchKey))
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	var wg sync.WaitGroup
	errChan := make(chan error, len(cachedHotelParents)) // Channel to collect errors

	for provider, parentID := range cachedHotelParents {
		wg.Add(1)

		go func(provider commonEnum.HotelProvider, parentID string) {
			defer wg.Done()
			cachedHotels, err := s.searchHotelsRepo.FindByParentID(ctx, parentID)
			if err != nil {
				log.Error("searchHotelsRepo.Find error", log.Any("error", err), log.String("searchKey", searchKey))
				errChan <- err
				return
			}

			out[provider] = cachedHotels
			returnProviders = append(returnProviders, provider)
		}(provider, parentID)
	}

	wg.Wait()
	close(errChan)

	if len(errChan) > 0 {
		return nil, nil, commonErrs.ErrSomethingOccurred
	}

	return out, returnProviders, nil
}
