package service

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonErrs "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/apps/common/tracing"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/utils"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *searchHotelService) SearchList(
	ctx context.Context,
	req *domain.HubSearchHotelRequest,
	reqProviders []commonEnum.HotelProvider,
) (
	_ []*domain.HotelSummary,
	_ bool,
	err error,
) {
	// Validate & side logic
	if len(reqProviders) == 0 {
		return []*domain.HotelSummary{}, false, nil
	}

	if req.Language == "" {
		req.Language = constants.DefaultLanguage
	}

	if req.CountryCode == "" {
		req.CountryCode = "VN"
	}

	err = s.validateSearchHotelRequest(req)
	if err != nil {
		return nil, false, err
	}

	// 1. Check if this is SUB Hub request - Forward to Hub Provider
	isSubHubRequest := ctx.Value(constants.IsSubHubRequestKey)
	if isSubHubRequest != nil && isSubHubRequest.(bool) {
		return s.handleSubHubSearchList(ctx, req, reqProviders)
	}

	// General occupancy logic
	occupancies := req.ConvertGeneralToOccupancies(req.GeneralOccupancy)

	if occupancies != nil {
		req.Occupancies = occupancies
	}

	// Begin
	firstBatchSize := int64(req.Pagination.PageLimit)

	searchKey, err := req.GenSearchKey()
	if err != nil {
		log.Error("GenSearchKey error", log.Any("error", err), log.Any("req", req))
		return nil, false, commonErrs.ErrSomethingOccurred
	}

	// t := time.Now()

	isHNHTravelAgentActive, err := isInActiveTime(s.cfg.HNHTravelAgentServiceActiveTime, s.cfg.HNHTravelAgentSkipDayOfWeek)
	if err != nil {
		log.Error("isInActiveTime error", log.Any("error", err), log.String("HNHTravelAgentServiceActiveTime", s.cfg.HNHTravelAgentServiceActiveTime))
		isHNHTravelAgentActive = false
	}

	isMayTravelAgentActive, err := isInActiveTime(s.cfg.MayTravelAgentServiceActiveTime, "")
	if err != nil {
		log.Error("isInActiveTime error", log.Any("error", err), log.String("MayTravelAgentServiceActiveTime", s.cfg.MayTravelAgentServiceActiveTime))
		isMayTravelAgentActive = false
	}

	if !isHNHTravelAgentActive {
		reqProviders = lo.Without(reqProviders, commonEnum.HotelProviderHNHTravelAgent)
	}

	if !isMayTravelAgentActive {
		reqProviders = lo.Without(reqProviders, commonEnum.HotelProviderMayTravelAgent)
	}

	dataProviderMap := map[commonEnum.HotelProvider][]*domain.HotelSummary{}
	foundProviders := []commonEnum.HotelProvider{}

	if !req.NoCache {
		dataProviderMap, foundProviders, err = s.SearchCached(ctx, req, reqProviders, searchKey)
		if err != nil {
			log.Error("s.SearchCached error", log.Any("error", err), log.Any("providers", reqProviders), log.Any("req", req))
			return nil, false, err
		}
	}

	// Retun first batch size
	shouldWaitFullData := true
	loadMore := false
	totalCount := 0

	missingProviders, _ := lo.Difference(reqProviders, foundProviders)
	if len(missingProviders) > 0 {
		lock, err := s.redisLock.AcquireLock(searchKey)
		if err != nil {
			log.Error("AcquireLock error", log.Any("error", err), log.String("searchKey", searchKey))
			return nil, false, err
		}

		if !lock {
			err = s.redisLock.WaitForLockToRelease(searchKey, time.Second, constants.CacheSearchResultTimeoutSecs)
			if err != nil {
				log.Error("WaitForLockToRelease error", log.Any("error", err), log.String("searchKey", searchKey))
				return nil, false, err
			}

			return s.SearchList(ctx, req, reqProviders)
		}

		if firstBatchSize == 20 && req.Pagination.PageCurrent == 1 {
			shouldWaitFullData = false

			getOutputSearchReq := &getOutputSearchReq{
				batchSize:        int(firstBatchSize),
				shouldGetFull:    shouldWaitFullData,
				providers:        missingProviders,
				searchKey:        searchKey,
				isFirstBatchOnly: true,
				timeout:          3,
			}

			var dataProviderMapTemp map[commonEnum.HotelProvider][]*domain.HotelSummary

			dataProviderMapTemp, loadMore, _, err = s.getOutputSearchHotels(ctx, req, getOutputSearchReq, true)
			if err != nil {
				log.Error("s.getOutputSearchForBatchHotel error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
				return nil, false, err
			}

			for provider, data := range dataProviderMapTemp {
				dataProviderMap[provider] = data
			}
		}

		if loadMore || shouldWaitFullData {
			waitChanFullData := make(chan map[commonEnum.HotelProvider][]*domain.HotelSummary)
			// var err error

			// Get full & cache
			go func() {
				bgCtx, cc := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
				defer cc()

				// Start a span with the background context and link it to the main span
				bgCtx, bgSpan := tracing.StartSpanFromContext(bgCtx, "SearchHotelFull")
				defer bgSpan.End()

				var fullDataMap map[commonEnum.HotelProvider][]*domain.HotelSummary

				defer func() {
					if shouldWaitFullData {
						waitChanFullData <- fullDataMap
					}
				}()

				getOutputSearchReq := &getOutputSearchReq{
					batchSize:        int(firstBatchSize),
					shouldGetFull:    shouldWaitFullData,
					providers:        missingProviders,
					searchKey:        searchKey,
					isFirstBatchOnly: false,
					timeout:          5,
				}

				fullDataMap, _, _, err = s.getOutputSearchHotels(bgCtx, req, getOutputSearchReq, true)
				if err != nil {
					log.Error("s.getOutputSearchFull error", log.Any("error", err), log.Any("providers", missingProviders), log.Any("req", req))
					s.redisLock.ReleaseLock(searchKey)

					return
				}

				if len(fullDataMap) > 0 {
					for key, val := range fullDataMap {
						fullDataMap[key] = simpleFilterHotelList(val)
					}

					go func(data map[commonEnum.HotelProvider][]*domain.HotelSummary) {
						defer s.redisLock.ReleaseLock(searchKey)

						if len(data) == 0 {
							log.Error("Prepare cache search results empty data error")
							return
						}

						cacheCtx, cancel := context.WithTimeout(context.Background(), constants.CachedSearchResultTimeout)
						defer cancel()

						s.cacheSearchResults(cacheCtx, searchKey, data)
					}(fullDataMap)
				} else {
					s.redisLock.ReleaseLock(searchKey)
				}
			}()

			if shouldWaitFullData {
				dataProviderMapTemp := <-waitChanFullData

				for provider, data := range dataProviderMapTemp {
					dataProviderMap[provider] = data
				}
				//	if err != nil {
				//		return nil, false, err
				//	}
			}
		} else {
			s.redisLock.ReleaseLock(searchKey)
		}
	}

	data := s.ProcessProviderDataMap(ctx, req.PartnershipID, req.OfficeID, uint32(req.Stay.RoomCount*req.Stay.DayCount), dataProviderMap, req.PriceConditionConfig)

	data = simpleFilterHotelList(data)

	if loadMore && totalCount != 0 {
		req.Pagination.TotalRecord = int64(totalCount)
	}

	return data, loadMore, nil
}

// Return hotels with content by hotel_ids.
func (s *searchHotelService) SearchHotelByHotelIDs(ctx context.Context, req *domain.HubSearchHotelRequest, searchKey string) ([]*domain.Hotel, map[string]*domain.HotelSummaryImage, error) {
	if req == nil {
		return nil, nil, commonErrs.ErrInvalidInput
	}

	hotelIDs := req.HotelIds
	isRequestFullImage := req.ImageMode == enum.ImageModeTypeFull

	languageHotels, err := s.hotelRepo.FindByHotelIDsWithDefault(ctx, hotelIDs, req.Language, req.ExcludeContent, isRequestFullImage)
	if err != nil {
		log.Error("[SearchHotelByHotelIDs] hotelContentRepo.FindByHotelIDs err", log.Any("err", err), log.Any("hotelIds", hotelIDs))
		return nil, nil, domain.ErrPlaceNotFound
	}

	thumbnailImageMap := map[string]*domain.HotelSummaryImage{}

	if !isRequestFullImage {
		thumbnailImages, err := s.hotelImageRepo.FindByDisplayImageHotelIDs(ctx, hotelIDs)
		if err != nil {
			log.Error("[SearchHotelByHotelIDs] hotelImageRepo.FindByDisplayImageHotelIDs err", log.Any("err", err), log.Any("hotelIDs", hotelIDs))
			return nil, nil, errors.ErrSomethingOccurred
		}

		thumbnailHotelIDs := make([]string, 0, len(thumbnailImages))
		for _, item := range thumbnailImages {
			thumbnailHotelIDs = append(thumbnailHotelIDs, item.HotelID)
			thumbnailImageMap[item.HotelID] = item.Image
		}

		if len(thumbnailHotelIDs) != len(hotelIDs) {

			_, missingHotelIDs := lo.Difference(thumbnailHotelIDs, hotelIDs)

			log.Error("missing hotel display image", log.Any("missing", missingHotelIDs))
		}
	}

	hotels := utils.GetHotelContent(languageHotels, req.Language, hotelIDs)

	return hotels, thumbnailImageMap, nil
}

func simpleFilterHotelList(hotels []*domain.HotelSummary) []*domain.HotelSummary {
	listHotelWithPrices := []*domain.HotelSummary{}

	for _, hotel := range hotels {
		if hotel != nil && hotel.Price != nil {
			listHotelWithPrices = append(listHotelWithPrices, hotel)
		}
	}

	return listHotelWithPrices
}

func (s *searchHotelService) handleSubHubSearchList(
	ctx context.Context,
	req *domain.HubSearchHotelRequest,
	reqProviders []commonEnum.HotelProvider,
) ([]*domain.HotelSummary, bool, error) {
	tempRequestCurrency := req.RequestCurrency
	req.RequestCurrency = ""

	response, err := s.hubAdapter.SearchListHotel(ctx, req)
	if err != nil {
		log.Error("SUB Hub search failed",
			log.String("error", err.Error()),
		)

		return nil, false, fmt.Errorf("SUB Hub search failed: %w", err)
	}

	// Set back request currency
	req.RequestCurrency = tempRequestCurrency

	// Process response qua price service cho SUB Hub partnership
	processedHotels, err := s.processSubHubPricing(ctx, response.HotelSummary, req)
	if err != nil {
		log.Error("SUB Hub price processing failed",
			log.String("error", err.Error()),
		)

		return nil, false, fmt.Errorf("SUB Hub price processing failed: %w", err)
	}

	// Determine load more based on pagination
	loadMore := false
	if response.Pagination != nil {
		loadMore = response.Pagination.PageCurrent < response.Pagination.TotalPage
	}

	return processedHotels, loadMore, nil
}
