package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type GetHotelDetailHandler interface {
	Handle(ctx context.Context, req *domain.GetHotelDetailReq) (*domain.GetHotelDetailRes, error)
}

type getHotelDetailHandler struct {
	cfg       *config.Schema
	hotelRepo repositories.HotelRepository
	roomRepo  repositories.RoomRepository
}

func NewGetHotelDetailHandler(
	cfg *config.Schema,
	hotelRepo repositories.HotelRepository,
	roomRepo repositories.RoomRepository,
) GetHotelDetailHandler {
	return &getHotelDetailHandler{cfg, hotelRepo, roomRepo}
}

func (h *getHotelDetailHandler) Handle(ctx context.Context, req *domain.GetHotelDetailReq) (*domain.GetHotelDetailRes, error) {
	hotel, err := h.hotelRepo.FindByHotelID(ctx, req.HotelID, req.Language)
	if err != nil {
		return nil, err
	}

	if hotel == nil {
		return nil, errors.ErrNotFound
	}

	// Get all provider's room contents
	rooms, err := h.roomRepo.FindByRoomIDsV2(ctx, commonEnum.HotelProviderNone, hotel.HotelID, req.Language)
	if err != nil {
		return nil, err
	}

	hotel.Rooms = rooms

	return &domain.GetHotelDetailRes{
		Hotel: hotel,
	}, nil
}
