package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type GetCurrencyExchangeDetailHandler interface {
	Handle(ctx context.Context, req *domain.GetCurrencyExchangeDetailReq) (*domain.CurrencyExchange, error)
}

type getCurrencyExchangeDetailHandler struct {
	currencyExchangeRepo repositories.CurrencyExchangeRepository
}

func NewGetCurrencyExchangeDetailHandler(currencyExchangeRepo repositories.CurrencyExchangeRepository) GetCurrencyExchangeDetailHandler {
	return &getCurrencyExchangeDetailHandler{currencyExchangeRepo}
}

func (h *getCurrencyExchangeDetailHandler) Handle(ctx context.Context, req *domain.GetCurrencyExchangeDetailReq) (*domain.CurrencyExchange, error) {
	currencyExchange, err := h.currencyExchangeRepo.FindByID(ctx, req.ID)
	if err != nil {
		log.Error("currencyExchangeRepo.FindByID error", log.Any("error", err), log.Any("req", req))
		return nil, errors.ErrSomethingOccurred
	}

	return currencyExchange, nil
}
