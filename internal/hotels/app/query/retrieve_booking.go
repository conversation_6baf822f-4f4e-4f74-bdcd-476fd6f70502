package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type RetrieveBookingHandler interface {
	Handle(ctx context.Context, req *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error)
}

type retrieveBookingHandler struct {
	cfg            *config.Schema
	hotelOrderRepo repositories.OrderRepository
}

func NewRetrieveBookingHandler(
	cfg *config.Schema,
	hotelOrderRepo repositories.OrderRepository,
) RetrieveBookingHandler {
	return &retrieveBookingHandler{cfg, hotelOrderRepo}
}

func getHotelRes(data *domain.HubOrderHotelItem) *domain.HubOrderHotelItem {
	if data == nil {
		log.Error("getHotelPriceCheckRes input nil")
		return nil
	}

	for _, room := range data.ListRooms {
		// MOCK SuccessWithoutConfirmationId
		// if data.HotelID == constants.SuccessWithoutConfirmationId {
		// 	room.ConfirmationID = ""
		// }
		room.RateData = nil
	}

	return data
}

func (h *retrieveBookingHandler) Handle(ctx context.Context, req *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error) {
	if req.OfficeID == "" && req.SessionID == "" {
		return nil, errors.ErrInvalidInput
	}

	var orderHotel *domain.HubHotelOrder

	var err error
	if req.OrderCode != "" {
		// Sử dụng method mới với PartnershipID để đảm bảo partnership isolation
		orderHotel, err = h.hotelOrderRepo.FindOneByOrderCodeWithPartnership(ctx, req.OfficeID, req.OrderCode, req.PartnershipID)
		if err != nil {
			log.Error("bookingRepo.FindOneByOrderCodeWithPartnership error", log.Any("error", err),
				log.String("officeID", req.OfficeID), log.String("OrderCode", req.OrderCode), log.String("PartnershipID", req.PartnershipID))
			return nil, errors.ErrSomethingOccurred
		}
	}

	if orderHotel == nil && req.SessionID != "" {
		// Sử dụng method mới với PartnershipID để đảm bảo partnership isolation
		orderHotel, err = h.hotelOrderRepo.FindOrderBySessionIDWithPartnership(ctx, req.OfficeID, req.SessionID, req.PartnershipID)
		if err != nil {
			log.Error("bookingRepo.FindOrderBySessionIDWithPartnership error", log.Any("error", err),
				log.String("officeID", req.OfficeID), log.String("SessionID", req.SessionID), log.String("PartnershipID", req.PartnershipID))
			return nil, errors.ErrSomethingOccurred
		}
	}

	if orderHotel == nil {
		return &domain.HubRetrieveBookingRes{
			ErrorRes: domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: errors.ErrNotFound.Error(),
			},
		}, nil
	}

	refundedAmount := float64(0)
	rateDataCf := orderHotel.ExchangedRateDataCf
	currency := ""

	if orderHotel.ExchangedRefundData != nil {
		refundedAmount = orderHotel.ExchangedRefundData.RefundAmount
	}

	if orderHotel.ExchangedRateDataCf != nil {
		currency = orderHotel.ExchangedRateDataCf.Currency
	}

	if orderHotel.RequestCurrencyRefundData != nil {
		refundedAmount = orderHotel.RequestCurrencyRefundData.RefundAmount
		currency = orderHotel.RequestCurrencyRefundData.Currency
	}

	if orderHotel.RequestCurrencyRateDataCf != nil {
		currency = orderHotel.RequestCurrencyRateDataCf.Currency
		rateDataCf = orderHotel.RequestCurrencyRateDataCf
	}

	res := &domain.HubRetrieveBookingRes{
		ErrorRes:      domain.ErrorRes{IsSuccess: true},
		OrderCode:     orderHotel.OrderCode,
		Hotel:         getHotelRes(orderHotel.Hotel),
		RateDataCf:    rateDataCf,
		BookingStatus: orderHotel.BookingStatus,
		RefundAmount:  float64(refundedAmount),
		Refunded:      orderHotel.Refunded,
		Cancelable:    orderHotel.CanCancel() == nil,
		Currency:      currency,
	}

	return res, nil
}
