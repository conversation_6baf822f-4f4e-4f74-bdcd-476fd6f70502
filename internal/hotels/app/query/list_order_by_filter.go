package query

import (
	"context"
	"time"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	commonEnum "gitlab.deepgate.io/apps/common/enum"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type ListOrderByFilterHandler interface {
	Handle(ctx context.Context, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, map[string]*domain.ClientBooking, error)
}

type listOrderHandler struct {
	cfg               *config.Schema
	orderRepo         repositories.OrderRepository
	partnerClient     partner.PartnerClient
	partnershipClient partnership.PartnershipClient
	clientBookingRepo repositories.ClientBookingRepository
}

func NewListOrderByFilterHandler(
	cfg *config.Schema,
	orderRepo repositories.OrderRepository,
	partnerClient partner.PartnerClient,
	partnershipClient partnership.PartnershipClient,
	clientBookingRepo repositories.ClientBookingRepository,
) ListOrderByFilterHandler {
	return &listOrderHandler{cfg, orderRepo, partnerClient, partnershipClient, clientBookingRepo}
}

func (h *listOrderHandler) GetShopsByRole(ctx context.Context, req *domain.ListOrderFilter) ([]*domain.PartnerShopInfo, error) {
	if req == nil || len(req.Roles) == 0 {
		log.Error("GetShopsByRole: request is nil or roles are empty", log.Any("req", req))
		return nil, errors.ErrInvalidInput
	}

	hasRoleSale := lo.Contains(req.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSale])
	saleCode := ""
	if hasRoleSale {
		partnershipUser, err := h.partnershipClient.GetPartnershipUserByID(ctx, req.UserID, req.PartnershipID)
		if err != nil {
			log.Error("partnershipClient.GetPartnershipUserByID error", log.Any("error", err), log.Any("req", req))
			return nil, err
		}

		if partnershipUser == nil {
			log.Error("partnershipClient.GetPartnershipUserByID returned nil", log.Any("req", req))
			return nil, errors.ErrNotFound
		}

		saleCode = partnershipUser.Username
	}

	shops, err := h.partnerClient.GetShopsByManagerID(ctx, req.PartnershipID, req.ManagerID)
	if err != nil {
		log.Error("partnerClient.GetShopsByManagerID error", log.Any("error", err), log.Any("req", req))
		return nil, err
	}

	if saleCode != "" {
		shopBySaleCodes, err := h.partnerClient.GetShopsBySaleCodes(ctx, req.PartnershipID, []string{saleCode})
		if err != nil {
			log.Error("partnerClient.GetShopsByManagerID error", log.Any("error", err), log.Any("req", req))
			return nil, err
		}

		for _, shopBySaleCode := range shopBySaleCodes{
			for _, shop := range shopBySaleCode.Shops{
				shops = append(shops, shop)
			}
		}
		
	}

	return shops, nil
}

func (h *listOrderHandler) Handle(ctx context.Context, req *domain.ListOrderFilter) ([]*domain.HubHotelOrder, map[string]*domain.ClientBooking, error) {
	clientBookings := map[string]*domain.ClientBooking{}

	shops, err := h.GetShopsByRole(ctx, req)
	if err != nil {
		log.Error("GetShopsByRole error", log.Any("error", err), log.Any("req", req))
		return nil, nil, err
	}

	if len(shops) == 0 {
		return nil, nil, nil
	}

	for _, shop := range shops {
		req.ManageOfficeIDs = append(req.ManageOfficeIDs, shop.OfficeID)
	}

	res, err := h.orderRepo.ListOrderByFilter(ctx, req.Pagination, req)
	if err != nil {
		log.Error("bookingRepo.ListOrder error", log.Any("error", err), log.Any("req", req))
		return nil, nil, err
	}

	hubOrderCodes := []string{}
	for _, order := range res {
		if order.AgentCode == "" {
			partner, err := h.partnerClient.GetOfficeInfo(ctx, order.OfficeID, req.PartnershipID)
			if err != nil {
				log.Error("partnerClient.GetOfficeInfo error", log.Any("error", err), log.Any("req", req))
				return nil, nil, err
			}

			if partner == nil {
				log.Error("partnerClient.GetOfficeInfo error", log.Any("error", err), log.Any("req", req))
				continue
			}

			order.AgentCode = partner.Code
			go func(id, agentCode string) {
				newCtx, ctxCancel := context.WithTimeout(context.Background(), 5*time.Second)
				defer ctxCancel()

				err := h.orderRepo.UpdateOneV2(newCtx, id, &domain.HubOrderUpdate{
					AgentCode: agentCode,
				})
				if err != nil {
					log.Error("bookingRepo.UpdateAgentCode error", log.Any("error", err), log.Any("req", req))
				}
			}(order.ID, partner.Code)
		}

		hubOrderCodes = append(hubOrderCodes, order.OrderCode)
	}

	if req.PartnershipID != h.cfg.HubPartnershipID {
		for _, code := range hubOrderCodes {
			clientBookings[code] = nil
		}

		clientBookingData, err := h.clientBookingRepo.FindByHubOrderCodes(ctx, hubOrderCodes, req.PartnershipID)
		if err != nil {
			log.Error("clientBookingRepo.FindByHubOrderCodes error", log.Any("error", err), log.Any("req", req))
			return nil, nil, err
		}

		for _, data := range clientBookingData {
			clientBookings[data.HubOrderCode] = data
		}

		for key, value := range clientBookings {
			if value == nil {
				clientBooking := &domain.ClientBooking{
					PartnershipID: req.PartnershipID,
					HubOrderCode:  key,
					Paid:          false,
				}

				err := h.clientBookingRepo.InsertOne(ctx, clientBooking)
				if err != nil {
					log.Error("clientBookingRepo.FindByHubOrderCodes error", log.Any("error", err), log.Any("req", req))
					return nil, nil, err
				}

				clientBookings[key] = clientBooking
			}
		}
	}

	return res, clientBookings, nil
}
