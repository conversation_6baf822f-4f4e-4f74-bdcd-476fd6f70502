package query

import (
	"context"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

type DetailConfigHiddenFeeHandler interface {
	Handle(ctx context.Context, id string) (*domain.HiddenServiceFee, error)
}

type detailConfigHiddenFeeHandler struct {
	hiddenFeeRepo repositories.HiddenServiceFeeRepository
}

func NewDetailConfigHiddenFeeHandler(hiddenFeeRepo repositories.HiddenServiceFeeRepository) DetailConfigHiddenFeeHandler {
	return &detailConfigHiddenFeeHandler{hiddenFeeRepo}
}

func (h *detailConfigHiddenFeeHandler) Handle(ctx context.Context, id string) (*domain.HiddenServiceFee, error) {
	res, err := h.hiddenFeeRepo.FindByID(ctx, id)
	if err != nil {
		log.Error("ConfigHiddenFeeRepo.FindByID error", log.Any("error", err), log.Any("req", id))
		return nil, errors.ErrSomethingOccurred
	}

	if res == nil {
		return nil, errors.ErrNotFound
	}

	return res, nil
}
