package query

import (
	"context"
	"strconv"
	"strings"

	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type GetDetailOrderByIDHandler interface {
	Handle(ctx context.Context, orderID string) (*domain.HubHotelOrder, *domain.Hotel, *domain.ClientBooking, error)
}

type getDetailOrderByIDHandler struct {
	cfg               *config.Schema
	hotelOrderRepo    repositories.OrderRepository
	hotelRepo         repositories.HotelRepository
	clientBookingRepo repositories.ClientBookingRepository
}

func NewGetDetailOrderByIDHandler(
	cfg *config.Schema,
	hotelOrderRepo repositories.OrderRepository,
	hotelRepo repositories.HotelRepository,
	clientBookingRepo repositories.ClientBookingRepository,
) GetDetailOrderByIDHandler {
	return &getDetailOrderByIDHandler{cfg, hotelOrderRepo, hotelRepo, clientBookingRepo}
}

func (h *getDetailOrderByIDHandler) Handle(ctx context.Context, orderID string) (*domain.HubHotelOrder, *domain.Hotel, *domain.ClientBooking, error) {
	if orderID == "" {
		return nil, nil, nil, errors.ErrInvalidInput
	}

	order, err := h.hotelOrderRepo.FindOrderByID(ctx, orderID)
	if err != nil {
		log.Error("bookingRepo.FindOneByBookingCode error", log.Any("error", err),
			log.String("officeID", orderID))
		return nil, nil, nil, errors.ErrSomethingOccurred
	}

	var hotelContent *domain.Hotel
	if order.Hotel != nil {
		language := order.HotelSearchRequest.Language
		if language == "" {
			language = constants.DefaultLanguage
		}

		hotelContent, err = h.hotelRepo.FindByHotelID(ctx, order.Hotel.HotelID, language)
		if err != nil {
			log.Error("hotelRepo.FindByHotelID error", log.Any("error", err),
				log.String("hotelID", order.Hotel.HotelID))
			return nil, nil, nil, errors.ErrSomethingOccurred
		}

		if hotelContent == nil {
			log.Error("hotelRepo.FindByHotelID error", log.Any("error", err),
				log.String("hotelID", order.Hotel.HotelID))
			return nil, nil, nil, errors.ErrSomethingOccurred
		}

		order.Hotel.Address = hotelContent.Address

		if hotelContent.Ratings != nil &&
			hotelContent.Ratings.Property != nil &&
			strings.EqualFold(hotelContent.Ratings.Property.Type, "star") {
			rating, err := strconv.ParseFloat(hotelContent.Ratings.Property.Rating, 32)
			if err != nil {
				log.Error("strconv.ParseFloat err", log.Any("err", err), log.String("value", hotelContent.Ratings.Property.Rating))
				rating = 0
			}

			order.Hotel.Rating = rating
		}
	}

	clientBookings, err := h.clientBookingRepo.FindByHubOrderCodes(ctx, []string{order.OrderCode}, order.PartnershipID)
	if err != nil {
		log.Error("h.clientBookingRepo.FindByHubOrderCodes", log.Any("error", err),
			log.String("officeID", orderID))
		return nil, nil, nil, errors.ErrSomethingOccurred
	}

	clientBooking := &domain.ClientBooking{
		PartnershipID: order.PartnershipID,
		HubOrderCode:  order.OrderCode,
		Paid:          false,
	}

	if len(clientBookings) > 0 {
		clientBooking = clientBookings[0]
	}

	return order, hotelContent, clientBooking, nil
}
