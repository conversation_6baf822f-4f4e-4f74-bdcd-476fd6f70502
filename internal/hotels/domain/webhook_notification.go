package domain

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type ExpediaWebhookHeader struct {
	APIKey    string `json:"api_key"`
	Signature string `json:"signature"`
	Timestamp int64  `json:"timestamp"`
}
type ExpediaWebhookNotificationRequest struct {
	*ExpediaWebhookHeader
	EventID              string                       `json:"event_id"`
	EventType            enum.ExpediaWebhookEventType `json:"event_type"`
	EventTime            string                       `json:"event_time"`
	ItineraryID          string                       `json:"itinerary_id"`
	Email                string                       `json:"email"`
	Message              string                       `json:"message"`
	AffiliateReferenceID string                       `json:"affiliate_reference_id"`
	TopicTags            enum.ExpediaWebhookTopicTag  `json:"topic_tags"`
	Rooms                []*ExpediaNotificationRoom   `json:"rooms"` // Array of Room structs
	Key                  string                       `json:"-"`
}

func (r *ExpediaWebhookNotificationRequest) GenKey() {
	if r == nil {
		return
	}

	nonce := fmt.Sprintf("%s:%s:%s:%s:%s:%s:%s:%s",
		r.EventID,
		r.EventType,
		r.EventTime,
		r.ItineraryID,
		r.Email,
		r.Message,
		r.AffiliateReferenceID,
		r.TopicTags,
	)

	for _, room := range r.Rooms {
		nonce += fmt.Sprintf(":%s", room.ConfirmationInfo.Expedia)
	}

	hash := sha256.Sum256([]byte(nonce))
	r.Key = hex.EncodeToString(hash[:])

	return
}

type ExpediaNotificationRoom struct {
	ConfirmationInfo *ConfirmationInfo `json:"confirmation_id"`
}

type ConfirmationInfo struct {
	Expedia  string `json:"expedia"`
	Property string `json:"property"`
}
