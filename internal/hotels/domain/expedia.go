package domain

import (
	"fmt"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type ExpediaHotelContent struct {
	PropertyID                     string                 `json:"property_id,omitempty"`
	Name                           string                 `json:"name,omitempty"`
	Address                        *Address               `json:"address,omitempty"`
	Ratings                        *Ratings               `json:"ratings,omitempty"`
	Location                       *Location              `json:"location,omitempty"`
	Phone                          string                 `json:"phone,omitempty"`
	Fax                            string                 `json:"fax,omitempty"`
	Category                       *Category              `json:"category,omitempty"`
	Rank                           int                    `json:"rank,omitempty"`
	BusinessModel                  *BusinessModel         `json:"business_model,omitempty"`
	CheckIn                        *CheckIn               `json:"checkin,omitempty"`
	Checkout                       *Checkout              `json:"checkout,omitempty"`
	Fees                           *Fees                  `json:"fees,omitempty"`
	Policies                       *Policies              `json:"policies,omitempty"`
	Attributes                     *ExpediaAttributes     `json:"attributes,omitempty"`
	Amenities                      map[string]Amenity     `json:"amenities,omitempty"`
	Images                         []Image                `json:"images,omitempty"`
	OnsitePayments                 *ExpediaOnsitePayments `json:"onsite_payments,omitempty"`
	Rooms                          map[string]ExpediaRoom `json:"rooms,omitempty"`
	Rates                          map[string]ExpediaRate `json:"rates,omitempty"`
	Dates                          *Dates                 `json:"dates,omitempty"`
	Descriptions                   *Descriptions          `json:"descriptions,omitempty"`
	Statistics                     map[string]Statistic   `json:"statistics,omitempty"`
	Airports                       *Airports              `json:"airports,omitempty"`
	Themes                         map[string]Theme       `json:"themes,omitempty"`
	AllInclusive                   *AllInclusive          `json:"all_inclusive,omitempty"`
	TaxID                          string                 `json:"tax_id,omitempty"`
	Chain                          *Chain                 `json:"chain,omitempty"`
	Brand                          *Brand                 `json:"brand,omitempty"`
	SpokenLanguages                map[string]Language    `json:"spoken_languages,omitempty"`
	MultiUnit                      bool                   `json:"multi_unit,omitempty"`
	PaymentRegistrationRecommended bool                   `json:"payment_registration_recommended,omitempty"`
	SupplySource                   string                 `json:"supply_source,omitempty"`
	RegistryNumber                 string                 `json:"registry_number,omitempty"`
	VacationRentalDetails          *VacationRentalDetails `json:"vacation_rental_details,omitempty"`
}

// PropertyManager represents the property manager's information.
type PropertyManager struct {
	Name  string           `json:"name"`
	Links map[string]*Link `json:"links"`
}

// RentalAgreement represents the rental agreement information.
type RentalAgreement struct {
	Links map[string]*Link `json:"links"`
}

// EnhancedRule represents enhanced house rules.
type EnhancedRule struct {
	Rule                  string   `json:"rule"`
	AdditionalInformation []string `json:"additional_information"`
}

// UnitConfig represents the configuration of a vacation rental unit.
type UnitConfig struct {
	Type        string `json:"type"`
	Description string `json:"description"`
	Quantity    int    `json:"quantity"`
	FreeText    string `json:"free_text"`
}

type ExpediaAttributes struct {
	General map[string]Attribute `json:"general,omitempty"`
	Pets    map[string]Attribute `json:"pets,omitempty"`
}

type ExpediaOnsitePayments struct {
	Currency string                 `json:"currency,omitempty"`
	Types    map[string]PaymentType `json:"types,omitempty"`
}

type ExpediaRoom struct {
	ID           string              `json:"id,omitempty"`
	Name         string              `json:"name,omitempty"`
	Descriptions *RoomDescriptions   `json:"descriptions,omitempty"`
	Amenities    map[string]Amenity  `json:"amenities,omitempty"`
	Images       []Image             `json:"images,omitempty"`
	BedGroups    map[string]BedGroup `json:"bed_groups,omitempty"`
	Area         *Area               `json:"area,omitempty"`
	Views        map[string]View     `json:"views,omitempty"`
	Occupancy    *ExpediaOccupancy   `json:"occupancy,omitempty"`
}

type View struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type ExpediaOccupancy struct {
	MaxAllowed    *MaxAllowed            `json:"max_allowed,omitempty"`
	AgeCategories map[string]AgeCategory `json:"age_categories,omitempty"`
}

type ExpediaRate struct {
	ID                      string             `json:"id,omitempty"`
	Amenities               map[string]Amenity `json:"amenities,omitempty"`
	SpecialOfferDescription string             `json:"special_offer_description,omitempty"`
}

type Airports struct {
	Preferred *PreferredAirport `json:"preferred,omitempty"`
}

type PreferredAirport struct {
	IataAirportCode string `json:"iata_airport_code,omitempty"`
}

type Theme struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}

type AllInclusive struct {
	AllRatePlans  bool   `json:"all_rate_plans,omitempty"`
	SomeRatePlans bool   `json:"some_rate_plans,omitempty"`
	Details       string `json:"details,omitempty"`
}

// FUNCTIONS

// MapExpediaHotelContentToRoomsAndHotel maps ExpediaHotelContent to arrays of Room and Hotel
func MapExpediaHotelContentToRoomsAndHotel(expediaContent *ExpediaHotelContent, language, version string) ([]*Room, *Hotel) {
	if expediaContent == nil {
		return nil, nil
	}

	hotel := &Hotel{
		HotelID:                        expediaContent.PropertyID,
		Rank:                           float64(expediaContent.Rank),
		Active:                         true,
		Language:                       language,
		MultiUnit:                      expediaContent.MultiUnit,
		PaymentRegistrationRecommended: expediaContent.PaymentRegistrationRecommended,
		Name:                           expediaContent.Name,
		Phone:                          expediaContent.Phone,
		Fax:                            expediaContent.Fax,
		SupplySource:                   expediaContent.SupplySource,
		Airports:                       expediaContent.Airports,
		Address:                        expediaContent.Address,
		Ratings:                        expediaContent.Ratings,
		Location:                       expediaContent.Location,
		Category:                       expediaContent.Category,
		BusinessModel:                  expediaContent.BusinessModel,
		CheckIn:                        expediaContent.CheckIn,
		Checkout:                       expediaContent.Checkout,
		Fees:                           expediaContent.Fees,
		Policies:                       expediaContent.Policies,
		Attributes:                     MapExpediaAttributesToAttributes(expediaContent.Attributes),
		Amenities:                      mapAmenities(expediaContent.Amenities),
		Images:                         mapImages(expediaContent.Images),
		OnsitePayments:                 mapOnsitePayments(expediaContent.OnsitePayments),
		Rates:                          mapRates(expediaContent.Rates),
		Dates:                          expediaContent.Dates,
		Descriptions:                   expediaContent.Descriptions,
		Statistics:                     mapStatistics(expediaContent.Statistics),
		Chain:                          expediaContent.Chain,
		Brand:                          expediaContent.Brand,
		SpokenLanguages:                mapLanguages(expediaContent.SpokenLanguages),
		ProviderIds:                    map[commonEnum.HotelProvider]string{commonEnum.HotelProviderExpedia: expediaContent.PropertyID},
		Themes:                         mapThemes(expediaContent.Themes),
		VacationRentalDetails:          expediaContent.VacationRentalDetails,
		AllInclusive:                   expediaContent.AllInclusive,
		TaxID:                          expediaContent.TaxID,
		RegistryNumber:                 expediaContent.RegistryNumber,
		Version:                        version,
	}

	// Mapping Rooms
	var rooms []*Room

	for _, expediaRoom := range expediaContent.Rooms {
		room := &Room{
			RoomID:       fmt.Sprintf("%s-%s", expediaContent.PropertyID, expediaRoom.ID),
			HotelRef:     hotel.ID,
			Language:     language,
			Name:         expediaRoom.Name,
			Descriptions: expediaRoom.Descriptions,
			Amenities:    mapAmenities(expediaRoom.Amenities),
			Images:       mapImages(expediaRoom.Images),
			BedGroups:    mapBedGroups(expediaRoom.BedGroups),
			Area:         expediaRoom.Area,
			Occupancy:    mapOccupancy(expediaRoom.Occupancy),
			Views:        mapViews(expediaRoom.Views),
			ProviderIDs:  map[commonEnum.HotelProvider]string{commonEnum.HotelProviderExpedia: expediaRoom.ID},
			Version:      version,
			HotelID:      hotel.HotelID,
		}
		rooms = append(rooms, room)
	}

	return rooms, hotel
}

// Helper functions to map nested structures

func mapAmenities(expediaAmenities map[string]Amenity) []*Amenity {
	var amenities []*Amenity
	for _, amenity := range expediaAmenities {
		amenities = append(amenities, &Amenity{
			ID:         amenity.ID,
			Name:       amenity.Name,
			Categories: amenity.Categories,
			Value:      amenity.Value,
		})
	}

	return amenities
}

// MapExpediaAttributesToAttributes maps ExpediaAttributes to Attributes.
func MapExpediaAttributesToAttributes(expediaAttributes *ExpediaAttributes) *Attributes {
	if expediaAttributes == nil {
		return nil
	}

	return &Attributes{
		General: mapAttributes(expediaAttributes.General),
		Pets:    mapAttributes(expediaAttributes.Pets),
	}
}

// Helper function to map map[string]Attribute to []*Attribute.
func mapAttributes(expediaAttributes map[string]Attribute) []*Attribute {
	var attributes []*Attribute
	for _, attribute := range expediaAttributes {
		attributes = append(attributes, &Attribute{
			ID:    attribute.ID,
			Name:  attribute.Name,
			Value: attribute.Value,
		})
	}

	return attributes
}

func mapImages(expediaImages []Image) []*Image {
	var images []*Image
	for _, image := range expediaImages {
		images = append(images, &Image{
			HeroImage: image.HeroImage,
			Category:  image.Category,
			Links:     image.Links,
			Caption:   image.Caption,
		})
	}

	return images
}

func mapOnsitePayments(expediaOnsitePayments *ExpediaOnsitePayments) *OnsitePayments {
	if expediaOnsitePayments == nil {
		return nil
	}

	return &OnsitePayments{
		Currency: expediaOnsitePayments.Currency,
		Types:    mapPaymentTypes(expediaOnsitePayments.Types),
	}
}

func mapPaymentTypes(expediaPaymentTypes map[string]PaymentType) []*PaymentType {
	var paymentTypes []*PaymentType
	for _, paymentType := range expediaPaymentTypes {
		paymentTypes = append(paymentTypes, &PaymentType{
			ID:   paymentType.ID,
			Name: paymentType.Name,
		})
	}

	return paymentTypes
}

func mapRates(expediaRates map[string]ExpediaRate) []*ContentRate {
	var rates []*ContentRate
	for _, rate := range expediaRates {
		rates = append(rates, &ContentRate{
			ID:        rate.ID,
			Amenities: mapAmenities(rate.Amenities),
		})
	}

	return rates
}

func mapStatistics(expediaStatistics map[string]Statistic) []*Statistic {
	var statistics []*Statistic
	for _, statistic := range expediaStatistics {
		statistics = append(statistics, &Statistic{
			ID:    statistic.ID,
			Name:  statistic.Name,
			Value: statistic.Value,
		})
	}

	return statistics
}

func mapLanguages(expediaLanguages map[string]Language) map[string]*Language {
	languages := make(map[string]*Language)
	for key, language := range expediaLanguages {
		languages[key] = &Language{
			ID:   language.ID,
			Name: language.Name,
		}
	}

	return languages
}

func mapThemes(expediaThemes map[string]Theme) []*Theme {
	var themes []*Theme
	for _, theme := range expediaThemes {
		themes = append(themes, &Theme{
			ID:   theme.ID,
			Name: theme.Name,
		})
	}

	return themes
}

func mapBedGroups(expediaBedGroups map[string]BedGroup) []*BedGroup {
	var bedGroups []*BedGroup
	for _, bedGroup := range expediaBedGroups {
		bedGroups = append(bedGroups, &BedGroup{
			ID:            bedGroup.ID,
			Description:   bedGroup.Description,
			Configuration: mapBedConfigurations(bedGroup.Configuration),
		})
	}

	return bedGroups
}

func mapBedConfigurations(expediaBedConfigurations []*BedConfiguration) []*BedConfiguration {
	var bedConfigurations []*BedConfiguration
	for _, bedConfiguration := range expediaBedConfigurations {
		bedConfigurations = append(bedConfigurations, &BedConfiguration{
			Quantity: bedConfiguration.Quantity,
			Size:     bedConfiguration.Size,
			Type:     bedConfiguration.Type,
		})
	}

	return bedConfigurations
}

func mapOccupancy(expediaOccupancy *ExpediaOccupancy) *ContentOccupancy {
	if expediaOccupancy == nil {
		return nil
	}

	return &ContentOccupancy{
		MaxAllowed:    expediaOccupancy.MaxAllowed,
		AgeCategories: mapAgeCategories(expediaOccupancy.AgeCategories),
	}
}

func mapViews(expediaViews map[string]View) []*View {
	if expediaViews == nil {
		return nil
	}

	var views []*View
	for _, view := range expediaViews {
		views = append(views, &View{
			ID:   view.ID,
			Name: view.Name,
		})
	}

	return views
}

func mapAgeCategories(expediaAgeCategories map[string]AgeCategory) []*AgeCategory {
	var ageCategories []*AgeCategory
	for _, ageCategory := range expediaAgeCategories {
		ageCategories = append(ageCategories, &AgeCategory{
			Name:       ageCategory.Name,
			MinimumAge: ageCategory.MinimumAge,
		})
	}

	return ageCategories
}
