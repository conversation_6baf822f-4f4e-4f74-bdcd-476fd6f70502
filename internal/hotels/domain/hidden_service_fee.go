package domain

import (
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type HiddenServiceFeeConfig struct {
	LocationType enum.HotelLocationType
	CountryCode  []string
	Rating       []float64
	HotelType    []string
}

type HiddenServiceFee struct {
	Base
	OfficeID  string
	Type      enum.HiddenFeeType
	Config    *HiddenServiceFeeConfig
	Provider  commonEnum.HotelProvider
	HotelID   string
	HotelName string
	Amount    float64
	Percent   float64
}

type ListHiddenServiceFeeReq struct {
	OfficeID     string
	LocationType enum.HotelLocationType
	CountryCode  []string
	Rating       []float64
	HotelType    []string
	HotelID      string
	Provider     commonEnum.HotelProvider
	HotelName    string
	Type         enum.HiddenFeeType
	Pagination   *commonDomain.Pagination `json:"pagination"  validate:"required"`
}

type UpsertConfigHiddenFeeRequest struct {
	ID        string
	UserID    string
	OfficeID  string
	HotelID   string
	HotelName string
	Amount    float64
	Percent   float64
	Provider  commonEnum.HotelProvider
	Type      enum.HiddenFeeType
	Location  enum.HotelLocationType
	Countries []string
	Ratings   []float64
	HotelType []string
}

type CalculateHotelSearchPricesReq struct {
	Multiplier     uint32
	PartnershipID  string
	OfficeID       string
	HotelSummaries []*HotelSummary
	Provider       commonEnum.HotelProvider
}

type CalculateHotelDetailPricesReq struct {
	Multiplier    uint32
	PartnershipID string
	OfficeID      string
	HubHotel      *HubHotel
	Provider      commonEnum.HotelProvider
}

type CalculateHotelRateDataPricesReq struct {
	Multiplier    uint32
	PartnershipID string
	OfficeID      string
	Rating        float64
	CountryCode   string
	CategoryType  string
	HotelID       string
	HubRateData   *HubRateData
	Provider      commonEnum.HotelProvider
}
