package domain

import (
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

type HubPriceCheckReq struct {
	PartnershipID   string                     `json:"partnership_id"`
	OfficeID        string                     `json:"-"`
	SearchKey       string                     `json:"search_key" validate:"required"`
	HotelID         string                     `json:"hotel_id" validate:"required"`
	RoomID          string                     `json:"room_id" validate:"required"`
	RateID          string                     `json:"rate_id" validate:"required"`
	BedOptionID     string                     `json:"bed_option_id" validate:"required"`
	PNRCode         string                     `json:"pnr_code"`
	Stateful        bool                       `json:"stateful"`
	EnableProviders []commonEnum.HotelProvider `json:"-"`
	RequestCurrency string                     `json:"request_currency"`
	TestingHashKey  string                     `json:"testing_hash_key"`
}

type HubPriceCheckRes struct {
	ErrorRes
	WarningRes
	Hotel               *HubHotel         `json:"hotel"`
	SessionID           string            `json:"session_id,omitempty"`
	RateData            *HubRateData      `json:"rate_data"`
	ExchangedRateData   *HubRateData      `json:"exchanged_rate_data,omitempty"`
	RateDataCf          *HubRateData      `json:"rate_data_cf,omitempty"`
	ExchangedRateDataCf *HubRateData      `json:"exchanged_rate_data_cf,omitempty"`
	RawExRateDataCf     *HubRateData      `json:"-"`
	ProviderCfRate      *HubRateData      `json:"-"`
	MatchKey            string            `json:"match_key"`
	SessionInfo         *HotelSessionData `json:"-"`
}
