package domain

import (
	commonDomain "gitlab.deepgate.io/apps/common/domain"
)

type CurrencyRateMapping map[string]float64

type CurrencyExchange struct {
	Base `json:",inline"`
	From string  `json:"from"`
	To   string  `json:"to"`
	Rate float64 `json:"rate"`
}

type GetCurrencyExchangeDetailReq struct {
	ID string `json:"id"`
}

type ListCurrencyExchangeReq struct {
	From       *string                  `json:"from,omitempty"`
	To         *string                  `json:"to,omitempty"`
	Pagination *commonDomain.Pagination `json:"pagination,omitempty"`
}

type GetCurrencyExchangeDetailRes struct {
	CurrencyExchange *CurrencyExchange `json:"currency_exchange"`
}
