package domain

import "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"

type HubRetrieveBookingReq struct {
	OfficeID      string `json:"office_id"`
	PartnershipID string `json:"-"` // Partnership ID để phân biệt các partnership trong SUB Hub
	OrderCode     string `json:"order_code"`
	SessionID     string `json:"session_id"`
}

type HubRetrieveBookingRes struct {
	ErrorRes
	OrderCode     string             `json:"order_code"`
	Hotel         *HubOrderHotelItem `json:"hotel"`
	RateDataCf    *HubRateData       `json:"rate_data_cf"`
	BookingStatus enum.BookingStatus `json:"booking_status"`
	RefundAmount  float64            `json:"refund_amount"`
	Currency      string             `json:"currency"`
	Refunded      bool               `json:"refunded"`
	Cancelable    bool               `json:"cancelable"`
	VAT           bool               `json:"vat"`
}
