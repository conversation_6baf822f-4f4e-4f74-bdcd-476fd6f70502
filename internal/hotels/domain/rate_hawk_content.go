package domain

import (
	"fmt"
	"strings"
	"time"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	pkgConstants "gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

type RateHawkHotelContent struct {
	Address              string                 `json:"address"`
	AmenityGroups        []RateHawkAmenityGroup `json:"amenity_groups"`
	CheckInTime          string                 `json:"check_in_time"`
	CheckOutTime         string                 `json:"check_out_time"`
	DescriptionStruct    []RateHawkDescription  `json:"description_struct"`
	ID                   string                 `json:"id"`
	HID                  int                    `json:"hid"`
	Images               []string               `json:"images"`
	ImagesExt            []RateHawkImageExt     `json:"images_ext"`
	Kind                 string                 `json:"kind"`
	Latitude             float64                `json:"latitude"`
	Longitude            float64                `json:"longitude"`
	Name                 string                 `json:"name"`
	Phone                string                 `json:"phone"`
	PolicyStruct         []RateHawkPolicy       `json:"policy_struct"`
	PostalCode           string                 `json:"postal_code"`
	RoomGroups           []RateHawkRoomGroup    `json:"room_groups"`
	Region               RateHawkRegion         `json:"region"`
	StarRating           float64                `json:"star_rating"`
	Email                string                 `json:"email"`
	SerpFilters          []string               `json:"serp_filters"`
	Deleted              bool                   `json:"deleted"`
	IsClosed             bool                   `json:"is_closed"`
	IsGenderSpecRequired bool                   `json:"is_gender_specification_required"`
	MetaPolicyExtraInfo  string                 `json:"metapolicy_extra_info"`
	Facts                RateHawkFacts          `json:"facts"`
	PaymentMethods       []string               `json:"payment_methods"`
	HotelChain           string                 `json:"hotel_chain"`
	FrontDeskTimeStart   string                 `json:"front_desk_time_start"`
	FrontDeskTimeEnd     string                 `json:"front_desk_time_end"`
	KeysPickup           RateHawkKeysPickup     `json:"keys_pickup"`
}

type RateHawkAmenityGroup struct {
	Amenities        []string `json:"amenities"`
	NonFreeAmenities []string `json:"non_free_amenities"`
	GroupName        string   `json:"group_name"`
}

type RateHawkDescription struct {
	Paragraphs []string `json:"paragraphs"`
	Title      string   `json:"title"`
}

type RateHawkImageExt struct {
	URL          string `json:"url"`
	CategorySlug string `json:"category_slug"`
}

type RateHawkPolicy struct {
	Paragraphs []string `json:"paragraphs"`
	Title      string   `json:"title"`
}

type RateHawkRoomGroup struct {
	RoomGroupID   int                  `json:"room_group_id"`
	Images        []string             `json:"images"`
	Name          string               `json:"name"`
	RoomAmenities []string             `json:"room_amenities"`
	RgExt         RateHawkRoomGroupExt `json:"rg_ext"`
	NameStruct    map[string]string    `json:"name_struct"`
}

type RateHawkRoomGroupExt struct {
	Class    int `json:"class"`
	Quality  int `json:"quality"`
	Sex      int `json:"sex"`
	Bathroom int `json:"bathroom"`
	Bedding  int `json:"bedding"`
	Family   int `json:"family"`
	Capacity int `json:"capacity"`
	Club     int `json:"club"`
	Bedrooms int `json:"bedrooms"`
	Balcony  int `json:"balcony"`
	Floor    int `json:"floor"`
	View     int `json:"view"`
}

type RateHawkRegion struct {
	ID          int    `json:"id"`
	CountryCode string `json:"country_code"`
	IATA        string `json:"iata"`
	Name        string `json:"name"`
	Type        string `json:"type"`
	TypeV2      string `json:"type_v2"`
}

type RateHawkVisa struct {
	VisaSupport string `json:"visa_support"`
}

type RateHawkNoShow struct {
	Availability string     `json:"availability"`
	Time         *time.Time `json:"time"`
	DayPeriod    string     `json:"day_period"`
}

type RateHawkFacts struct {
	FloorsNumber  *int                `json:"floors_number"`
	RoomsNumber   int                 `json:"rooms_number"`
	YearBuilt     *int                `json:"year_built"`
	YearRenovated *int                `json:"year_renovated"`
	Electricity   RateHawkElectricity `json:"electricity"`
}

type RateHawkElectricity struct {
	Frequency []int    `json:"frequency"`
	Voltage   []int    `json:"voltage"`
	Sockets   []string `json:"sockets"`
}

type RateHawkKeysPickup struct {
	Type                      string  `json:"type"`
	Phone                     *string `json:"phone"`
	Email                     *string `json:"email"`
	ApartmentOfficeAddress    *string `json:"apartment_office_address"`
	ApartmentExtraInformation *string `json:"apartment_extra_information"`
	IsContactless             bool    `json:"is_contactless"`
}

func MapImageLink(imageStr string) map[string]*Link {
	hubPxToSize := map[string]string{
		"1000px": "1024x768",
		"350px":  "x500",
		"200px":  "240x240",
		"70px":   "100x100",
	}

	result := map[string]*Link{
		"1000px": nil,
		"350px":  nil,
		"200px":  nil,
		"70px":   nil,
	}

	for key := range result {
		size := hubPxToSize[key]
		if size == "" {
			size = "x500"
		}

		result[key] = &Link{
			Href: strings.ReplaceAll(imageStr, "{size}", size),
		}
	}

	return result
}

var BeddingMap = map[int]string{
	0: "",
	1: "Bunk Bed",
	2: "Single Bed",
	3: "Double Bed",
	4: "2 Single Beds",
	7: "Multiple Beds",
}

var RoomViewType = map[int]string{
	0:  "",
	1:  "Bay view",
	2:  "Bosphorus view",
	3:  "Burj-khalifa view",
	4:  "Canal view",
	5:  "City view",
	6:  "Courtyard view",
	7:  "Dubai-marina view",
	8:  "Garden view",
	9:  "Golf view",
	17: "Harbour view",
	18: "Inland view",
	19: "Kremlin view",
	20: "Lake view",
	21: "Land view",
	22: "Mountain view",
	23: "Ocean view",
	24: "Panoramic view",
	25: "Park view",
	26: "Partial-ocean view",
	27: "Partial-sea view",
	28: "Partial view",
	29: "Pool view",
	30: "River view",
	31: "Sea view",
	32: "Sheikh-zayed view",
	33: "Street view",
	34: "Sunrise view",
	35: "Sunset view",
	36: "Water view",
	37: "With view",
	38: "Beachfront",
	39: "Ocean front",
	40: "Sea front",
}

func GenRoomID(roomExt RoomGroupExt) string {
	return fmt.Sprintf(
		"%d%d%d%d%d%d%d%d%d%d%d%d",
		roomExt.Class,
		roomExt.Quality,
		roomExt.Sex,
		roomExt.Bathroom,
		roomExt.Bedding,
		roomExt.Family,
		roomExt.Capacity,
		roomExt.Club,
		roomExt.Bedrooms,
		roomExt.Balcony,
		roomExt.Floor,
		roomExt.View,
	)
}

func GetRoomKey(roomExt RoomGroupExt) string {
	return fmt.Sprintf(
		"%d-%d-%d-%d-%d-%d-%d-%d-%d-%d-%d-%d",
		roomExt.Class,
		roomExt.Quality,
		roomExt.Sex,
		roomExt.Bathroom,
		roomExt.Bedding,
		roomExt.Family,
		roomExt.Capacity,
		roomExt.Club,
		roomExt.Bedrooms,
		roomExt.Balcony,
		roomExt.Floor,
		roomExt.View,
	)
}

func IsSpecialLivingConditions(str string) bool {
	return str == "Special living conditions" ||
		str == "Điều kiện sống đặc biệt"
}

func IsExtraInfos(str string) bool {
	return str == "Extra info" ||
		str == "Thông tin bổ sung"
}

func IsGeneral(str string) bool {
	return str == "General" ||
		str == "Chung"
}

func IsPet(str string) bool {
	return str == "Pets" ||
		str == "Thú cưng"
}

func IsLanguageSpoken(str string) bool {
	return str == "Language Spoken" ||
		str == "Ngôn ngữ sử dụng"
}

func IsAtHotel(str string) bool {
	return str == "At the hotel" ||
		str == "Tại khách sạn"
}

func IsAtResort(str string) bool {
	return str == "At the resort" ||
		str == "Tại khu nghỉ dưỡng"
}

func IsRoomAmenities(str string) bool {
	return str == "Room amenities" ||
		str == "Tiện nghi phòng"
}

func IsLocation(str string) bool {
	return str == "Location" ||
		str == "Địa điểm"
}

func MapRateHawkHotelContentToRoomsAndHotel(content *RateHawkHotelContent, language string) ([]*Room, *Hotel) {
	if content == nil {
		return nil, nil
	}

	hotel := &Hotel{
		HotelID:                        fmt.Sprintf("%d", content.HID),
		Rank:                           0,
		Active:                         !content.IsClosed,
		MultiUnit:                      false,
		PaymentRegistrationRecommended: false,
		Language:                       language,
		Name:                           content.Name,
		Phone:                          content.Phone,
		Fax:                            "",
		SupplySource:                   "",
		RoomReferences:                 []*RoomRefInfo{},
		Address: &Address{
			Line1:               content.Address,
			City:                content.Region.Name,
			StateProvinceName:   "",
			PostalCode:          content.PostalCode,
			CountryCode:         content.Region.CountryCode,
			ObfuscationRequired: false,
		},
		Ratings: &Ratings{
			Property: &PropertyRating{
				Rating: fmt.Sprintf("%.1f", content.StarRating),
				Type:   "Star",
			},
			Guest: &GuestRating{},
		},
		Location: &Location{
			Coordinates: &Coordinates{
				Latitude:  content.Latitude,
				Longitude: content.Longitude,
			},
			ObfuscationRequired: false,
		},
		Category: &Category{
			ID:   content.Kind,
			Name: content.Kind,
		},
		CheckIn: &CheckIn{
			BeginTime: content.CheckInTime,
			EndTime:   content.FrontDeskTimeEnd,
		},
		Checkout: &Checkout{
			Time: content.CheckOutTime,
		},
		Fees:     &Fees{},
		Policies: &Policies{},
		Attributes: &Attributes{
			Pets:    []*Attribute{},
			General: []*Attribute{},
		},
		Amenities:             []*Amenity{},
		Images:                []*Image{},
		OnsitePayments:        &OnsitePayments{},
		Rates:                 []*ContentRate{},
		Dates:                 &Dates{},
		Descriptions:          &Descriptions{},
		Statistics:            []*Statistic{},
		AllInclusive:          &AllInclusive{},
		TaxID:                 "",
		RegistryNumber:        "",
		Chain:                 &Chain{},
		Brand:                 &Brand{},
		SpokenLanguages:       map[string]*Language{},
		Rooms:                 []*Room{},
		Themes:                []*Theme{},
		VacationRentalDetails: &VacationRentalDetails{},
		ProviderIds: map[commonEnum.HotelProvider]string{
			commonEnum.HotelProviderRateHawk: content.ID,
		},
		Timezone: "",
		Version:  "1",
		Distance: 0,
	}

	for _, policy := range content.PolicyStruct {
		isSpecialLivingConditionsInfo := IsSpecialLivingConditions(policy.Title)
		IsExtraInfo := IsExtraInfos(policy.Title)

		if isSpecialLivingConditionsInfo {
			hotel.Fees.Mandatory = strings.Join(policy.Paragraphs, "\n")
		}

		if !isSpecialLivingConditionsInfo && !IsExtraInfo {
			hotel.Fees.Optional = fmt.Sprintf("%s:\n%s", policy.Title, strings.Join(policy.Paragraphs, "\n"))
		}
	}

	for _, amenity := range content.AmenityGroups {
		for _, value := range amenity.Amenities {
			switch true {
			case IsGeneral(amenity.GroupName):
				hotel.Attributes.General = append(hotel.Attributes.General, &Attribute{
					ID:    value,
					Name:  value,
					Value: value,
				})
			case IsPet(amenity.GroupName):
				hotel.Attributes.Pets = append(hotel.Attributes.Pets, &Attribute{
					ID:    value,
					Name:  value,
					Value: value,
				})
			case IsLanguageSpoken(amenity.GroupName):
				hotel.SpokenLanguages[value] = &Language{
					ID:   value,
					Name: value,
				}
			default:
				hotel.Amenities = append(hotel.Amenities, &Amenity{
					ID:        value,
					Name:      value,
					Value:     value,
					GroupName: amenity.GroupName,
				})
			}
		}
	}

	for _, paymentMethods := range content.PaymentMethods {
		hotel.OnsitePayments.Types = append(hotel.OnsitePayments.Types, &PaymentType{
			ID:   paymentMethods,
			Name: paymentMethods,
		})
	}

	for _, description := range content.DescriptionStruct {
		switch true {
		case IsAtHotel(description.Title), IsAtResort(description.Title):
			hotel.Descriptions.Amenities = fmt.Sprintf("%s:\n%s", description.Title, strings.Join(description.Paragraphs, "\n"))
		case IsRoomAmenities(description.Title):
			hotel.Descriptions.Rooms = strings.Join(description.Paragraphs, "\n")
		case IsLocation(description.Title):
			hotel.Descriptions.Location = strings.Join(description.Paragraphs, "\n")
		}
	}

	isHaveHeroImage := false

	for _, image := range content.ImagesExt {
		isHeroImage := image.CategorySlug == "hotel_rooms" || image.CategorySlug == "hotel_front"
		if !isHaveHeroImage {
			isHaveHeroImage = isHeroImage
		}

		hotel.Images = append(hotel.Images, &Image{
			HeroImage: isHeroImage,
			Links:     MapImageLink(image.URL),
			Caption:   image.CategorySlug,
		})
	}

	if !isHaveHeroImage && len(hotel.Images) > 0 {
		hotel.Images[0].HeroImage = true
	}

	rooms := make([]*Room, 0, len(content.RoomGroups))

	for _, room := range content.RoomGroups {
		item := &Room{
			Name:      room.Name,
			Amenities: []*Amenity{},
			Images:    []*Image{},
			BedGroups: []*BedGroup{
				{
					Description: room.NameStruct["bedding_type"],
					Configuration: []*BedConfiguration{
						{
							Type: BeddingMap[room.RgExt.Bedding],
						},
					},
				},
			},
			Area:      &Area{},
			Occupancy: &ContentOccupancy{},
			Views: []*View{
				{
					ID:   fmt.Sprintf("%d", room.RgExt.View),
					Name: RoomViewType[room.RgExt.View],
				},
			},
			Language: language,
			Version:  "1",
		}

		for _, amenity := range room.RoomAmenities {
			locale, ok := pkgConstants.LanguageMap[language]
			name := ""

			if !ok {
				name = amenity
			}

			amenityName, ok := pkgConstants.AmenitiesMap[amenity]
			if !ok {
				name = amenity
			}

			name, ok = amenityName[locale]
			if !ok {
				name = amenity
			}

			item.Amenities = append(item.Amenities, &Amenity{
				ID:   amenity,
				Name: name,
			})
		}

		for _, image := range room.Images {
			item.Images = append(item.Images, &Image{
				Links: MapImageLink(image),
			})
		}

		item.RoomGroupExt = RoomGroupExt{
			Class:    room.RgExt.Class,
			Quality:  room.RgExt.Quality,
			Sex:      room.RgExt.Sex,
			Bathroom: room.RgExt.Bathroom,
			Bedding:  room.RgExt.Bedding,
			Family:   room.RgExt.Family,
			Capacity: room.RgExt.Capacity,
			Club:     room.RgExt.Club,
			Bedrooms: room.RgExt.Bedrooms,
			Balcony:  room.RgExt.Balcony,
			Floor:    room.RgExt.Floor,
			View:     room.RgExt.View,
		}

		item.ProviderIDs = map[commonEnum.HotelProvider]string{
			commonEnum.HotelProviderRateHawk: fmt.Sprintf("%s_%s", content.ID, GetRoomKey(item.RoomGroupExt)),
		}

		item.RoomID = fmt.Sprintf("%s%d%s", hotel.HotelID, room.RoomGroupID, GenRoomID(item.RoomGroupExt))

		rooms = append(rooms, item)
	}

	return rooms, hotel
}

type RateHawkName struct {
	En string `json:"en"`
	Vi string `json:"vi"`
}

type RateHawkCoordinate struct {
	Longitude float64 `json:"longitude"`
	Latitude  float64 `json:"latitude"`
}

type RateHawkRegionContent struct {
	CountryName RateHawkName       `json:"country_name"`
	CountryCode string             `json:"country_code"`
	Center      RateHawkCoordinate `json:"center"`
	Hotels      []string           `json:"hotels"`
	Iata        string             `json:"iata"`
	ID          int                `json:"id"`
	Type        string             `json:"type"`
	Name        RateHawkName       `json:"name"`
}

var RegionTypeMap = map[string]enum.RegionType{
	"Continent":                       enum.Continent,
	"Country":                         enum.Country,
	"Province (State)":                enum.ProvinceState,
	"Multi-Region (within a country)": enum.HighLevelRegion,
	"Multi-City (Vicinity)":           enum.MultiCityVicinity,
	"City":                            enum.City,
	"Neighborhood":                    enum.Neighborhood,
	"Airport":                         enum.Airport,
	"Point of Interest":               enum.PointOfInterest,
	"Railway Station":                 enum.TrainStation,
	"Subway (Entrace)":                enum.MetroStation,
	"Bus Station":                     enum.BusStation,
	"Street":                          enum.Street,
}

func MapRateHawkRegionContentToRegion(content *RateHawkRegionContent, language string) *Region {
	if content == nil {
		return nil
	}

	name := ""

	switch language {
	case "en-US":
		name = content.Name.En
	case "vi-VN":
		name = content.Name.Vi
	}

	result := &Region{
		RegionID:    fmt.Sprintf("%d-%d", commonEnum.HotelProviderRateHawk, content.ID),
		Type:        enum.RegionTypeName[RegionTypeMap[content.Type]],
		Name:        name,
		FullName:    name,
		CountryCode: content.CountryCode,
		Coordinates: &RegionCoordinates{
			CenterLongitude: content.Center.Longitude,
			CenterLatitude:  content.Center.Latitude,
		},
		PropertyIDs: content.Hotels,
		Language:    language,
		Version:     "1",
	}

	return result
}

type RateHawkDetailRating struct {
	Cleanness float64 `json:"cleanness"`
	Location  float64 `json:"location"`
	Price     float64 `json:"price"`
	Services  float64 `json:"services"`
	Room      float64 `json:"room"`
	Meal      float64 `json:"meal"`
	Wifi      float64 `json:"wifi"`
	Hygiene   float64 `json:"hygiene"`
}

type RateHawkCommentDetailRating struct {
	Cleanness float64 `json:"cleanness"`
	Location  float64 `json:"location"`
	Price     float64 `json:"price"`
	Services  float64 `json:"services"`
	Room      float64 `json:"room"`
	Meal      float64 `json:"meal"`
	Wifi      string  `json:"wifi"`
	Hygiene   string  `json:"hygiene"`
}

type RateHawkReviewComment struct {
	ID           int                         `json:"id"`
	ReviewPlus   any                         `json:"review_plus"`
	ReviewMinus  any                         `json:"review_minus"`
	Created      string                      `json:"created"`
	Author       string                      `json:"author"`
	Adults       int                         `json:"adults"`
	Children     int                         `json:"children"`
	RoomName     string                      `json:"room_name"`
	Nights       int                         `json:"nights"`
	Detailed     RateHawkCommentDetailRating `json:"detailed"`
	TravelerType string                      `json:"traveller_type"`
	TripType     string                      `json:"trip_type"`
	Rating       float64                     `json:"rating"`
}

type RateHawkHotelReview struct {
	Hid             int                  `json:"hid"`
	PropertyID      string               `json:"property_id"`
	Rating          float64              `json:"rating"`
	DetailedRatings RateHawkDetailRating `json:"detailed_ratings"`
	// Reviews         []RateHawkReviewComment `json:"reviews"`
}

func (s *RateHawkHotelReview) ToHotelGuestRating() (string, *GuestRating) {
	if s == nil {
		return "", nil
	}

	hotelID := fmt.Sprintf("%d", s.Hid)

	amenityRating := float64(0)
	amenityRatingCount := float64(0)

	if s.DetailedRatings.Meal > 0 {
		amenityRating += s.DetailedRatings.Meal / 2
		amenityRatingCount++
	}

	if s.DetailedRatings.Wifi > 0 {
		amenityRating += s.DetailedRatings.Wifi / 2
		amenityRatingCount++
	}

	if s.DetailedRatings.Hygiene > 0 {
		amenityRating += s.DetailedRatings.Hygiene / 2
		amenityRatingCount++
	}

	if amenityRatingCount > 0 {
		amenityRating = amenityRating / amenityRatingCount
	}

	guestRating := &GuestRating{
		Overall:     fmt.Sprintf("%.1f", s.Rating/2),
		Cleanliness: fmt.Sprintf("%.1f", s.DetailedRatings.Cleanness/2),
		Service:     fmt.Sprintf("%.1f", s.DetailedRatings.Services/2),
		Comfort:     fmt.Sprintf("%.1f", s.DetailedRatings.Room/2),
		Location:    fmt.Sprintf("%.1f", s.DetailedRatings.Location/2),
		Quality:     fmt.Sprintf("%.1f", s.DetailedRatings.Room/2),
		Value:       fmt.Sprintf("%.1f", s.DetailedRatings.Price/2),
		Amenities:   fmt.Sprintf("%.1f", amenityRating),
	}

	return hotelID, guestRating
}
