package domain

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"strings"

	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/logger"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type SearchHotelSortReq struct {
	SortType commonEnum.SearchHotelSortItemType `json:"sort_type"`
	Desc     bool                               `json:"desc"`
}

type Place struct {
	PlaceID     string                 `json:"id"`
	Name        string                 `json:"name"`
	Type        enum.PlaceType         `json:"type"`
	Lang        string                 `json:"language"`
	Location    *Coordinates           `json:"location"`
	Address     string                 `json:"address"`
	CountryCode string                 `json:"country_code"`
	Source      commonEnum.PlaceSource `json:"source"`
}

type HubSearchHotelRequest struct {
	PartnershipID            string                     `json:"-"`
	SearchHotelFilterRequest *SearchHotelFilterRequest  `json:"search_hotel_filter_request"`
	GeneralOccupancy         *HubSearchGeneralOccupancy `json:"general_occupancy"`
	Occupancies              []*HubSearchOccupancy      `json:"occupancies"`
	Stay                     HubSearchStay              `json:"stay" validate:"required"`
	CountryCode              string                     `json:"country_code"`
	HotelIds                 []string                   `json:"hotel_ids"`
	Place                    *Place                     `json:"place"`
	SaleChannel              enum.SalesChannel          `json:"sale_channel"`
	TravelPurpose            enum.TravelPurpose         `json:"travel_purpose"`
	Pagination               *commonDomain.Pagination   `json:"pagination"  validate:"required"`
	SalesEnv                 enum.SalesEnvironment      `json:"sales_environment"`
	OfficeID                 string                     `json:"-"`
	Language                 string                     `json:"language"`
	DefaultLanguage          string                     `json:"-"`
	EnableProviders          []commonEnum.HotelProvider `json:"-"`
	Sort                     []*SearchHotelSortReq      `json:"sort"`
	PriceConditionConfig     *HotelPriceConditionConfig `json:"-"`
	RequestCurrency          string                     `json:"request_currency"`
	ExcludeContent           bool                       `json:"exclude_content"`
	ImageMode                enum.ImageModeType         `json:"image_mode"`
	NoCache                  bool                       `json:"no_cache"`
	TracingID                string                     `json:"tracing_id"`
}

type SearchHotelFilterRequest struct {
	Ratings             []float64                         `json:"ratings"`
	AccommodationTypes  []string                          `json:"accommodation_types"`
	Amenities           []string                          `json:"amenities"`
	MinPrice            float64                           `json:"min_price"`
	MaxPrice            float64                           `json:"max_price"`
	CustomerRatings     []enum.CustomerRatingFilterOption `json:"customer_ratings,omitempty"`
	MinDistanceToCenter float64                           `json:"min_distance_to_center,omitempty"`
	MaxDistanceToCenter float64                           `json:"max_distance_to_center,omitempty"`
	PetFriendly         bool                              `json:"-"`
	BreakfastIncluded   bool                              `json:"-"`
}

func (filter *SearchHotelFilterRequest) IsDefault() bool {
	if filter == nil {
		return false
	}

	return len(filter.Ratings) == 0 &&
		len(filter.AccommodationTypes) == 0 &&
		len(filter.Amenities) == 0 &&
		filter.MinPrice == 0.0 &&
		filter.MaxPrice == 0.0 &&
		(len(filter.CustomerRatings) == 0 || (filter.CustomerRatings[0] == enum.CustomerRatingFilterOptionNone)) &&
		filter.MinDistanceToCenter == 0.0 &&
		filter.MaxDistanceToCenter == 0.0 &&
		!filter.PetFriendly &&
		!filter.BreakfastIncluded
}

func (req *HubSearchHotelRequest) IsSortDefault() bool {
	return req == nil ||
		(len(req.Sort) == 0 ||
			(len(req.Sort) == 1 && req.Sort[0].SortType == commonEnum.SearchHotelSortItemTypeNone))
}

// CheckGeneralOccupancy checks if general occupancy is enabled.
func (d HubSearchHotelRequest) CheckGeneralOccupancy() bool {
	return d.GeneralOccupancy != nil && d.GeneralOccupancy.Priority
}

// CountRooms returns the total number of rooms base on occupancies or general occupancy.
func (d HubSearchHotelRequest) CountRooms() int {
	var roomCount int

	isGenOccupancy := d.CheckGeneralOccupancy()

	// Case: General occupancy takes priority
	if isGenOccupancy {
		roomCount = int(d.GeneralOccupancy.Rooms)
	} else {
		for _, occupancy := range d.Occupancies {
			roomCount += int(occupancy.Rooms)
		}
	}

	d.Stay.RoomCount = roomCount

	return roomCount
}

// ConvertGeneralToOccupancies converts general occupancy to occupancies if priority is enabled.
func (d HubSearchHotelRequest) ConvertGeneralToOccupancies(general *HubSearchGeneralOccupancy) []*HubSearchOccupancy {
	isGenOccupancy := d.CheckGeneralOccupancy()
	if !isGenOccupancy || general == nil || general.Rooms == 0 {
		return nil
	}

	totalAdults := int(general.Adults)
	totalChildren := 0
	var childrenAges []uint

	if general.Children != nil {
		totalChildren = int(general.Children.Number)
		childrenAges = append([]uint{}, general.Children.Age...)
	}

	totalRooms := int(general.Rooms)
	occupancies := make([]*HubSearchOccupancy, totalRooms)

	// Initialize all occupancy slots
	for i := 0; i < totalRooms; i++ {
		occupancies[i] = &HubSearchOccupancy{
			OccupancyIndex: uint(i + 1),
			Rooms:          1,
			Adults:         0,
			Children: &HubSearchChildren{
				Number: 0,
				Age:    []uint{},
			},
		}
	}

	// Distribute adult per room first
	for i := 0; i < totalRooms && totalAdults > 0; i++ {
		occupancies[i].Adults = 1
		totalAdults--
	}

	// Adt not assign to room
	if totalAdults > 0 {
		adultsPerRoom := uint(totalAdults / totalRooms)
		remainder := totalAdults % totalRooms

		// Assign adults to each room
		for i := 0; i < totalRooms; i++ {
			occupancies[i].Adults += adultsPerRoom
			if i < remainder {
				occupancies[i].Adults++
			}
		}
	}

	// Distribute children sequentially into rooms
	childIdx := 0
	for i := 0; i < totalRooms && childIdx < totalChildren; i++ {
		childrenPerRoom := totalChildren / totalRooms
		if i < totalChildren%totalRooms {
			childrenPerRoom++ // Distribute the remainder
		}

		for j := 0; j < childrenPerRoom && childIdx < len(childrenAges); j++ {
			occupancies[i].Children.Age = append(occupancies[i].Children.Age, childrenAges[childIdx])
			occupancies[i].Children.Number++
			childIdx++
		}
	}

	return occupancies
}

type SearchHotelFilterResponse struct {
	Ratings             []float64                         `json:"ratings"`
	AccommodationTypes  []string                          `json:"accommodation_types"`
	Amenities           []string                          `json:"amenities"`
	MinPrice            float64                           `json:"min_price"`
	MaxPrice            float64                           `json:"max_price"`
	Currency            string                            `json:"currency"`
	CustomerRatings     []enum.CustomerRatingFilterOption `json:"customer_ratings,omitempty"`
	MinDistanceToCenter float64                           `json:"min_distance_to_center,omitempty"`
	MaxDistanceToCenter float64                           `json:"max_distance_to_center,omitempty"`
	DistanceUnit        string                            `json:"-"`
	PetFriendly         bool                              `json:"-"`
	BreakfastIncluded   bool                              `json:"-"`
}

type HubSearchHotelResponse struct {
	ErrorRes
	SearchHotelFilterRequest *SearchHotelFilterResponse `json:"search_hotel_filter_request,omitempty"`
	SearchKey                string                     `json:"search_key,omitempty"`
	HotelSummary             []*HotelSummary            `json:"hotel_summary"`
	Occupancies              []*HubSearchOccupancy      `json:"occupancies,omitempty"`
	Pagination               *commonDomain.Pagination   `json:"pagination"`
}

func (h *HubSearchHotelRequest) GenSearchKey() (string, error) {
	occupancyHash := make([]string, 0, len(h.Occupancies))

	for _, item := range h.Occupancies {
		if item == nil {
			logger.Error("HubSearchHotelRequest GenSearchKey occupancy nil")
			return "", ErrInvalidValue
		}
		childrenStr := ""

		if item.Children != nil {
			for _, age := range item.Children.Age {
				childrenStr += fmt.Sprintf("%d|", age)
			}
		}

		occupancyHash = append(occupancyHash, fmt.Sprintf("%d|%s|%d", item.Adults, childrenStr, item.Rooms))
	}

	var hash [16]byte

	additionHash := ""
	if !h.ExcludeContent || h.ImageMode != enum.ImageModeTypeFull {
		additionHash = fmt.Sprintf("_%t_%s", h.ExcludeContent, h.ImageMode)
	}

	if h.Place != nil {
		hash = md5.Sum([]byte(fmt.Sprintf("%s__%s__%s__%s__%d__%s__%s", h.Stay.CheckIn, h.Stay.CheckOut, strings.Join(occupancyHash, "_"), h.Place.PlaceID, h.Place.Type, h.SalesEnv, h.Language) + additionHash))
	} else if len(h.HotelIds) != 0 {
		hash = md5.Sum([]byte(fmt.Sprintf("%s__%s__%s__%s__%s__%s", h.Stay.CheckIn, h.Stay.CheckOut, strings.Join(occupancyHash, "_"), strings.Join(h.HotelIds, "_"), h.SalesEnv, h.Language) + additionHash))
	}

	hashString := hex.EncodeToString(hash[:])

	return hashString, nil
}

type PricePerNight struct {
	DiscountPrice float64 `json:"discount_price"`
	OriginalPrice float64 `json:"original_price"`
}

type Price struct {
	PricePerNight    *PricePerNight `json:"price_per_night"`
	Total            float64        `json:"total_price"`
	IsIncludeTax     bool           `json:"is_include_tax"`
	SaleScenario     []string       `json:"-"`
	Currency         string         `json:"currency"`
	PayAtHotel       []*PayAtHotel  `json:"pay_at_hotel"`
	TotalPayAtHotel  float64        `json:"total_pay_at_hotel"`
	HiddenFee        float64        `json:"-"`
	HasBreakfast     bool           `json:"has_breakfast"`
	HasExtraBed      bool           `json:"has_extra_bed"`
	NonSmoking       bool           `json:"non_smoking"`
	Refundable       bool           `json:"refundable"`
	AppliedHiddenFee interface{}    `json:"-"`
	AppliedDiscount  interface{}    `json:"-"`
}

type HotelSummaryImageLink struct {
	Px70   string `json:"px70,omitempty"`
	Px200  string `json:"px200,omitempty"`
	Px350  string `json:"px350,omitempty"`
	Px1000 string `json:"px1000,omitempty"`
}

type HotelSummaryImage struct {
	HeroImage bool                  `json:"hero_image,omitempty"`
	Category  float64               `json:"category,omitempty"`
	Links     HotelSummaryImageLink `json:"links,omitempty"`
	Caption   string                `json:"caption,omitempty"`
}

type HotelSummary struct {
	ID                    string                              `json:"id"`
	ProviderHotelID       string                              `json:"-"`
	ProviderIds           map[commonEnum.HotelProvider]string `json:"-"`
	Provider              commonEnum.HotelProvider            `json:"-"`
	MatchKey              string                              `json:"match_key"`
	Name                  string                              `json:"name"`
	NameEn                string                              `json:"name_en"`
	Location              string                              `json:"location,omitempty"`
	Review                *HotelReview                        `json:"review,omitempty"`
	Amenities             []*Amenity                          `json:"amenities,omitempty"`
	CategoryType          string                              `json:"category_type,omitempty"`
	ThumbnailURL          []*HotelSummaryImage                `json:"thumbnail_url"`
	DisplayImage          *HotelSummaryImage                  `json:"display_image,omitempty"`
	Rating                float64                             `json:"rating,omitempty"`
	Price                 *Price                              `json:"price"`
	ExchangedPrice        *Price                              `json:"exchanged_price,omitempty"`
	CenterInfo            *CenterInfo                         `json:"center_info,omitempty"`
	CountryCode           string                              `json:"country_code,omitempty"`
	Available             bool                                `json:"available"`
	RoomLeft              int                                 `json:"room_left"`
	CheckIn               string                              `json:"check_in,omitempty"`
	CheckOut              string                              `json:"check_out,omitempty"`
	HotelPriority         bool                                `json:"hotel_priority,omitempty"`
	ApplicableNationality []*ApplicableNationality            `json:"applicable_nationality,omitempty"`
}

type CenterInfo struct {
	CenterName       string  `json:"center_name"`
	DistanceToCenter float64 `json:"distance_to_center"`
	Unit             string  `json:"unit,omitempty"`
}

type FilterOption struct {
	Value string `json:"value"`
	Count int32  `json:"count"`
}

type DistanceRange struct {
	Label       string  `json:"label"`
	MinDistance float64 `json:"min_distance"`
	MaxDistance float64 `json:"max_distance"`
}

type DestinationFilterOptions struct {
	StarRatings        []*FilterOption  `json:"star_ratings"`
	AccommodationTypes []*FilterOption  `json:"accommodation_types"`
	Amenities          []*FilterOption  `json:"amenities"`
	DistanceToCenter   []*DistanceRange `json:"distance_to_center"`
	CustomerRatings    []*FilterOption  `json:"customer_ratings"`
}

type HotelReview struct {
	Rate        float64            `json:"rate,omitempty"`
	ReviewCount int32              `json:"review_count,omitempty"`
	Label       string             `json:"label,omitempty"`
	Detail      *HotelReviewDetail `json:"detail,omitempty"`
}

type HotelReviewDetail struct {
	Reviews []*Review       `json:"reviews"`
	Rating  *CriteriaRating `json:"rating"`
}

type Review struct {
	ReviewID     string   `json:"review_id"`
	UserName     string   `json:"user_name"`
	UserType     string   `json:"user_type"`
	Date         string   `json:"date"`
	Rating       int32    `json:"rating"`
	Title        string   `json:"title"`
	Content      string   `json:"content"`
	Liked        []string `json:"liked"`
	StayDuration string   `json:"stay_duration"`
}
type CriteriaRating struct {
	Cleanliness                  float64 `json:"cleanliness"`
	StaffService                 float64 `json:"staff_service"`
	Amenities                    float64 `json:"amenities"`
	PropertyConditionsFacilities float64 `json:"property_conditions_facilities"`
	EcoFriendliness              float64 `json:"eco_friendliness"`
}

type FilterLocation struct {
	Lat         float64 `json:"latitude"`
	Lon         float64 `json:"longitude"`
	MaxDistance float64 `json:"max_distance"`
	MinDistance float64 `json:"min_distance"`
}
