package domain

type ExpediaSessionInfo struct {
	RetrieveBookingSession string `json:"-"`
	HoldBookingSession     string `json:"-"`
	CancelBookingSession   string `json:"-"`
}

type TourmindSessionInfo struct {
	RateCode  string `json:"-"`
	HotelCode int    `json:"-"`
}

type DidaSessionInfo struct {
	ReferenceNo            string
	CancelBookingConfirmID string
}

type BasicSessionInfo struct {
	BookingKey string
}

type HubSessionInfo struct {
	SessionID string
}

type AgodaSessionInfo struct {
	SearchID     int64
	PaymentModel string
}

type HotelSessionData struct {
	ExpediaSessionInfo  *ExpediaSessionInfo
	TourmindSessionInfo *TourmindSessionInfo
	DidaSessionInfo     *DidaSessionInfo
	BasicSessionInfo    *BasicSessionInfo
	HubSessionInfo      *HubSessionInfo
	AgodaSessionInfo    *AgodaSessionInfo
}

type HotelSession struct {
	SessionID           string
	OfficeID            string
	ExpiredAt           int64
	ExpediaSessionInfo  *ExpediaSessionInfo  `json:"-"`
	TourmindSessionInfo *TourmindSessionInfo `json:"-"`
	DidaSessionInfo     *DidaSessionInfo     `json:"-"`
	BasicSessionInfo    *BasicSessionInfo    `json:"-"`
	HubSessionInfo      *HubSessionInfo      `json:"-"`
	AgodaSessionInfo    *AgodaSessionInfo    `json:"-"`
}
