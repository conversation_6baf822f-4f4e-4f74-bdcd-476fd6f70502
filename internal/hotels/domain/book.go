package domain

import (
	commonDomain "gitlab.deepgate.io/apps/common/domain"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type HubBookReq struct {
	OfficeID             string                `json:"-"`
	HubOfficeID          string                `json:"hub_office_id" validate:"required"`
	PartnershipID        string                `json:"-"` // Partnership ID để phân biệt các partnership trong SUB Hub
	SessionID            string                `json:"session_id" validate:"required"`
	Holder               *HubHolderInfo        `json:"holder" validate:"required"`
	EndUserIPAddress     string                `json:"-"`
	EndUserBrowserAgent  string                `json:"-"`
	AgentID              string                `json:"-"`
	OrderCode            string                `json:"-"` // Send to provider
	TestingHashKey       string                `json:"testing_hash_key"`
	IsInvoice            bool                  `json:"is_invoice,omitempty"`
	InvoicingInformation *InvoicingInformation `json:"invoicing_information,omitempty"` // thông tin xuất hóa đơn cho khách
	ClientOrderCode      string                `json:"client_order_code"`
}

type InvoicingInformationCompany struct {
	Name    string `json:"name" validate:"required"`
	TaxCode string `json:"tax_code" validate:"required"`
	Address string `json:"address" validate:"required"`
}

type InvoicingInformationReceiver struct {
	Name        string `json:"name" validate:"required"`
	PhoneCode   string `json:"phone_code" validate:"required"`
	PhoneNumber string `json:"phone_number" validate:"required"`
	Email       string `json:"email" validate:"required"`
	Address     string `json:"address" validate:"required"`
	Note        string `json:"note"`
}

type InvoicingInformation struct {
	Company  *InvoicingInformationCompany  `json:"company"`
	Receiver *InvoicingInformationReceiver `json:"receiver"`
}

type HubBookRes struct {
	OrderCode       string             `json:"order_code"`
	BookingStatus   enum.BookingStatus `json:"booking_status"`
	PendingDeadline int64              `json:"pending_deadline,omitempty"`
	ErrorRes
}

type ListBookingRequest struct {
	Pagination       *commonDomain.Pagination
	BookingStatus    enum.BookingStatus
	OrderStatus      enum.HubOrderStatus
	OrderKey         string
	OrderVal         int
	PendingStartAtLt int64
}

type HubBookOldProviderReq struct {
	OrderCode string
	ID        string
}

type ListOrderFilter struct {
	PartnershipID   string
	Pagination      *commonDomain.Pagination
	BookingStatuses []enum.BookingStatus
	From            int64
	To              int64
	OfficeID        *string
	OrderCode       string
	NotInStatuses   []enum.BookingStatus
	ManagerID       string
	ManageOfficeIDs []string
	UserID          string
	Roles           []string
	UnknownPending  *bool
}
