package domain

import (
	"sort"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/log"
)

type LoginReq struct {
	OfficeID      string `json:"office_id"`
	APIKey        string `json:"api_key"`
	PartnershipID string `json:"partnership_id"`
}

type PartnerUser struct {
	ID            string
	CreatedAt     int64
	UpdatedAt     int64
	CreatedBy     string
	UpdatedBy     string
	Email         string
	Name          string
	PartnershipID string
	PartnerShopID string
	WebhookCfg    WebhookCfg
}

type PartnerDCPs struct{}

type PartnerShopInfo struct {
	ID          string
	Name        string
	OwnerID     string
	PartnerType int64
	Code        string
	OfficeID    string
	WebhookCfg  *WebhookCfg
	DCPs        *PartnerDCPs
	Hotel       *PartnerHotelInfo
}

type ProviderConfig struct {
	Provider commonEnum.HotelProvider `json:"provider" bson:"provider"`
	Enable   bool                     `json:"enable" bson:"enable"`
}

type PartnerHotelInfo struct {
	Enable          bool                       `json:"enable" bson:"enabled"`
	Webhook         *WebhookURLCfg             `json:"webhook" bson:"webhook"`
	ProviderConfigs []*ProviderConfig          `json:"providers" bson:"providers"`
	DefaultLanguage string                     `json:"default_language" bson:"default_language"`
	PriceConfig     *HotelPriceConditionConfig `json:"price_config" bson:"price_config"`
}

type HotelPriceConditionDetail struct {
	Percent float64
	Amount  float64
}

type HotelPriceConditionConfig struct {
	ProviderPriceRange map[int64]HotelPriceConditionDetail
	ProviderOrder      []int64
	Active             bool
}

type multiProviderComparePrice struct {
	Provider commonEnum.HotelProvider
	Price    float64
}

func (d *HotelPriceConditionConfig) SelectHotelProviderByOrder(hotels []*HotelSummary) *HotelSummary {
	if d == nil || len(hotels) == 0 {
		return nil
	}

	rawMultiProviderComparePrices := make([]multiProviderComparePrice, 0, len(hotels))

	for _, hotel := range hotels {
		providerPriceConfig, ok := d.ProviderPriceRange[int64(hotel.Provider)]
		if !ok {
			providerPriceConfig = HotelPriceConditionDetail{
				Percent: 0,
				Amount:  0,
			}
		}

		price := hotel.Price.Total*(1+providerPriceConfig.Percent) + providerPriceConfig.Amount

		log.Info("Calc price", log.Any("hotel.Price.Total", hotel.Price.Total),
			log.Any("hotel.Provider", hotel.Provider),
			log.Any("providerPriceConfig", providerPriceConfig))

		rawMultiProviderComparePrices = append(rawMultiProviderComparePrices, multiProviderComparePrice{
			Provider: hotel.Provider,
			Price:    price,
		})
	}

	multiProviderComparePrices := make([]multiProviderComparePrice, 0)

	for _, item := range rawMultiProviderComparePrices {
		if item.Provider == commonEnum.HotelProviderHNHTravelAgent || item.Provider == commonEnum.HotelProviderMayTravelAgent || item.Provider == commonEnum.HotelProviderBZTTravelAgent {
			multiProviderComparePrices = append(multiProviderComparePrices, item)
		}
	}

	if len(multiProviderComparePrices) == 0 {
		multiProviderComparePrices = rawMultiProviderComparePrices
	}

	providerOrderMap := map[commonEnum.HotelProvider]int{}

	for i, provider := range d.ProviderOrder {
		providerOrderMap[commonEnum.HotelProvider(provider)] = i
	}

	sort.Slice(multiProviderComparePrices, func(i, j int) bool {
		if multiProviderComparePrices[i].Price == multiProviderComparePrices[j].Price {
			firstOrder, ok := providerOrderMap[multiProviderComparePrices[i].Provider]
			if !ok {
				firstOrder = 99
			}

			secOrder, ok := providerOrderMap[multiProviderComparePrices[j].Provider]
			if !ok {
				secOrder = 99
			}

			return firstOrder < secOrder
		}

		return multiProviderComparePrices[i].Price < multiProviderComparePrices[j].Price
	})

	log.Info("Compare price", log.Any("multiProviderComparePrices", multiProviderComparePrices))

	selectProvider := multiProviderComparePrices[0].Provider
	for _, hotel := range hotels {
		if hotel.Provider == selectProvider {
			log.Info("selected result", log.Any("hotel", hotel.ID), log.Any("provider", selectProvider))
			return hotel
		}
	}

	return hotels[0]
}

type WebhookURLCfg struct {
	Transaction    string
	ConfirmationID string
}

type WebhookCfg struct {
	WebhookURLCfg WebhookURLCfg
	WebhookKey    string
}

type ShopsBySaleCode struct {
	SaleCode string `json:"sale_code"`
	Shops    []*PartnerShopInfo
}
