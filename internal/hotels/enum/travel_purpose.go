package enum

import (
	"bytes"
	"errors"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
)

type TravelPurpose string

const (
	TravelPurposeNone     TravelPurpose = ""
	TravelPurposeLeisure  TravelPurpose = "leisure"
	TravelPurposeBusiness TravelPurpose = "business"
	// TravelPurposeTest     TravelPurpose = "test" // TEST.
)

var TravelPurposeName = map[TravelPurpose]string{
	TravelPurposeNone:     "",
	TravelPurposeLeisure:  "leisure",
	TravelPurposeBusiness: "business",
	// TravelPurposeTest:     "business",
}

var TravelPurposeValue = func() map[string]TravelPurpose {
	value := map[string]TravelPurpose{}

	for k, v := range TravelPurposeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e TravelPurpose) MarshalJSON() ([]byte, error) {
	v, ok := TravelPurposeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *TravelPurpose) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := TravelPurposeValue[string(data)]

	if !ok {
		log.Error(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
		return errors.ErrUnsupported
	}

	*e = v

	return nil
}

func (*TravelPurpose) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range TravelPurposeName {
		vals = append(vals, name)
	}

	return vals
}
