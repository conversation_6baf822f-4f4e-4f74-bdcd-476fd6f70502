package enum

import (
	"bytes"
	"errors"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
)

type HotelLocationType uint

const (
	HotelTypeNone HotelLocationType = iota
	HotelTypeDomestic
	HotelTypeInternational
)

var HotelTypeName = map[HotelLocationType]string{
	HotelTypeNone:          "",
	HotelTypeDomestic:      "domestic",
	HotelTypeInternational: "international",
}

var HotelTypeValue = func() map[string]HotelLocationType {
	value := map[string]HotelLocationType{}

	for k, v := range HotelTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e HotelLocationType) MarshalJSON() ([]byte, error) {
	v, ok := HotelTypeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *HotelLocationType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := HotelTypeValue[string(data)]

	if !ok {
		log.Error(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
		return errors.ErrUnsupported
	}

	*e = v

	return nil
}

func (*HotelLocationType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range HotelTypeName {
		vals = append(vals, name)
	}

	return vals
}
