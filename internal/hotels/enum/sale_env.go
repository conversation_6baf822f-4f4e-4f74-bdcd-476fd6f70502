package enum

import (
	"bytes"
	"errors"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
)

type SalesEnvironment string

const (
	SalesEnvironmentHotelOnly    SalesEnvironment = "hotel_only"
	SalesEnvironmentHotelPackage SalesEnvironment = "hotel_package"
	SalesEnvironmentLoyalty      SalesEnvironment = "loyalty"
)

var SalesEnvironmentName = map[SalesEnvironment]string{
	SalesEnvironmentHotelOnly:    "hotel_only",
	SalesEnvironmentHotelPackage: "hotel_package",
	SalesEnvironmentLoyalty:      "loyalty",
}

var SalesEnvironmentValue = func() map[string]SalesEnvironment {
	value := map[string]SalesEnvironment{}

	for k, v := range SalesEnvironmentName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e SalesEnvironment) MarshalJSON() ([]byte, error) {
	v, ok := SalesEnvironmentName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *SalesEnvironment) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := SalesEnvironmentValue[string(data)]

	if !ok {
		log.Error(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
		return errors.ErrUnsupported
	}

	*e = v

	return nil
}

func (*SalesEnvironment) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range SalesEnvironmentName {
		vals = append(vals, name)
	}

	return vals
}
