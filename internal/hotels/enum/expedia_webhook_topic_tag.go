package enum

type ExpediaWebhookTopicTag string

// Enum values for EventType.
const (
	CheckIn        ExpediaWebhookTopicTag = "check_in"
	EarlyCheckIn   ExpediaWebhookTopicTag = "early_check_in"
	LateCheckOut   ExpediaWebhookTopicTag = "late_check_out"
	Food           ExpediaWebhookTopicTag = "food"
	Beverage       ExpediaWebhookTopicTag = "beverage"
	Maintenance    ExpediaWebhookTopicTag = "maintenance"
	Closure        ExpediaWebhookTopicTag = "closure"
	Parking        ExpediaWebhookTopicTag = "parking"
	PetPolicy      ExpediaWebhookTopicTag = "pet_policy"
	RequestInfo    ExpediaWebhookTopicTag = "request_info"
	ArrivalTime    ExpediaWebhookTopicTag = "arrival_time"
	ReviewRequest  ExpediaWebhookTopicTag = "review_request"
	Transportation ExpediaWebhookTopicTag = "transportation"
)
