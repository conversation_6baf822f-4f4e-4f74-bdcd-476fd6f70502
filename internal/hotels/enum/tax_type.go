package enum

import (
	"bytes"
	"errors"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
)

type TaxType uint

const (
	TaxTypeNone TaxType = iota
	TaxTypeTax
	TaxTypeFee
	TaxTypeTaxAndFee
)

var TaxTypeName = map[TaxType]string{
	TaxTypeNone:      "",
	TaxTypeTax:       "tax",
	TaxTypeFee:       "fee",
	TaxTypeTaxAndFee: "tax-and-fee",
}

var TaxTypeValue = func() map[string]TaxType {
	value := map[string]TaxType{}

	for k, v := range TaxTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e TaxType) MarshalJSON() ([]byte, error) {
	v, ok := TaxTypeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *TaxType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := TaxTypeValue[string(data)]

	if !ok {
		log.Error(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
		return errors.ErrUnsupported
	}

	*e = v

	return nil
}

func (*TaxType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range TaxTypeName {
		vals = append(vals, name)
	}

	return vals
}
