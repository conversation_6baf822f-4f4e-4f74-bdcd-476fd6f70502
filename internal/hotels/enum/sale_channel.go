package enum

import (
	"bytes"
	"errors"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
)

type SalesChannel string

const (
	SalesChannelNone      SalesChannel = ""
	SalesChannelWebsite   SalesChannel = "website"
	SalesChannelAgentTool SalesChannel = "agent_tool"
	SalesChannelMobileApp SalesChannel = "mobile_app"
	SalesChannelMobileWeb SalesChannel = "mobile_web"
	SalesChannelMeta      SalesChannel = "meta"
	SalesChannelCache     SalesChannel = "cache"
)

var SalesChannelName = map[SalesChannel]string{
	SalesChannelNone:      "",
	SalesChannelWebsite:   "website",
	SalesChannelAgentTool: "agent_tool",
	SalesChannelMobileApp: "mobile_app",
	SalesChannelMobileWeb: "mobile_web",
	SalesChannelMeta:      "meta",
	SalesChannelCache:     "cache",
}

var SalesChannelValue = func() map[string]SalesChannel {
	value := map[string]SalesChannel{}

	for k, v := range SalesChannelName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e SalesChannel) MarshalJSON() ([]byte, error) {
	v, ok := SalesChannelName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *SalesChannel) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := SalesChannelValue[string(data)]

	if !ok {
		log.Error(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
		return errors.ErrUnsupported
	}

	*e = v

	return nil
}

func (*SalesChannel) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range SalesChannelName {
		vals = append(vals, name)
	}

	return vals
}
