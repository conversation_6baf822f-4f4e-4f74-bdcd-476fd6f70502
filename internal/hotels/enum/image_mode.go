package enum

import (
	"bytes"
	"errors"
	"fmt"

	"gitlab.deepgate.io/apps/common/log"
)

type ImageModeType string

const (
	ImageModeTypeNone      ImageModeType = ""
	ImageModeTypeFull      ImageModeType = "full"
	ImageModeTypeThumbnail ImageModeType = " thumbnail"
)

var ImageModeTypeName = map[ImageModeType]string{
	ImageModeTypeNone:      "",
	ImageModeTypeFull:      "full",
	ImageModeTypeThumbnail: "thumbnail",
}

var ImageModeTypeValue = func() map[string]ImageModeType {
	value := map[string]ImageModeType{}

	for k, v := range ImageModeTypeName {
		value[v] = k
		value[fmt.Sprintf("%v", k)] = k
	}

	return value
}()

func (e ImageModeType) MarshalJSON() ([]byte, error) {
	v, ok := ImageModeTypeName[e]

	if !ok {
		return []byte("\"\""), nil
	}

	buffer := bytes.NewBufferString(`"`)
	buffer.WriteString(v)
	buffer.WriteString(`"`)

	return buffer.Bytes(), nil
}

func (e *ImageModeType) UnmarshalJSON(data []byte) error {
	data = bytes.Trim(data, "\"")
	v, ok := ImageModeTypeValue[string(data)]

	if !ok {
		log.Error(fmt.Sprintf("enum '%s' is not register, must be one of: %v", data, e.EnumDescriptions()))
		return errors.ErrUnsupported
	}

	*e = v

	return nil
}

func (*ImageModeType) EnumDescriptions() []string {
	vals := []string{}

	for _, name := range ImageModeTypeName {
		vals = append(vals, name)
	}

	return vals
}
