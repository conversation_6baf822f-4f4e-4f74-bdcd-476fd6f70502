// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type checkAvailabilityServiceTrace struct {
	service.CheckAvailabilityService
}

func NewCheckAvailabilityService(arg1 *config.Schema,
	arg2 repositories.ProviderSearchHotelsRepository,
	arg3 *service.ProviderSearchHandlerImp,
	arg4 redis.SearchHotelsRepository,
	arg5 repositories.HotelRepository,
	arg6 repositories.RoomRepository,
	arg7 hub_provider.HubAdapter,
	arg8 service.HiddenFeeService,

) service.CheckAvailabilityService {
	return &checkAvailabilityServiceTrace{
		CheckAvailabilityService: service.NewCheckAvailabilityService(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
		),
	}
}

func (s *checkAvailabilityServiceTrace) Search(ctx context.Context, arg2 *domain.HubCheckAvailabilityReq, arg3 []enum.HotelProvider) (*domain.HubCheckAvailabilityRes, []enum.HotelProvider, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "checkAvailabilityService.Search")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.CheckAvailabilityService.Search(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CheckAvailabilityService.Search failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}
