// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/payment"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/wallet"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type cancelBookingServiceTrace struct {
	service.CancelBookingService
}

func NewCancelBookingService(arg1 *config.Schema,
	arg2 expedia_client.ExpediaAdapter,
	arg3 rate_hawk_client.RateHawkAdapter,
	arg4 redis.CancelRepository,
	arg5 repositories.OrderRepository,
	arg6 wallet.WalletClient,
	arg7 payment.PaymentClient,
	arg8 partner.PartnerClient,
	arg9 webhook.WebhookAdapter,
	arg10 dida.Adapter,
	arg11 travel_agent_client.TravelAgentAdapter,
	arg12 hub_provider.HubAdapter,
	arg13 agoda_client.AgodaAdapter,

) service.CancelBookingService {
	return &cancelBookingServiceTrace{
		CancelBookingService: service.NewCancelBookingService(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
			arg12,
			arg13,
		),
	}
}

func (s *cancelBookingServiceTrace) CancelBooking(ctx context.Context, arg2 *domain.HubCancelBookingReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "cancelBookingService.CancelBooking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.CancelBookingService.CancelBooking(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CancelBookingService.CancelBooking failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *cancelBookingServiceTrace) CheckCancelStatus(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 *domain.WebhookCfg) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "cancelBookingService.CheckCancelStatus")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.CancelBookingService.CheckCancelStatus(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CancelBookingService.CheckCancelStatus failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *cancelBookingServiceTrace) RefundBooking(ctx context.Context, arg2 *domain.WebhookCfg, arg3 string, arg4 string, arg5 string, arg6 string, arg7 string, arg8 float64) (*domain.TransactionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "cancelBookingService.RefundBooking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6, arg7, arg8)

	res1, res2 := s.CancelBookingService.RefundBooking(ctx, arg2, arg3, arg4, arg5, arg6, arg7, arg8)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CancelBookingService.RefundBooking failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
