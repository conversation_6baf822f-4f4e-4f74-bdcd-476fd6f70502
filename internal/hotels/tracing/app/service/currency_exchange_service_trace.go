// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type currencyExchangeServiceTrace struct {
	service.CurrencyExchangeService
}

func NewCurrencyExchangeService(arg1 repositories.CurrencyExchangeRepository,
	arg2 redis.CurrencyExchangeRepository,

) service.CurrencyExchangeService {
	return &currencyExchangeServiceTrace{
		CurrencyExchangeService: service.NewCurrencyExchangeService(
			arg1,
			arg2,
		),
	}
}

func (s *currencyExchangeServiceTrace) ConvertCurrency(ctx context.Context, arg2 *domain.HubCheckAvailabilityReq, arg3 enum.HotelProvider, arg4 []*domain.HubHotel, arg5 string) ([]*domain.HubHotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeService.ConvertCurrency")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2 := s.CurrencyExchangeService.ConvertCurrency(ctx, arg2, arg3, arg4, arg5)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeService.ConvertCurrency failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *currencyExchangeServiceTrace) ConvertFilterPrice(ctx context.Context, arg2 *domain.SearchHotelFilterResponse, arg3 string) (*domain.SearchHotelFilterResponse, *domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeService.ConvertFilterPrice")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.CurrencyExchangeService.ConvertFilterPrice(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeService.ConvertFilterPrice failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *currencyExchangeServiceTrace) ConvertHotelSummaryPrice(ctx context.Context, arg2 *domain.Price, arg3 string, arg4 float64) (*domain.Price, *domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeService.ConvertHotelSummaryPrice")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2, res3 := s.CurrencyExchangeService.ConvertHotelSummaryPrice(ctx, arg2, arg3, arg4)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeService.ConvertHotelSummaryPrice failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *currencyExchangeServiceTrace) ConvertHubRateDataCurrency(ctx context.Context, arg2 *domain.HubRateData, arg3 enum.HotelProvider, arg4 string, arg5 string) (*domain.HubRateData, *domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeService.ConvertHubRateDataCurrency")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3 := s.CurrencyExchangeService.ConvertHubRateDataCurrency(ctx, arg2, arg3, arg4, arg5)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeService.ConvertHubRateDataCurrency failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *currencyExchangeServiceTrace) GetRateMapping(ctx context.Context, arg2 string) (domain.CurrencyRateMapping, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeService.GetRateMapping")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CurrencyExchangeService.GetRateMapping(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeService.GetRateMapping failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *currencyExchangeServiceTrace) ValidateCurrency(ctx context.Context, arg2 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeService.ValidateCurrency")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.CurrencyExchangeService.ValidateCurrency(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeService.ValidateCurrency failed")
			span.RecordError(err)
		}
	}
	return res1

}
