// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"go.opentelemetry.io/otel/codes"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/dida"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/expedia_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mq"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/notification"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/rate_hawk_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/ta_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/tourmind_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type bookingServiceTrace struct {
	service.BookingService
}

func NewBookingService(arg1 *config.Schema,
	arg2 redis.BookingRepository,
	arg3 repositories.OrderRepository,
	arg4 expedia_client.ExpediaAdapter,
	arg5 tourmind_client.TourmindAdapter,
	arg6 rate_hawk_client.RateHawkAdapter,
	arg7 ta_client.TAAdapter,
	arg8 dida.Adapter,
	arg9 notification.NotificationServiceClient,
	arg10 partner.PartnerClient,
	arg11 mq.Adapter,
	arg12 service.CancelBookingService,
	arg13 travel_agent_client.TravelAgentAdapter,
	arg14 webhook.WebhookAdapter,
	arg15 hub_provider.HubAdapter,
	arg16 redis.CheckAvailabilityRepository,
	arg17 repositories.ProviderSearchHotelsRepository,
	arg18 repositories.SearchHotelCachesRepository,
	arg19 agoda_client.AgodaAdapter,

) service.BookingService {
	return &bookingServiceTrace{
		BookingService: service.NewBookingService(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
			arg8,
			arg9,
			arg10,
			arg11,
			arg12,
			arg13,
			arg14,
			arg15,
			arg16,
			arg17,
			arg18,
			arg19,
		),
	}
}

func (s *bookingServiceTrace) Book(ctx context.Context, arg2 commonEnum.HotelProvider, arg3 *domain.HubBookReq, arg4 *domain.HotelSession, arg5 *domain.HubHotelOrder) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, string, bool, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookingService.Book")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3, res4, res5, res6 := s.BookingService.Book(ctx, arg2, arg3, arg4, arg5)
	if res6 != nil {
		err, ok := reflect.ValueOf(res6).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookingService.Book failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4, res5, res6

}

func (s *bookingServiceTrace) FetchBookConfirmationID(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 *domain.HotelSession) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookingService.FetchBookConfirmationID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.BookingService.FetchBookConfirmationID(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookingService.FetchBookConfirmationID failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *bookingServiceTrace) GenOrderCode(ctx context.Context) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookingService.GenOrderCode")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1, res2 := s.BookingService.GenOrderCode(ctx)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookingService.GenOrderCode failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *bookingServiceTrace) MarkBookingFail(ctx context.Context, arg2 *domain.HubHotelOrder) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookingService.MarkBookingFail")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.BookingService.MarkBookingFail(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookingService.MarkBookingFail failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *bookingServiceTrace) Refund(ctx context.Context, arg2 *domain.HubHotelOrder) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookingService.Refund")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.BookingService.Refund(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookingService.Refund failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *bookingServiceTrace) ResolvePendingBook(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 *domain.HotelSession) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookingService.ResolvePendingBook")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.BookingService.ResolvePendingBook(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BookingService.ResolvePendingBook failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *bookingServiceTrace) SendUnknownPendingNotify(ctx context.Context, arg2 *domain.HubHotelOrder) {
	ctx, span := tracing.StartSpanFromContext(ctx, "bookingService.SendUnknownPendingNotify")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	s.BookingService.SendUnknownPendingNotify(ctx, arg2)

}
