// DO NOT EDIT: code generated from 'gen-tracing.go'
package service_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type sessionServiceTrace struct {
	service.SessionService
}

func NewSessionService(arg1 repositories.SessionRepository,

) service.SessionService {
	return &sessionServiceTrace{
		SessionService: service.NewSessionService(
			arg1,
		),
	}
}

func (s *sessionServiceTrace) Close(ctx context.Context, arg2 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionService.Close")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.SessionService.Close(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionService.Close failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *sessionServiceTrace) New(ctx context.Context, arg2 string, arg3 enum.HotelProvider, arg4 *domain.HotelSessionData) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionService.New")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.SessionService.New(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionService.New failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *sessionServiceTrace) Verify(ctx context.Context, arg2 string, arg3 string) (bool, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionService.Verify")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.SessionService.Verify(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionService.Verify failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *sessionServiceTrace) VerifyBulk(ctx context.Context, arg2 []string) (map[string]bool, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "sessionService.VerifyBulk")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.SessionService.VerifyBulk(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SessionService.VerifyBulk failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
