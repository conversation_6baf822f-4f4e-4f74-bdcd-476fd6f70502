// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type getDetailOrderByIDHandlerTrace struct {
	query.GetDetailOrderByIDHandler
}

func NewGetDetailOrderByIDHandler(arg1 *config.Schema,
	arg2 repositories.OrderRepository,
	arg3 repositories.HotelRepository,
	arg4 repositories.ClientBookingRepository,

) query.GetDetailOrderByIDHandler {
	return &getDetailOrderByIDHandlerTrace{
		GetDetailOrderByIDHandler: query.NewGetDetailOrderByIDHandler(
			arg1,
			arg2,
			arg3,
			arg4,
		),
	}
}

func (s *getDetailOrderByIDHandlerTrace) Handle(ctx context.Context, arg2 string) (*domain.HubHotelOrder, *domain.Hotel, *domain.ClientBooking, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "getDetailOrderByIDHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2, res3, res4 := s.GetDetailOrderByIDHandler.Handle(ctx, arg2)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.GetDetailOrderByIDHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}
