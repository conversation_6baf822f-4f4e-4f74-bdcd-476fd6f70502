// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type listOrderByFilterHandlerTrace struct {
	query.ListOrderByFilterHandler
}

func NewListOrderByFilterHandler(arg1 *config.Schema,
	arg2 repositories.OrderRepository,
	arg3 partner.PartnerClient,
	arg4 partnership.PartnershipClient,
	arg5 repositories.ClientBookingRepository,

) query.ListOrderByFilterHandler {
	return &listOrderByFilterHandlerTrace{
		ListOrderByFilterHandler: query.NewListOrderByFilterHandler(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		),
	}
}

func (s *listOrderByFilterHandlerTrace) Handle(ctx context.Context, arg2 *domain.ListOrderFilter) ([]*domain.HubHotelOrder, map[string]*domain.ClientBooking, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "listOrderByFilterHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2, res3 := s.ListOrderByFilterHandler.Handle(ctx, arg2)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ListOrderByFilterHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}
