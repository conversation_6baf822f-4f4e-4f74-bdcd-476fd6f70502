// DO NOT EDIT: code generated from 'gen-tracing.go'
package query_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/query"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type listCurrencyExchangeHandlerTrace struct {
	query.ListCurrencyExchangeHandler
}

func NewListCurrencyExchangeHandler(arg1 repositories.CurrencyExchangeRepository,

) query.ListCurrencyExchangeHandler {
	return &listCurrencyExchangeHandlerTrace{
		ListCurrencyExchangeHandler: query.NewListCurrencyExchangeHandler(
			arg1,
		),
	}
}

func (s *listCurrencyExchangeHandlerTrace) Handle(ctx context.Context, arg2 *domain.ListCurrencyExchangeReq) ([]*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "listCurrencyExchangeHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.ListCurrencyExchangeHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ListCurrencyExchangeHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
