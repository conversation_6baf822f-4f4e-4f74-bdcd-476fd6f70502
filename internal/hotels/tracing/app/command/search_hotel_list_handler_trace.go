// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type searchHotelListHandlerTrace struct {
	command.SearchHotelListHandler
}

func NewSearchHotelListHandler(arg1 *config.Schema,
	arg2 service.SearchHotelService,
	arg3 service.CurrencyExchangeService,

) command.SearchHotelListHandler {
	return &searchHotelListHandlerTrace{
		SearchHotelListHandler: command.NewSearchHotelListHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *searchHotelListHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelListHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.SearchHotelListHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelListHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
