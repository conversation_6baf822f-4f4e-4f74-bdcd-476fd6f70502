// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type searchHotelsHandlerTrace struct {
	command.SearchHotelsHandler
}

func NewSearchHotelsHandler(arg1 *config.Schema,
	arg2 service.SearchHotelService,
	arg3 service.CurrencyExchangeService,
	arg4 repositories.HotelPrioritiesRepository,

) command.SearchHotelsHandler {
	return &searchHotelsHandlerTrace{
		SearchHotelsHandler: command.NewSearchHotelsHandler(
			arg1,
			arg2,
			arg3,
			arg4,
		),
	}
}

func (s *searchHotelsHandlerTrace) Handle(ctx context.Context, arg2 *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "searchHotelsHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.SearchHotelsHandler.Handle(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.SearchHotelsHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
