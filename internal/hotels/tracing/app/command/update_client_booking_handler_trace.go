// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type updateClientBookingHandlerTrace struct {
	command.UpdateClientBookingHandler
}

func NewUpdateClientBookingHandler(arg1 repositories.ClientBookingRepository,

) command.UpdateClientBookingHandler {
	return &updateClientBookingHandlerTrace{
		UpdateClientBookingHandler: command.NewUpdateClientBookingHandler(
			arg1,
		),
	}
}

func (s *updateClientBookingHandlerTrace) Handle(ctx context.Context, arg2 []*domain.ClientBookingUpdateReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "updateClientBookingHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.UpdateClientBookingHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.UpdateClientBookingHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
