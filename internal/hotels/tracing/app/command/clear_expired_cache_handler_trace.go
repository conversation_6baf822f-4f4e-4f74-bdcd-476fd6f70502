// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"go.opentelemetry.io/otel/codes"
)

type clearExpiredCacheHandlerTrace struct {
	command.ClearExpiredCacheHandler
}

func NewClearExpiredCacheHandler(arg1 repositories.SearchHotelCachesRepository,

) command.ClearExpiredCacheHandler {
	return &clearExpiredCacheHandlerTrace{
		ClearExpiredCacheHandler: command.NewClearExpiredCacheHandler(
			arg1,
		),
	}
}

func (s *clearExpiredCacheHandlerTrace) Handle(ctx context.Context) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "clearExpiredCacheHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1 := s.ClearExpiredCacheHandler.Handle(ctx)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ClearExpiredCacheHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
