// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"go.opentelemetry.io/otel/codes"
)

type processBookingConfirmationIDHandlerTrace struct {
	command.ProcessBookingConfirmationIDHandler
}

func NewProcessBookingConfirmationIDHandler(arg1 repositories.OrderRepository,
	arg2 repositories.SessionRepository,
	arg3 service.BookingService,

) command.ProcessBookingConfirmationIDHandler {
	return &processBookingConfirmationIDHandlerTrace{
		ProcessBookingConfirmationIDHandler: command.NewProcessBookingConfirmationIDHandler(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *processBookingConfirmationIDHandlerTrace) Handle(ctx context.Context) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "processBookingConfirmationIDHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1 := s.ProcessBookingConfirmationIDHandler.Handle(ctx)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ProcessBookingConfirmationIDHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
