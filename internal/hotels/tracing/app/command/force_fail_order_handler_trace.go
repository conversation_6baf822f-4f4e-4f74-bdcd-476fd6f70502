// DO NOT EDIT: code generated from 'gen-tracing.go'
package command_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"go.opentelemetry.io/otel/codes"
)

type forceFailOrderHandlerTrace struct {
	command.ForceFailOrderHandler
}

func NewForceFailOrderHandler(arg1 repositories.OrderRepository,
	arg2 service.BookingService,

) command.ForceFailOrderHandler {
	return &forceFailOrderHandlerTrace{
		ForceFailOrderHandler: command.NewForceFailOrderHandler(
			arg1,
			arg2,
		),
	}
}

func (s *forceFailOrderHandlerTrace) Handle(ctx context.Context, arg2 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "forceFailOrderHandler.Handle")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ForceFailOrderHandler.Handle(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ForceFailOrderHandler.Handle failed")
			span.RecordError(err)
		}
	}
	return res1

}
