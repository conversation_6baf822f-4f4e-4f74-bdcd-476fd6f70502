// DO NOT EDIT: code generated from 'gen-tracing.go'
package partner_tracing

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
	"google.golang.org/grpc"
)

type partnerClientTrace struct {
	partner.PartnerClient
}

func NewPartnerClient(arg1 *config.Schema,
	arg2 *grpc.ClientConn,

) partner.PartnerClient {
	return &partnerClientTrace{
		PartnerClient: partner.NewPartnerClient(
			arg1,
			arg2,
		),
	}
}

func (s *partnerClientTrace) AuthByOffice(ctx context.Context, arg2 *domain.LoginReq) (*domain.PartnerUser, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "partnerClient.AuthByOffice")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.PartnerClient.AuthByOffice(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PartnerClient.AuthByOffice failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *partnerClientTrace) GetOfficeInfo(ctx context.Context, arg2 string, arg3 string) (*domain.PartnerShopInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "partnerClient.GetOfficeInfo")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.PartnerClient.GetOfficeInfo(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PartnerClient.GetOfficeInfo failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *partnerClientTrace) GetShopsByManagerID(ctx context.Context, arg2 string, arg3 string) ([]*domain.PartnerShopInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "partnerClient.GetShopsByManagerID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.PartnerClient.GetShopsByManagerID(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PartnerClient.GetShopsByManagerID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *partnerClientTrace) GetShopsBySaleCodes(ctx context.Context, arg2 string, arg3 []string) ([]*domain.ShopsBySaleCode, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "partnerClient.GetShopsBySaleCodes")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.PartnerClient.GetShopsBySaleCodes(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.PartnerClient.GetShopsBySaleCodes failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
