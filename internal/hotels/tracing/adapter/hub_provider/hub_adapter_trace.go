// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type hubAdapterTrace struct {
	hub_provider.HubAdapter
}

func NewHubAdapter(arg1 hub_provider.Client,
	arg2 *config.Schema,

) hub_provider.HubAdapter {
	return &hubAdapterTrace{
		HubAdapter: hub_provider.NewHubAdapter(
			arg1,
			arg2,
		),
	}
}

func (s *hubAdapterTrace) Book(ctx context.Context, arg2 *domain.HubBookReq) (*domain.HubBookRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hubAdapter.Book")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HubAdapter.Book(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HubAdapter.Book failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hubAdapterTrace) Cancel(ctx context.Context, arg2 string, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hubAdapter.Cancel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.HubAdapter.Cancel(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HubAdapter.Cancel failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hubAdapterTrace) CheckAvailability(ctx context.Context, arg2 *domain.HubCheckAvailabilityReq) (*domain.HubCheckAvailabilityRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hubAdapter.CheckAvailability")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HubAdapter.CheckAvailability(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HubAdapter.CheckAvailability failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hubAdapterTrace) PriceCheck(ctx context.Context, arg2 *domain.HubPriceCheckReq) (*domain.HubPriceCheckRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hubAdapter.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HubAdapter.PriceCheck(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HubAdapter.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hubAdapterTrace) Retrieve(ctx context.Context, arg2 *domain.HubRetrieveBookingReq) (*domain.HubRetrieveBookingRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hubAdapter.Retrieve")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HubAdapter.Retrieve(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HubAdapter.Retrieve failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hubAdapterTrace) SearchHotel(ctx context.Context, arg2 *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hubAdapter.SearchHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HubAdapter.SearchHotel(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HubAdapter.SearchHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hubAdapterTrace) SearchListHotel(ctx context.Context, arg2 *domain.HubSearchHotelRequest) (*domain.HubSearchHotelResponse, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hubAdapter.SearchListHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HubAdapter.SearchListHotel(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HubAdapter.SearchListHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
