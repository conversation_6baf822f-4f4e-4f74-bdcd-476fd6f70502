// DO NOT EDIT: code generated from 'gen-tracing.go'
package client

import (
	"context"
	"reflect"

	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type agodaAdapterTrace struct {
	agoda_client.AgodaAdapter
}

func NewAgodaAdapter(arg1 *config.Schema,
	arg2 commonRedis.IRedis,
	arg3 repositories.RequestRepository,

) agoda_client.AgodaAdapter {
	return &agodaAdapterTrace{
		AgodaAdapter: agoda_client.NewAgodaAdapter(
			arg1,
			arg2,
			arg3,
		),
	}
}

func (s *agodaAdapterTrace) Booking(ctx context.Context, arg2 *domain.HubBookReq, arg3 *domain.HubHotelOrder, arg4 *domain.AgodaSessionInfo, arg5 string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "agodaAdapter.Booking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3, res4, res5 := s.AgodaAdapter.Booking(ctx, arg2, arg3, arg4, arg5)
	if res5 != nil {
		err, ok := reflect.ValueOf(res5).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AgodaAdapter.Booking failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4, res5

}

func (s *agodaAdapterTrace) CancelBooking(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string) (float64, float64, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "agodaAdapter.CancelBooking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3 := s.AgodaAdapter.CancelBooking(ctx, arg2, arg3)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AgodaAdapter.CancelBooking failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *agodaAdapterTrace) CheckAvailability(ctx context.Context, arg2 *domain.CheckAvaiAdapterReq) ([]*domain.HubRoom, string, int64, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "agodaAdapter.CheckAvailability")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2, res3, res4 := s.AgodaAdapter.CheckAvailability(ctx, arg2)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AgodaAdapter.CheckAvailability failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}

func (s *agodaAdapterTrace) PriceCheck(ctx context.Context, arg2 *domain.HotelSearchResult, arg3 string, arg4 *domain.HubRateData, arg5 *domain.HubHotel, arg6 *domain.HubPriceCheckReq) (*domain.HubRateData, *domain.AgodaSessionInfo, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "agodaAdapter.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1, res2, res3 := s.AgodaAdapter.PriceCheck(ctx, arg2, arg3, arg4, arg5, arg6)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AgodaAdapter.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *agodaAdapterTrace) RetrieveBooking(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, bool, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "agodaAdapter.RetrieveBooking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2, res3, res4, res5 := s.AgodaAdapter.RetrieveBooking(ctx, arg2, arg3)
	if res5 != nil {
		err, ok := reflect.ValueOf(res5).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AgodaAdapter.RetrieveBooking failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4, res5

}

func (s *agodaAdapterTrace) RetrieveForRefund(ctx context.Context, arg2 *domain.HubHotelOrder, arg3 float64, arg4 float64, arg5 string) (bool, *domain.RefundData, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "agodaAdapter.RetrieveForRefund")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3 := s.AgodaAdapter.RetrieveForRefund(ctx, arg2, arg3, arg4, arg5)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AgodaAdapter.RetrieveForRefund failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *agodaAdapterTrace) SearchHotel(ctx context.Context, arg2 *domain.SearchAdapterReq, arg3 string) ([]*domain.HotelSummary, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "agodaAdapter.SearchHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.AgodaAdapter.SearchHotel(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AgodaAdapter.SearchHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
