// DO NOT EDIT: code generated from 'gen-tracing.go'
package travel_agent_client

import (
	"context"
	"reflect"

	commonRedis "gitlab.deepgate.io/apps/common/adapter/redis"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/travel_agent_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

type travelAgentAdapterTrace struct {
	travel_agent_client.TravelAgentAdapter
}

func NewTravelAgentAdapter(arg1 *config.Schema,
	arg2 commonRedis.IRedis,
	arg3 repositories.RequestRepository,
	arg4 repositories.AmenityRepository,
	arg5 repositories.BedOptionRepository,

) travel_agent_client.TravelAgentAdapter {
	return &travelAgentAdapterTrace{
		TravelAgentAdapter: travel_agent_client.NewTravelAgentAdapter(
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		),
	}
}

func (s *travelAgentAdapterTrace) Booking(ctx context.Context, arg2 *domain.HubBookReq, arg3 commonEnum.HotelProvider, arg4 string, arg5 string) (string, []*domain.HubRetrieveConfirmationID, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "travelAgentAdapter.Booking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3, res4 := s.TravelAgentAdapter.Booking(ctx, arg2, arg3, arg4, arg5)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TravelAgentAdapter.Booking failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}

func (s *travelAgentAdapterTrace) CancelBooking(ctx context.Context, arg2 *domain.HubCancelBookingReq, arg3 commonEnum.HotelProvider, arg4 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "travelAgentAdapter.CancelBooking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.TravelAgentAdapter.CancelBooking(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TravelAgentAdapter.CancelBooking failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *travelAgentAdapterTrace) PriceCheck(ctx context.Context, arg2 *domain.HubPriceCheckReq, arg3 commonEnum.HotelProvider, arg4 string) (*domain.HubPriceCheckRes, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "travelAgentAdapter.PriceCheck")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.TravelAgentAdapter.PriceCheck(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TravelAgentAdapter.PriceCheck failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *travelAgentAdapterTrace) RetrieveBooking(ctx context.Context, arg2 *domain.HubRetrieveBookingReq, arg3 commonEnum.HotelProvider, arg4 string) ([]string, string, enum.BookingStatus, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "travelAgentAdapter.RetrieveBooking")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2, res3, res4 := s.TravelAgentAdapter.RetrieveBooking(ctx, arg2, arg3, arg4)
	if res4 != nil {
		err, ok := reflect.ValueOf(res4).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TravelAgentAdapter.RetrieveBooking failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3, res4

}

func (s *travelAgentAdapterTrace) RetrieveBookingForRefund(ctx context.Context, arg2 *domain.HubRetrieveBookingReq, arg3 commonEnum.HotelProvider, arg4 bool, arg5 string) (bool, bool, *domain.RefundData) {
	ctx, span := tracing.StartSpanFromContext(ctx, "travelAgentAdapter.RetrieveBookingForRefund")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2, res3 := s.TravelAgentAdapter.RetrieveBookingForRefund(ctx, arg2, arg3, arg4, arg5)
	if res3 != nil {
		err, ok := reflect.ValueOf(res3).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TravelAgentAdapter.RetrieveBookingForRefund failed")
			span.RecordError(err)
		}
	}
	return res1, res2, res3

}

func (s *travelAgentAdapterTrace) SearchHotel(ctx context.Context, arg2 *domain.SearchHotelRequestData, arg3 commonEnum.HotelProvider, arg4 string) ([]*domain.HubHotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "travelAgentAdapter.SearchHotel")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.TravelAgentAdapter.SearchHotel(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.TravelAgentAdapter.SearchHotel failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
