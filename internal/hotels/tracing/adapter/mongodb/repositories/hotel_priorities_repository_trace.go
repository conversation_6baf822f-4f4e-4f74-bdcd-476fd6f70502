// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type hotelPrioritiesRepositoryTrace struct {
	repositories.HotelPrioritiesRepository
}

func NewHotelPrioritiesRepository(arg1 commonMongoDb.DB,

) repositories.HotelPrioritiesRepository {
	return &hotelPrioritiesRepositoryTrace{
		HotelPrioritiesRepository: repositories.NewHotelPrioritiesRepository(
			arg1,
		),
	}
}

func (s *hotelPrioritiesRepositoryTrace) FindAll(ctx context.Context) ([]*domain.HotelPriority, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelPrioritiesRepository.FindAll")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1, res2 := s.HotelPrioritiesRepository.FindAll(ctx)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelPrioritiesRepository.FindAll failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
