// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type inActiveHotelsRepositoryTrace struct {
	repositories.InActiveHotelsRepository
}

func NewInActiveHotelsRepository(arg1 commonMongoDb.DB,

) repositories.InActiveHotelsRepository {
	return &inActiveHotelsRepositoryTrace{
		InActiveHotelsRepository: repositories.NewInActiveHotelsRepository(
			arg1,
		),
	}
}

func (s *inActiveHotelsRepositoryTrace) FindAll(ctx context.Context) ([]*domain.InActiveHotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "inActiveHotelsRepository.FindAll")
	defer span.End()
	tracing.AddAttributes(span, ctx)

	res1, res2 := s.InActiveHotelsRepository.FindAll(ctx)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.InActiveHotelsRepository.FindAll failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *inActiveHotelsRepositoryTrace) FindByHotelIDs(ctx context.Context, arg2 []string) ([]*domain.InActiveHotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "inActiveHotelsRepository.FindByHotelIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.InActiveHotelsRepository.FindByHotelIDs(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.InActiveHotelsRepository.FindByHotelIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
