// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"go.opentelemetry.io/otel/codes"
)

type roomRepositoryTrace struct {
	repositories.RoomRepository
}

func NewRoomRepository(arg1 commonMongoDb.DB,
	arg2 *config.Schema,

) repositories.RoomRepository {
	return &roomRepositoryTrace{
		RoomRepository: repositories.NewRoomRepository(
			arg1,
			arg2,
		),
	}
}

func (s *roomRepositoryTrace) Create(ctx context.Context, arg2 *domain.Room) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.Create")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.RoomRepository.Create(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.Create failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *roomRepositoryTrace) CreateMany(ctx context.Context, arg2 []*domain.Room, arg3 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.CreateMany")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1 := s.RoomRepository.CreateMany(ctx, arg2, arg3)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.CreateMany failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *roomRepositoryTrace) FindByHotelIDsWithDefault(ctx context.Context, arg2 string, arg3 string) (map[string][]*domain.Room, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.FindByHotelIDsWithDefault")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.RoomRepository.FindByHotelIDsWithDefault(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.FindByHotelIDsWithDefault failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *roomRepositoryTrace) FindByRoomIDAndHotelRef(ctx context.Context, arg2 string, arg3 string, arg4 string) (*domain.Room, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.FindByRoomIDAndHotelRef")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.RoomRepository.FindByRoomIDAndHotelRef(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.FindByRoomIDAndHotelRef failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *roomRepositoryTrace) FindByRoomIDs(ctx context.Context, arg2 []string, arg3 string) ([]*domain.Room, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.FindByRoomIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.RoomRepository.FindByRoomIDs(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.FindByRoomIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *roomRepositoryTrace) FindByRoomIDsV2(ctx context.Context, arg2 enum.HotelProvider, arg3 string, arg4 string) ([]*domain.Room, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.FindByRoomIDsV2")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.RoomRepository.FindByRoomIDsV2(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.FindByRoomIDsV2 failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *roomRepositoryTrace) FindByRoomObjectIDs(ctx context.Context, arg2 []string) ([]*domain.Room, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.FindByRoomObjectIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.RoomRepository.FindByRoomObjectIDs(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.FindByRoomObjectIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *roomRepositoryTrace) MigrateHotelRoomName(ctx context.Context, arg2 int, arg3 int, arg4 int, arg5 string, arg6 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.MigrateHotelRoomName")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6)

	res1 := s.RoomRepository.MigrateHotelRoomName(ctx, arg2, arg3, arg4, arg5, arg6)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.MigrateHotelRoomName failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *roomRepositoryTrace) Update(ctx context.Context, arg2 *domain.Room) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.Update")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.RoomRepository.Update(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.Update failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *roomRepositoryTrace) WithTransaction(ctx context.Context, arg2 func(context.Context) (interface{}, error)) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "roomRepository.WithTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.RoomRepository.WithTransaction(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RoomRepository.WithTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
