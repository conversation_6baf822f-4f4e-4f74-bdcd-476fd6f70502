// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type requestPlaceRepositoryTrace struct {
	repositories.RequestPlaceRepository
}

func NewRequestPlaceRepository(arg1 commonMongoDb.DB,

) repositories.RequestPlaceRepository {
	return &requestPlaceRepositoryTrace{
		RequestPlaceRepository: repositories.NewRequestPlaceRepository(
			arg1,
		),
	}
}

func (s *requestPlaceRepositoryTrace) LogPlace(ctx context.Context, arg2 *domain.Place) (string, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "requestPlaceRepository.LogPlace")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.RequestPlaceRepository.LogPlace(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.RequestPlaceRepository.LogPlace failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
