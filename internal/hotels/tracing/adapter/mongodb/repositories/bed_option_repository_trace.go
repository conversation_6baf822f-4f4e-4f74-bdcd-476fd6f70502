// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type bedOptionRepositoryTrace struct {
	repositories.BedOptionRepository
}

func NewBedOptionRepository(arg1 commonMongoDb.DB,

) repositories.BedOptionRepository {
	return &bedOptionRepositoryTrace{
		BedOptionRepository: repositories.NewBedOptionRepository(
			arg1,
		),
	}
}

func (s *bedOptionRepositoryTrace) FindByBedOptionIDs(ctx context.Context, arg2 []string, arg3 string) ([]*domain.BedOptionContent, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "bedOptionRepository.FindByBedOptionIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.BedOptionRepository.FindByBedOptionIDs(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BedOptionRepository.FindByBedOptionIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *bedOptionRepositoryTrace) FindByLanguage(ctx context.Context, arg2 string) ([]*domain.BedOptionContent, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "bedOptionRepository.FindByLanguage")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.BedOptionRepository.FindByLanguage(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.BedOptionRepository.FindByLanguage failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
