// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type displayImageRepositoryTrace struct {
	repositories.DisplayImageRepository
}

func NewDisplayImageRepository(arg1 commonMongoDb.DB,

) repositories.DisplayImageRepository {
	return &displayImageRepositoryTrace{
		DisplayImageRepository: repositories.NewDisplayImageRepository(
			arg1,
		),
	}
}

func (s *displayImageRepositoryTrace) CreateBulk(ctx context.Context, arg2 []*domain.DisplayImageContent) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "displayImageRepository.CreateBulk")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.DisplayImageRepository.CreateBulk(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.DisplayImageRepository.CreateBulk failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *displayImageRepositoryTrace) FindByDisplayImageHotelIDs(ctx context.Context, arg2 []string) ([]*domain.DisplayImageContent, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "displayImageRepository.FindByDisplayImageHotelIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.DisplayImageRepository.FindByDisplayImageHotelIDs(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.DisplayImageRepository.FindByDisplayImageHotelIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
