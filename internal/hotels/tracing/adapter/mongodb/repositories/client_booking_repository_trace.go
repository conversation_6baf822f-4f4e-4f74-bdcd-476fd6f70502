// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type clientBookingRepositoryTrace struct {
	repositories.ClientBookingRepository
}

func NewClientBookingRepository(arg1 commonMongoDb.DB,

) repositories.ClientBookingRepository {
	return &clientBookingRepositoryTrace{
		ClientBookingRepository: repositories.NewClientBookingRepository(
			arg1,
		),
	}
}

func (s *clientBookingRepositoryTrace) FindByHubOrderCodes(ctx context.Context, arg2 []string, arg3 string) ([]*domain.ClientBooking, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "clientBookingRepository.FindByHubOrderCodes")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.ClientBookingRepository.FindByHubOrderCodes(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ClientBookingRepository.FindByHubOrderCodes failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *clientBookingRepositoryTrace) InsertOne(ctx context.Context, arg2 *domain.ClientBooking) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "clientBookingRepository.InsertOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ClientBookingRepository.InsertOne(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ClientBookingRepository.InsertOne failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *clientBookingRepositoryTrace) UpdateByHubOrderCodes(ctx context.Context, arg2 *domain.ClientBookingUpdateReq) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "clientBookingRepository.UpdateByHubOrderCodes")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ClientBookingRepository.UpdateByHubOrderCodes(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ClientBookingRepository.UpdateByHubOrderCodes failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *clientBookingRepositoryTrace) WithTransaction(ctx context.Context, arg2 func(context.Context) (interface{}, error)) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "clientBookingRepository.WithTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.ClientBookingRepository.WithTransaction(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.ClientBookingRepository.WithTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
