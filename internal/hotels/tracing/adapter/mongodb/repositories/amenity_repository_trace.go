// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type amenityRepositoryTrace struct {
	repositories.AmenityRepository
}

func NewAmenityRepository(arg1 commonMongoDb.DB,

) repositories.AmenityRepository {
	return &amenityRepositoryTrace{
		AmenityRepository: repositories.NewAmenityRepository(
			arg1,
		),
	}
}

func (s *amenityRepositoryTrace) FindByAmenityIDs(ctx context.Context, arg2 []string, arg3 string) ([]*domain.AmenityContent, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "amenityRepository.FindByAmenityIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.AmenityRepository.FindByAmenityIDs(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AmenityRepository.FindByAmenityIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *amenityRepositoryTrace) FindByLanguage(ctx context.Context, arg2 string) ([]*domain.AmenityContent, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "amenityRepository.FindByLanguage")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.AmenityRepository.FindByLanguage(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.AmenityRepository.FindByLanguage failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}
