// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/tracing"
	"go.opentelemetry.io/otel/codes"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type hotelRepositoryTrace struct {
	repositories.HotelRepository
}

func NewHotelRepository(arg1 commonMongoDb.DB,
	arg2 *config.Schema,

) repositories.HotelRepository {
	return &hotelRepositoryTrace{
		HotelRepository: repositories.NewHotelRepository(
			arg1,
			arg2,
		),
	}
}

func (s *hotelRepositoryTrace) Create(ctx context.Context, arg2 *domain.Hotel) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.Create")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HotelRepository.Create(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.Create failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hotelRepositoryTrace) CreateMany(ctx context.Context, arg2 []*domain.Hotel) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.CreateMany")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HotelRepository.CreateMany(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.CreateMany failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hotelRepositoryTrace) FindAllByCountryCode(ctx context.Context, arg2 string) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindAllByCountryCode")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HotelRepository.FindAllByCountryCode(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindAllByCountryCode failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByHotelByExpediaID(ctx context.Context, arg2 string, arg3 string) (*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByHotelByExpediaID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.HotelRepository.FindByHotelByExpediaID(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByHotelByExpediaID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByHotelID(ctx context.Context, arg2 string, arg3 string) (*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByHotelID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.HotelRepository.FindByHotelID(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByHotelID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByHotelIDV2(ctx context.Context, arg2 string) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByHotelIDV2")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HotelRepository.FindByHotelIDV2(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByHotelIDV2 failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByHotelIDs(ctx context.Context, arg2 []string, arg3 string) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByHotelIDs")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.HotelRepository.FindByHotelIDs(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByHotelIDs failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByHotelIDsWithDefault(ctx context.Context, arg2 []string, arg3 string, arg4, arg5 bool) (map[string][]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByHotelIDsWithDefault")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2 := s.HotelRepository.FindByHotelIDsWithDefault(ctx, arg2, arg3, arg4, arg5)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByHotelIDsWithDefault failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByProviderAndContentVersionHotelID(ctx context.Context, arg2 enum.HotelProvider, arg3 string, arg4 string, arg5 string) (*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByProviderAndContentVersionHotelID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2 := s.HotelRepository.FindByProviderAndContentVersionHotelID(ctx, arg2, arg3, arg4, arg5)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByProviderAndContentVersionHotelID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByProviderHotelID(ctx context.Context, arg2 enum.HotelProvider, arg3 []string, arg4 string) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByProviderHotelID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.HotelRepository.FindByProviderHotelID(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByProviderHotelID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByProviderHotelIDForCache(ctx context.Context, arg2 enum.HotelProvider, arg3 []string, arg4 string, arg5 int) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByProviderHotelIDForCache")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5)

	res1, res2 := s.HotelRepository.FindByProviderHotelIDForCache(ctx, arg2, arg3, arg4, arg5)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByProviderHotelIDForCache failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindByProviderIds(ctx context.Context, arg2 enum.HotelProvider, arg3 string) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindByProviderIds")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.HotelRepository.FindByProviderIds(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindByProviderIds failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindHotelIDForCache(ctx context.Context, arg2 []string, arg3 string, arg4 int) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindHotelIDForCache")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1, res2 := s.HotelRepository.FindHotelIDForCache(ctx, arg2, arg3, arg4)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindHotelIDForCache failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindHotelsByRadius(ctx context.Context, arg2 float64, arg3 float64, arg4 float64, arg5 string, arg6 string, arg7 int) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindHotelsByRadius")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4, arg5, arg6, arg7)

	res1, res2 := s.HotelRepository.FindHotelsByRadius(ctx, arg2, arg3, arg4, arg5, arg6, arg7)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindHotelsByRadius failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) FindWithFilter(ctx context.Context, arg2 *domain.FindHotelsWithFilterReq) ([]*domain.Hotel, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.FindWithFilter")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.HotelRepository.FindWithFilter(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.FindWithFilter failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *hotelRepositoryTrace) Update(ctx context.Context, arg2 *domain.Hotel) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.Update")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HotelRepository.Update(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.Update failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hotelRepositoryTrace) UpdateHotelRating(ctx context.Context, arg2 string, arg3 enum.HotelProvider, arg4 *domain.GuestRating) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.UpdateHotelRating")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.HotelRepository.UpdateHotelRating(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.UpdateHotelRating failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hotelRepositoryTrace) UpdateProviderIds(ctx context.Context, arg2 string, arg3 enum.HotelProvider, arg4 string) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.UpdateProviderIds")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3, arg4)

	res1 := s.HotelRepository.UpdateProviderIds(ctx, arg2, arg3, arg4)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.UpdateProviderIds failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *hotelRepositoryTrace) WithTransaction(ctx context.Context, arg2 func(context.Context) (interface{}, error)) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "hotelRepository.WithTransaction")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.HotelRepository.WithTransaction(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.HotelRepository.WithTransaction failed")
			span.RecordError(err)
		}
	}
	return res1

}
