// DO NOT EDIT: code generated from 'gen-tracing.go'
package repositories_tracing

import (
	"context"
	"reflect"

	commonMongoDb "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/tracing"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"go.opentelemetry.io/otel/codes"
)

type currencyExchangeRepositoryTrace struct {
	repositories.CurrencyExchangeRepository
}

func NewCurrencyExchangeRepository(arg1 commonMongoDb.DB,

) repositories.CurrencyExchangeRepository {
	return &currencyExchangeRepositoryTrace{
		CurrencyExchangeRepository: repositories.NewCurrencyExchangeRepository(
			arg1,
		),
	}
}

func (s *currencyExchangeRepositoryTrace) Create(ctx context.Context, arg2 *domain.CurrencyExchange) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.Create")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.CurrencyExchangeRepository.Create(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.Create failed")
			span.RecordError(err)
		}
	}
	return res1

}

func (s *currencyExchangeRepositoryTrace) Find(ctx context.Context, arg2 string) ([]*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.Find")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CurrencyExchangeRepository.Find(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.Find failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *currencyExchangeRepositoryTrace) FindByID(ctx context.Context, arg2 string) (*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.FindByID")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CurrencyExchangeRepository.FindByID(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.FindByID failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *currencyExchangeRepositoryTrace) FindCurrencyFromTo(ctx context.Context, arg2 string, arg3 string) (*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.FindCurrencyFromTo")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2, arg3)

	res1, res2 := s.CurrencyExchangeRepository.FindCurrencyFromTo(ctx, arg2, arg3)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.FindCurrencyFromTo failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *currencyExchangeRepositoryTrace) FindOne(ctx context.Context, arg2 *domain.CurrencyExchange) (*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.FindOne")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CurrencyExchangeRepository.FindOne(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.FindOne failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *currencyExchangeRepositoryTrace) FindWithFilter(ctx context.Context, arg2 *domain.ListCurrencyExchangeReq) ([]*domain.CurrencyExchange, error) {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.FindWithFilter")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1, res2 := s.CurrencyExchangeRepository.FindWithFilter(ctx, arg2)
	if res2 != nil {
		err, ok := reflect.ValueOf(res2).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.FindWithFilter failed")
			span.RecordError(err)
		}
	}
	return res1, res2

}

func (s *currencyExchangeRepositoryTrace) Update(ctx context.Context, arg2 *domain.CurrencyExchange) error {
	ctx, span := tracing.StartSpanFromContext(ctx, "currencyExchangeRepository.Update")
	defer span.End()
	tracing.AddAttributes(span, ctx, arg2)

	res1 := s.CurrencyExchangeRepository.Update(ctx, arg2)
	if res1 != nil {
		err, ok := reflect.ValueOf(res1).Interface().(error)
		if ok {
			span.SetStatus(codes.Error, "s.CurrencyExchangeRepository.Update failed")
			span.RecordError(err)
		}
	}
	return res1

}
