package utils

import (
	"crypto/md5"
	"encoding/hex"
	"strings"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func GenHotelKey(name, city, countryCode string) string {
	key := strings.ToLower(name) + strings.ToLower(city) + strings.ToLower(countryCode)

	hash := md5.Sum([]byte(key))

	return hex.EncodeToString(hash[:])
}

func getHotelContentWithDefault(hotels, defaultLanguageHotels []*domain.Hotel) []*domain.Hotel {
	hotelMap := make(map[string]*domain.Hotel)

	for _, hotel := range hotels {
		hotelMap[hotel.HotelID] = hotel
	}

	for _, hotel := range defaultLanguageHotels {
		if _, exists := hotelMap[hotel.HotelID]; !exists {
			hotelMap[hotel.HotelID] = hotel
		}
	}

	var response []*domain.Hotel
	for _, hotel := range hotelMap {
		response = append(response, hotel)
	}

	return response
}

func GetHotelContent(hotelContents map[string][]*domain.Hotel, language string, hotelIDs []string) []*domain.Hotel {
	defaultLanguage := hotelContents[constants.DefaultLanguage]

	enLanguage := hotelContents[constants.LanguageEnglish]

	requestLanguage := hotelContents[language]

	hotels := getHotelContentWithDefault(requestLanguage, defaultLanguage)
	enHotels := getHotelContentWithDefault(enLanguage, defaultLanguage)

	hotelEnMap := map[string]*domain.Hotel{}
	hotelMap := map[string]*domain.Hotel{}

	for _, enHotel := range enHotels {
		hotelEnMap[enHotel.HotelID] = enHotel
	}

	for _, hotel := range hotels {
		hotelMap[hotel.HotelID] = hotel
	}

	result := make([]*domain.Hotel, 0, len(hotelIDs))
	for _, hotelID := range hotelIDs {
		hotel, ok := hotelMap[hotelID]
		if !ok {
			continue
		}

		enContent, ok := hotelEnMap[hotelID]
		if !ok {
			enContent = hotel
		}

		hotel.NameEn = enContent.Name

		roomEnMap := map[string]*domain.Room{}
		for _, enRoom := range enContent.Rooms {
			roomEnMap[enRoom.RoomID] = enRoom
		}

		for _, room := range hotel.Rooms {
			enRoom, ok := roomEnMap[room.RoomID]
			if !ok {
				enRoom = room
			}

			room.NameEn = enRoom.Name
		}

		result = append(result, hotel)
	}

	return result
}

func getRoomContentWithDefault(rooms, defaultLanguageRooms []*domain.Room) []*domain.Room {
	roomMap := make(map[string]*domain.Room)

	for _, room := range rooms {
		roomMap[room.RoomID] = room
	}

	for _, room := range defaultLanguageRooms {
		if _, exists := roomMap[room.RoomID]; !exists {
			roomMap[room.RoomID] = room
		}
	}

	var response []*domain.Room
	for _, room := range roomMap {
		response = append(response, room)
	}

	return response
}

func GetRoomContent(roomContents map[string][]*domain.Room, language string) []*domain.Room {
	defaultLanguage := roomContents[constants.DefaultLanguage]

	enLanguage := roomContents[constants.LanguageEnglish]

	requestLanguage := roomContents[language]

	rooms := getRoomContentWithDefault(requestLanguage, defaultLanguage)
	enRooms := getRoomContentWithDefault(enLanguage, defaultLanguage)

	roomEnMap := map[string]*domain.Room{}
	for _, enRoom := range enRooms {
		roomEnMap[enRoom.RoomID] = enRoom
	}

	for _, room := range rooms {
		enContent, ok := roomEnMap[room.RoomID]
		if !ok {
			enContent = room
		}

		room.NameEn = enContent.Name
	}

	return rooms
}
