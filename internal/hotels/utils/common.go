package utils

// import (
// 	"fmt"
// 	"strconv"

// 	"gitlab.deepgate.io/apps/common/errors"
// 	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
// )

// func GetHotelProvider(hotelID string) (commonEnum.HotelProvider, error) {
// 	if hotelID == "" {
// 		return commonEnum.HotelProviderNone, errors.ErrInvalidInput
// 	}

// 	iProvider, err := strconv.ParseInt(string(hotelID[0]), 10, 0)
// 	if err != nil {
// 		return commonEnum.HotelProviderNone, err
// 	}

// 	return commonEnum.HotelProvider(iProvider), nil
// }

// func GetHotelGroupID(first commonEnum.HotelProvider, sec int) string {
// 	return fmt.Sprintf("%d%d", first, sec)
// }
