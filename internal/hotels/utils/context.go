package utils

import (
	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func GetWebhookCfgFromEcho(c echo.Context) (*domain.WebhookCfg, bool) {
	webhookCfg, ok := c.Get(constants.ContextWebhookKey).(*domain.WebhookCfg)
	if ok {
		return webhookCfg, true
	}

	return &domain.WebhookCfg{}, false
}
