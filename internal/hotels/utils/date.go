package utils

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func CalcTimeRemainToEndOfDay(date time.Time, loc *time.Location) time.Duration {
	now := time.Now().In(loc)

	endOfDay := time.Date(date.Year(), date.Month(), date.Day(), 23, 59, 59, 0, loc)
	timeRemaining := endOfDay.Sub(now)

	return timeRemaining
}

func GetStartOfDay(in int64) int64 {
	t := time.UnixMilli(in).UTC()
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location()).UnixMilli()
}

// Arg timeRange format : 0700:2100.
func CheckTimeInRange(timeRange string, timeWithTz time.Time) (bool, error) {
	if len(timeRange) != 9 {
		return false, fmt.Errorf("invalid time range format")
	}

	location := timeWithTz.Location()
	now := timeWithTz

	// Parse the input time range string
	times := strings.Split(timeRange, ":")
	if len(times) != 2 {
		return false, fmt.Errorf("invalid time range format")
	}

	// Convert the start and end times to hours and minutes
	startHour, err := strconv.Atoi(times[0][:2])
	if err != nil {
		return false, fmt.Errorf("invalid start hour: %w", err)
	}

	startMinute, err := strconv.Atoi(times[0][2:])
	if err != nil {
		return false, fmt.Errorf("invalid start minute: %w", err)
	}

	endHour, err := strconv.Atoi(times[1][:2])
	if err != nil {
		return false, fmt.Errorf("invalid end hour: %w", err)
	}

	endMinute, err := strconv.Atoi(times[1][2:])
	if err != nil {
		return false, fmt.Errorf("invalid end minute: %w", err)
	}

	// Get the current time in tz

	// Define the start and end times for the range
	startTime := time.Date(now.Year(), now.Month(), now.Day(), startHour, startMinute, 0, 0, location)
	endTime := time.Date(now.Year(), now.Month(), now.Day(), endHour, endMinute, 0, 0, location)

	// Check if the current time is within the range
	if now.After(startTime) && now.Before(endTime) {
		return true, nil
	}

	return false, nil
}

func CheckLastDayOfMonth() error {
	location, err := time.LoadLocation(constants.HCMTimezone)
	if err != nil {
		return fmt.Errorf("failed to load location: %w", err)
	}

	now := time.Now().In(location)

	// Check if the current time is after 17:00
	currentYear, currentMonth, currentDay := now.Date()
	lastDay := time.Date(currentYear, currentMonth+1, 0, 0, 0, 0, 0, location).Day()

	// If today is the last day of the month and the time is after 17:00, return an error
	if currentDay == lastDay && now.Hour() >= 17 {
		return domain.ErrInvoicingTemporaryNotAvailable
	}

	// OK
	return nil
}
