package converts

import (
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func FromHotelBookingStatuses(ins []skyhub.HotelBookingStatus) []enum.BookingStatus {
	return lo.Map(ins, func(item skyhub.HotelBookingStatus, _ int) enum.BookingStatus {
		return enum.BookingStatusIntMap[int32(item)]
	})
}

func FromDomainListOrderReq(info *web_partnership.ListOrdersFilter, userRoles []string) *domain.ListOrderFilter {
	if info == nil {
		return nil
	}

	return &domain.ListOrderFilter{
		BookingStatuses: FromHotelBookingStatuses(info.BookingStatuses),
		From:            info.From,
		To:              info.To,
		OfficeID:        info.OfficeId,
		OrderCode:       info.OrderCode,
		NotInStatuses:   FromHotelBookingStatuses(info.NotInBookingStatuses),
		Roles:           userRoles,
		UnknownPending:  info.UnknownPending,
	}
}

func ToProtoOrderBasicInfos(infos []*domain.HubHotelOrder, clientBookingData map[string]*domain.ClientBooking) []*skyhub.OrderBasicInfo {
	if len(infos) == 0 {
		return nil
	}

	res := make([]*skyhub.OrderBasicInfo, 0)
	for _, item := range infos {
		matchClientBooking := clientBookingData[item.OrderCode]
		res = append(res, ToHubOrderBasicInfo(item, matchClientBooking))
	}

	return res
}

func ToHubOrderBasicInfo(info *domain.HubHotelOrder, clientBooking *domain.ClientBooking) *skyhub.OrderBasicInfo {
	if info == nil {
		return nil
	}

	res := &skyhub.OrderBasicInfo{
		OrderCode:  info.OrderCode,
		Id:         info.ID,
		Status:     skyhub.HotelBookingStatus(enum.BookingStatusNumber[info.BookingStatus]),
		AgentCode:  info.AgentCode,
		Refunded:   info.Refunded,
		CreatedAt:  info.CreatedAt,
		ClientPaid: false,
		ClientOrderCode: info.ClientOrderCode,
	}

	// RefundAmount dùng AmountFX chuẩn
	res.RefundAmount = mapRefundAmountFX(info.RefundData, info.ExchangedRefundData, info.RequestCurrencyRateApply)

	// Amount dùng AmountFX chuẩn
	res.Amount = mapAmountFX(info.ExchangedRateDataCf, info.RequestCurrencyRateDataCf, info.RequestCurrencyRateApply, "PayNow")
	profit := info.ExchangedRateDataCf.PayNow - info.ExchangedRateDataCfRaw.PayNow

	res.Profit = mapProfitAmountFX(profit, info.ExchangedRateDataCf.Currency, info.RequestCurrencyRateApply)

	if info.Hotel != nil && len(info.Hotel.ListRooms) > 0 {
		res.ConfirmationId = info.Hotel.ListRooms[0].ConfirmationID
		res.HotelConfirmationCode = info.Hotel.ListRooms[0].HotelConfirmationID
	}

	if clientBooking != nil {
		res.ClientPaid = clientBooking.Paid
	}

	return res
}

func FromDomainOrderDetail(hubOrder *domain.HubHotelOrder, hotel *domain.Hotel, clientBooking *domain.ClientBooking) *skyhub.OrderDetail {
	if hubOrder == nil {
		return nil
	}

	res := &skyhub.OrderDetail{
		Id:        hubOrder.ID,
		OrderCode: hubOrder.OrderCode,
		CreatedAt: hubOrder.CreatedAt,
		Status:    skyhub.HotelBookingStatus(enum.BookingStatusNumber[hubOrder.BookingStatus]),
		RateCf:    FromDomainHubRateData(hubOrder.ExchangedRateDataCf, hubOrder.RequestCurrencyRateDataCf, hubOrder.RequestCurrencyRateApply),
	}

	if hubOrder.HotelSearchRequest != nil {
		res.TotalNights = int32(hubOrder.HotelSearchRequest.Stay.DayCount)
		res.CheckInDate = hubOrder.HotelSearchRequest.Stay.CheckIn
		res.CheckOutDate = hubOrder.HotelSearchRequest.Stay.CheckOut
	}

	if hotel != nil {
		if hotel.CheckIn != nil {
			res.CheckInTime = hotel.CheckIn.BeginTime
			res.CheckInInstructions = append(res.CheckInInstructions, helpers.SplitHTMLContentByTag(hotel.CheckIn.Instructions, helpers.TagLi)...)
			res.CheckInInstructions = append(res.CheckInInstructions, helpers.SplitIntoSentences(hotel.CheckIn.SpecialInstructions)...)
		}

		if hotel.Checkout != nil {
			res.CheckOutTime = hotel.Checkout.Time
		}
	}

	if hubOrder.ExchangedRateDataCf != nil || hubOrder.RateData != nil {
		rateData := hubOrder.ExchangedRateDataCf
		if rateData == nil {
			rateData = hubOrder.RateData
		}

		// checkInStr := helpers.ConvertToVietnameseDateFormat(fmt.Sprintf("%sT%s", res.CheckInDate, res.CheckInTime))
		// res.CancelPolicies = domain.MappingCancelPolicyToStrings(rateData, checkInStr)
		res.Amenities = rateData.GetAmenityNames()
	}

	if hubOrder.Hotel != nil {
		room := make([]*skyhub.BookingRoom, 0)

		for _, item := range hubOrder.Hotel.ListRooms {
			roomProto := &skyhub.BookingRoom{
				RoomId:         item.RoomID,
				Name:           item.Name,
				NameEn:         item.NameEn,
				ConfirmationId: item.ConfirmationID,
				Holder:         FromDomainHolder(item),
			}
			if item.BedOption != nil {
				roomProto.BedOption = &skyhub.BedOption{
					OptionId: item.BedOption.OptionID,
					Name:     item.BedOption.Name,
				}
			}

			room = append(room, roomProto)
		}

		res.Rooms = room

		res.HotelInfo = &skyhub.HotelBasicInfo{
			Id:      hubOrder.Hotel.HotelID,
			Name:    hubOrder.Hotel.Name,
			NameEn:  hubOrder.Hotel.NameEn,
			Address: FromDomainAddress(hubOrder.Hotel.Address),
			Rating:  hubOrder.Hotel.Rating,
		}

		if len(hubOrder.Hotel.ListRooms) > 0 {
			res.HotelConfirmationCode = hubOrder.Hotel.ListRooms[0].HotelConfirmationID
		}
	}

	if clientBooking != nil {
		res.ClientPaid = clientBooking.Paid
	}

	return res
}

func FromDomainAddress(address *domain.Address) *skyhub.Address {
	if address == nil {
		return nil
	}

	return &skyhub.Address{
		City:        address.City,
		CountryCode: address.CountryCode,
		Line_1:      address.Line1,
	}
}

func FromDomainHolder(room *domain.HubOrderRoomItem) *skyhub.RoomHolder {
	if room == nil {
		return nil
	}

	return &skyhub.RoomHolder{
		GivenName: room.GivenName,
		Surname:   room.Surname,
		Email:     room.Email,
	}
}

// Map AmountFX for PayNow, TotalPayAtHotel, TotalRateBasic, TotalTaxAmount
// order: HubHotelOrder, baseField: lấy từ ExchangedRateDataCf, exchangeField: lấy từ RequestCurrencyRateDataCf, rate: lấy từ RequestCurrencyRateApply
func mapAmountFXToProto(order *domain.HubHotelOrder, baseField, exchangeField string) *skyhub.AmountFX {
	if order == nil || order.ExchangedRateDataCf == nil {
		return nil
	}

	var baseCurrency string
	var amount float64
	var exchangeCurrency string
	var exchangedAmount float64
	var rateExchange float64

	switch baseField {
	case "PayNow":
		baseCurrency = order.ExchangedRateDataCf.Currency
		amount = order.ExchangedRateDataCf.PayNow
	case "TotalPayAtHotel":
		baseCurrency = order.ExchangedRateDataCf.Currency
		amount = order.ExchangedRateDataCf.TotalPayAtHotel
	case "TotalRateBasic":
		baseCurrency = order.ExchangedRateDataCf.Currency
		amount = order.ExchangedRateDataCf.TotalRateBasic
	case "TotalTaxAmount":
		baseCurrency = order.ExchangedRateDataCf.Currency
		amount = order.ExchangedRateDataCf.TotalTaxAmount
	}

	if order.RequestCurrencyRateDataCf != nil {
		switch exchangeField {
		case "PayNow":
			exchangeCurrency = order.RequestCurrencyRateDataCf.Currency
			exchangedAmount = order.RequestCurrencyRateDataCf.PayNow
		case "TotalPayAtHotel":
			exchangeCurrency = order.RequestCurrencyRateDataCf.Currency
			exchangedAmount = order.RequestCurrencyRateDataCf.TotalPayAtHotel
		case "TotalRateBasic":
			exchangeCurrency = order.RequestCurrencyRateDataCf.Currency
			exchangedAmount = order.RequestCurrencyRateDataCf.TotalRateBasic
		case "TotalTaxAmount":
			exchangeCurrency = order.RequestCurrencyRateDataCf.Currency
			exchangedAmount = order.RequestCurrencyRateDataCf.TotalTaxAmount
		}
	}

	if order.RequestCurrencyRateApply != nil {
		rateExchange = order.RequestCurrencyRateApply.Rate
	}

	return &skyhub.AmountFX{
		BaseCurrency:     baseCurrency,
		Amount:           amount,
		ExchangeCurrency: exchangeCurrency,
		ExchangedAmount:  exchangedAmount,
		RateExchange:     rateExchange,
	}
}

// Helper to map AmountFX for a specific field.
func mapAmountFX(rateData, exchangeRateData *domain.HubRateData, rateDataEx *domain.CurrencyExchange, field string) *skyhub.AmountFX {
	if rateData == nil {
		return nil
	}
	var amount, exchangedAmount float64
	var baseCurrency, exchangeCurrency string
	var rateExchange float64

	switch field {
	case "PayNow":
		amount = rateData.PayNow
		baseCurrency = rateData.Currency

		if exchangeRateData != nil {
			exchangedAmount = exchangeRateData.PayNow
			exchangeCurrency = exchangeRateData.Currency
		}
	case "TotalPayAtHotel":
		amount = rateData.TotalPayAtHotel
		baseCurrency = rateData.Currency

		if exchangeRateData != nil {
			exchangedAmount = exchangeRateData.TotalPayAtHotel
			exchangeCurrency = exchangeRateData.Currency
		}
	case "TotalRateBasic":
		amount = rateData.TotalRateBasic
		baseCurrency = rateData.Currency

		if exchangeRateData != nil {
			exchangedAmount = exchangeRateData.TotalRateBasic
			exchangeCurrency = exchangeRateData.Currency
		}
	case "TotalTaxAmount":
		amount = rateData.TotalTaxAmount
		baseCurrency = rateData.Currency

		if exchangeRateData != nil {
			exchangedAmount = exchangeRateData.TotalTaxAmount
			exchangeCurrency = exchangeRateData.Currency
		}
	}

	if rateDataEx != nil {
		rateExchange = rateDataEx.Rate
	}

	return &skyhub.AmountFX{
		Amount:           amount,
		BaseCurrency:     baseCurrency,
		ExchangeCurrency: exchangeCurrency,
		ExchangedAmount:  exchangedAmount,
		RateExchange:     rateExchange,
	}
}

func mapProfitAmountFX(profit float64, baseCurrency string, rateDataEx *domain.CurrencyExchange) *skyhub.AmountFX {
	exchangeCurrency := baseCurrency
	exchangedAmount := profit
	rateExchange := 1.0

	if rateDataEx != nil {
		rateExchange = rateDataEx.Rate
		exchangedAmount = profit * rateDataEx.Rate
		exchangeCurrency = rateDataEx.To
	}

	return &skyhub.AmountFX{
		Amount:           profit,
		BaseCurrency:     baseCurrency,
		ExchangeCurrency: exchangeCurrency,
		ExchangedAmount:  exchangedAmount,
		RateExchange:     rateExchange,
	}
}

func FromDomainHubRateData(rateData *domain.HubRateData, exchangeRateData *domain.HubRateData, rateDataEx *domain.CurrencyExchange) *skyhub.HubRateData {
	if rateData == nil {
		return nil
	}

	return &skyhub.HubRateData{
		RateId:          rateData.RateID,
		PayNow:          mapAmountFX(rateData, exchangeRateData, rateDataEx, "PayNow"),
		TotalPayAtHotel: rateData.TotalPayAtHotel,
		TotalRateBasic:  mapAmountFX(rateData, exchangeRateData, rateDataEx, "TotalRateBasic"),
		TotalTaxAmount:  mapAmountFX(rateData, exchangeRateData, rateDataEx, "TotalTaxAmount"),
	}
}

// Map RefundAmountFX từ RefundData, ExchangedRefundData, Rate.
func mapRefundAmountFX(refundData, exchangedRefundData *domain.RefundData, rateDataEx *domain.CurrencyExchange) *skyhub.AmountFX {
	if refundData == nil {
		return nil
	}
	var amount, exchangedAmount float64
	var baseCurrency, exchangeCurrency string
	var rateExchange float64

	amount = refundData.RefundAmount
	baseCurrency = refundData.Currency

	if exchangedRefundData != nil {
		exchangedAmount = exchangedRefundData.RefundAmount
		exchangeCurrency = exchangedRefundData.Currency
	}

	if rateDataEx != nil {
		rateExchange = rateDataEx.Rate
	}

	return &skyhub.AmountFX{
		Amount:           amount,
		BaseCurrency:     baseCurrency,
		ExchangeCurrency: exchangeCurrency,
		ExchangedAmount:  exchangedAmount,
		RateExchange:     rateExchange,
	}
}

func FromDomainPayAtHotel(domain *domain.PayAtHotel) *skyhub.PayAtHotel {
	if domain == nil {
		return nil
	}

	return &skyhub.PayAtHotel{
		Amount:      domain.Amount,
		Description: domain.Description,
		Currency:    domain.Currency,
	}
}

func FromDomainPayAtHotels(domains []*domain.PayAtHotel) []*skyhub.PayAtHotel {
	if len(domains) == 0 {
		return nil
	}

	res := make([]*skyhub.PayAtHotel, 0)
	for _, item := range domains {
		res = append(res, FromDomainPayAtHotel(item))
	}

	return res
}
