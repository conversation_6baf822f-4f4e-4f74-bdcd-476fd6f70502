package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func FromDomainListHiddenServiceFeeReq(info *web_partnership.ListHiddenFeeFilter) *domain.ListHiddenServiceFeeReq {
	if info == nil {
		return nil
	}

	return &domain.ListHiddenServiceFeeReq{
		OfficeID:     info.OfficeId,
		LocationType: enum.HotelLocationType(info.Location),
		CountryCode:  info.CountryCode,
		Rating:       info.Rating,
		HotelType:    info.AccommodationType,
		HotelName:    info.Keywords,
		Type:         enum.HiddenFeeType(info.Type),
	}
}

func ToHiddenServiceFeeProto(info *domain.HiddenServiceFee) *skyhub.HiddenFee {
	if info == nil {
		return nil
	}

	res := &skyhub.HiddenFee{
		Id:        info.ID,
		HotelName: info.HotelName,
		HotelId:   info.HotelID,
		Percent:   info.Percent * 100,
		Amount:    info.Amount,
		Provider:  commonEnum.HotelProviderName[info.Provider],
		Type:      skyhub.HiddenFeeType(info.Type),
		CreatedAt: info.CreatedAt,
		UpdatedAt: info.UpdatedAt,
		OfficeId:  info.OfficeID,
	}

	if info.Config != nil {
		res.CountryCode = info.Config.CountryCode
		res.Rating = info.Config.Rating
		res.Location = skyhub.HotelLocationType(info.Config.LocationType)
		res.AccommodationType = info.Config.HotelType
	}

	return res
}

func ToProtoListHiddenServiceFee(info []*domain.HiddenServiceFee) []*skyhub.HiddenFee {
	if info == nil {
		return nil
	}

	res := make([]*skyhub.HiddenFee, 0)
	for _, item := range info {
		res = append(res, ToHiddenServiceFeeProto(item))
	}

	return res
}
