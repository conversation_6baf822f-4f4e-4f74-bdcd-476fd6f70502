package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func FromDomainCreateCurrencyExchangeReq(info *skyhub.HotelCurrencyExchange) *domain.CurrencyExchange {
	if info == nil {
		return nil
	}

	return &domain.CurrencyExchange{
		From: info.From,
		To:   info.To,
		Rate: info.Rate,
	}
}

func FromDomainUpdateCurrencyExchangeReq(info *skyhub.HotelCurrencyExchange, id string) *domain.CurrencyExchange {
	if info == nil {
		return nil
	}

	return &domain.CurrencyExchange{
		Base: domain.Base{
			ID: id,
		},
		From: info.From,
		To:   info.To,
		Rate: info.Rate,
	}
}

func FromDomainGetCurrencyExchangeReq(info *web_partnership.GetCurrencyExchangeReq) *domain.GetCurrencyExchangeDetailReq {
	if info == nil {
		return nil
	}

	return &domain.GetCurrencyExchangeDetailReq{
		ID: info.Id,
	}
}

func FromDomainListCurrencyExchangeReq(info *web_partnership.GetCurrencyExchangeReqFilter) *domain.ListCurrencyExchangeReq {
	if info == nil {
		return nil
	}

	return &domain.ListCurrencyExchangeReq{
		From: info.From,
		To:   info.To,
	}
}

func ToProtoCurrencyExchanges(info []*domain.CurrencyExchange) []*skyhub.HotelCurrencyExchange {
	res := make([]*skyhub.HotelCurrencyExchange, 0, len(info))
	for _, item := range info {
		res = append(res, ToProtoCurrencyExchange(item))
	}
	return res
}

func ToProtoCurrencyExchange(info *domain.CurrencyExchange) *skyhub.HotelCurrencyExchange {
	if info == nil {
		return nil
	}

	return &skyhub.HotelCurrencyExchange{
		Id:   info.ID,
		From: info.From,
		To:   info.To,
		Rate: info.Rate,
	}
}
