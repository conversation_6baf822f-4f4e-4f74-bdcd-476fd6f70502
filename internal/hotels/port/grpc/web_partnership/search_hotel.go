package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/converts"
)

func (s *WebPartnershipServer) SearchHotel(ctx context.Context, req *web_partnership.SearchHotelReq) (*web_partnership.SearchHotelRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrPermissionDenied
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSkyAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	if err := req.Validate(); err != nil {
		log.Error("SearchHotel invalid req", log.Any("err", err))
		return nil, errors.ErrInvalidInput
	}

	pagi := commonDomain.ToPagingDomain(req.Pagination)
	res, err := s.app.Commands.SearchDestinationHandler.Handle(ctx, &domain.SearchDestinationReq{
		Query:      req.Keywords,
		Pagination: pagi,
		PlaceType:  enum.PlaceTypeHotel,
	})
	if err != nil {
		return &web_partnership.SearchHotelRes{
			IsSuccess: false,
			ErrorCode: converts.HandleErr(err),
		}, nil
	}

	if res == nil {
		return &web_partnership.SearchHotelRes{
			IsSuccess: true,
		}, nil
	}

	return &web_partnership.SearchHotelRes{
		IsSuccess:  true,
		Pagination: commonDomain.ToPagingProto(res.Pagination),
		Items:      converts.ToHotelInfoProto(res.Destinations),
	}, nil
}
