package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func (s *WebPartnershipServer) FetchOrderStatus(ctx context.Context, req *web_partnership.FetchOrderStatusReq) (*base.BasicRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains(user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleOfficeManager]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	hotelOrder, _, _, err := s.app.Queries.GetDetailOrderByIDHandler.Handle(ctx, req.Id)
	if err != nil {
		return &base.BasicRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	if hotelOrder == nil {
		if err != nil {
			return &base.BasicRes{
				IsSuccess: false,
				ErrorCode: errors.ErrNotFound.Error(),
			}, nil
		}
	}

	err = s.app.Commands.ProcessPendingBookingHandler.Handle(ctx, &command.ProcessPendingBookingRequest{
		Bookings: []*domain.HubHotelOrder{hotelOrder},
	})
	if err != nil {
		return &base.BasicRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &base.BasicRes{
		IsSuccess: true,
	}, nil
}
