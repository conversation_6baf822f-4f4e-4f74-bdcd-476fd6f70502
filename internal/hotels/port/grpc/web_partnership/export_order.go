package web_partnership

import (
	"context"

	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"

	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) ExportOrders(ctx context.Context, req *web_partnership.ExportOrdersReq) (*web_partnership.ExportOrdersRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	allowRoles := []string{
		commonEnum.UserRoleName[commonEnum.UserRoleOfficeManager],
		commonEnum.UserRoleName[commonEnum.UserRoleSale],
	}

	if isContain := len(lo.Intersect(user.Roles, allowRoles)) > 0; !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainListOrderReq(req.Filter, user.Roles)
	reqFilter.PartnershipID = user.PartnershipId
	reqFilter.ManagerID = user.Id
	reqFilter.UserID = user.Id

	res, clientBookingData, err := s.app.Queries.ListOrderByFilterHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.ExportOrdersRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.ExportOrdersRes{
		IsSuccess: true,
		Items:     partnershipConverts.ToProtoOrderBasicInfos(res, clientBookingData),
	}, nil
}
