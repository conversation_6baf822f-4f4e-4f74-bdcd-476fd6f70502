package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func (s *WebPartnershipServer) CreateHiddenFee(ctx context.Context, req *web_partnership.CreateHiddenFeeReq) (*web_partnership.CreateHiddenFeeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSkyAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	cmdReq := &domain.UpsertConfigHiddenFeeRequest{
		UserID:    user.Id,
		OfficeID:  req.OfficeId,
		HotelID:   req.HotelId,
		HotelName: req.HotelName,
		Amount:    req.Amount,
		Percent:   req.Percent / 100,
		Provider:  commonEnum.HotelProviderValue[req.Provider],
		Type:      enum.HiddenFeeType(req.Type),
		Location:  enum.HotelLocationType(req.Location),
		Countries: req.CountryCode,
		Ratings:   req.Rating,
		HotelType: req.AccommodationType,
	}

	configID, err := s.app.Commands.CreateConfigHiddenFeeHandler.Handle(ctx, cmdReq)
	if err != nil {
		return &web_partnership.CreateHiddenFeeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.CreateHiddenFeeRes{
		IsSuccess: true,
		Id:        configID,
	}, nil
}
