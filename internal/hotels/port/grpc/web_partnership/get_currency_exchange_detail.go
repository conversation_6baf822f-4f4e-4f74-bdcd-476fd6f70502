package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) GetCurrencyExchange(ctx context.Context, req *web_partnership.GetCurrencyExchangeReq) (*web_partnership.GetCurrencyExchangeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	//Only super admin can list currency exchange
	if isContain := commonHelpers.Contains(user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSuperAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainGetCurrencyExchangeReq(req)

	res, err := s.app.Queries.GetCurrencyExchangeHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.GetCurrencyExchangeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.GetCurrencyExchangeRes{
		IsSuccess:        true,
		CurrencyExchange: partnershipConverts.ToProtoCurrencyExchange(res),
	}, nil
}
