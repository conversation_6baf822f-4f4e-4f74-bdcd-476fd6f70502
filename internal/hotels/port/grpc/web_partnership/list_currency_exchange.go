package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) ListCurrencyExchange(ctx context.Context, req *web_partnership.ListCurrencyExchangeReq) (*web_partnership.ListCurrencyExchangeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	//Only super admin can list currency exchange
	if isContain := commonHelpers.Contains(user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSuperAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	reqFilter := partnershipConverts.FromDomainListCurrencyExchangeReq(req.Filter)
	reqFilter.Pagination = commonDomain.ToPagingDomain(req.Pagination)

	res, err := s.app.Queries.ListCurrencyExchangeHandler.Handle(ctx, reqFilter)
	if err != nil {
		return &web_partnership.ListCurrencyExchangeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.ListCurrencyExchangeRes{
		IsSuccess:  true,
		Pagination: commonDomain.ToPagingProto(reqFilter.Pagination),
		Items:      partnershipConverts.ToProtoCurrencyExchanges(res),
	}, nil
}
