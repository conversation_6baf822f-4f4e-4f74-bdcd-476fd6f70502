package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
)

func (s *WebPartnershipServer) UpdateClientBooking(ctx context.Context, req *web_partnership.UpdateClientBookingReq) (*base.BasicRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains(user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleOfficeManager]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	cmdReq := []*domain.ClientBookingUpdateReq{
		{
			HubOrderCodes: req.PaidIds,
			PartnershipID: user.PartnershipId,
			Paid:          true,
		},
		{
			HubOrderCodes: req.UnpaidIds,
			PartnershipID: user.PartnershipId,
			Paid:          false,
		},
	}

	err := s.app.Commands.UpdateClientBookingHandler.Handle(ctx, cmdReq)
	if err != nil {
		return &base.BasicRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &base.BasicRes{
		IsSuccess: true,
	}, nil
}
