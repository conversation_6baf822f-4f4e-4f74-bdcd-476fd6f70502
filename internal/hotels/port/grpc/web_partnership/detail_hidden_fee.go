package web_partnership

import (
	"context"

	"gitlab.deepgate.io/apps/api/gen/go/skyhub/web_partnership"
	"gitlab.deepgate.io/apps/common/auth"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/apps/common/errors"
	commonHelpers "gitlab.deepgate.io/apps/common/helpers"
	partnershipConverts "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc/web_partnership/converts"
)

func (s *WebPartnershipServer) DetailHiddenFee(ctx context.Context, req *web_partnership.DetailHiddenFeeReq) (*web_partnership.DetailHiddenFeeRes, error) {
	user, ok := auth.GetContextUser(ctx)
	if !ok {
		return nil, errors.ErrInvalidToken
	}

	if isContain := commonHelpers.Contains[string](user.Roles, commonEnum.UserRoleName[commonEnum.UserRoleSkyAdmin]); !isContain {
		return nil, errors.ErrPermissionDenied
	}

	res, err := s.app.Queries.DetailConfigHiddenFeeHandler.Handle(ctx, req.Id)
	if err != nil {
		return &web_partnership.DetailHiddenFeeRes{
			IsSuccess: false,
			ErrorCode: err.Error(),
		}, nil
	}

	return &web_partnership.DetailHiddenFeeRes{
		IsSuccess: true,
		HiddenFee: partnershipConverts.ToHiddenServiceFeeProto(res),
	}, nil
}
