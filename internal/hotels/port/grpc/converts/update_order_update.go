package converts

import (
	"gitlab.deepgate.io/apps/api/gen/go/skyhub"
	"gitlab.deepgate.io/apps/api/gen/go/skyhub/backend"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func ConvertBookingStatus(info skyhub.HotelBookingStatus) enum.BookingStatus {
	switch info {
	case skyhub.HotelBookingStatus_HotelBookingStatusSuccess:
		return enum.BookingStatusSuccess
	case skyhub.HotelBookingStatus_HotelBookingStatusCancelled:
		return enum.BookingStatusCancel
	}

	return enum.BookingStatusNone
}

func FromDomainOccupancyUpdateInfo(info *backend.OccupancyUpdateInfo) *domain.OccupancyUpdateInfo {
	if info == nil {
		return nil
	}

	return &domain.OccupancyUpdateInfo{
		OccupancyIndex: int(info.OccupancyIndex),
		ConfirmationID: info.ConfirmationId,
	}
}

func FromDomainOccupancyUpdateInfos(info []*backend.OccupancyUpdateInfo) []*domain.OccupancyUpdateInfo {
	result := make([]*domain.OccupancyUpdateInfo, 0, len(info))

	for _, item := range info {
		result = append(result, FromDomainOccupancyUpdateInfo(item))
	}

	return result
}
