package admin

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"sync"
	"time"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

// AggregateRegionContent nhận và xử lý dữ liệu JSON từ client.
func (s *AdminServer) AggregateRegionContent(stream pb.HotelAdminService_AggregateRegionContentServer) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	concurrency := 30 // Số lượng goroutine tối đa

	// Semaphore giới hạn số lượng goroutine đồng thời
	semaphore := make(chan struct{}, concurrency)

	prefixPool := make(chan int, concurrency)
	for i := 0; i < concurrency; i++ {
		prefixPool <- i // Đưa các giá trị prefix vào pool
	}

	for { // i là prefix đại diện cho mỗi goroutine
		req, err := stream.Recv()
		if errors.Is(err, io.EOF) {
			// Kết thúc stream
			break
		}

		if err != nil {
			return fmt.Errorf("error receiving data: %w", err)
		}

		var cmdReq *command.AggregateRegionContentReq

		switch req.Provider {
		case int32(commonEnum.HotelProviderRateHawk):
			// Giải mã JSON vào struct domain.RateHawkHotelContent
			var property *domain.RateHawkRegionContent
			if err := json.Unmarshal(req.Content, &property); err != nil {
				log.Printf("Error unmarshalling JSON for Index %v: %v", req.IndexContent, err)
				// Gửi thông báo thất bại và kết thúc stream
				return stream.SendAndClose(&base.BasicRes{})
			}

			fmt.Println("receive region", property.Name)
			cmdReq = &command.AggregateRegionContentReq{
				ContentVersion:  "1",
				Provider:        commonEnum.HotelProviderRateHawk,
				RateHawkContent: property,
				Language:        req.Language,
			}
		}

		if cmdReq == nil {
			log.Printf("error receiving non support provider: %d", req.Provider)
			return stream.SendAndClose(&base.BasicRes{})
		}

		wg.Add(1)
		semaphore <- struct{}{} // Chặn khi số lượng goroutine đạt tối đa
		prefix := <-prefixPool
		// Truyền giá trị prefix (i) vào hàm Handle
		go func(cmdReq *command.AggregateRegionContentReq, indexContent int64, content []byte, prefix int) {
			cmdReq.PrefixNumber = prefix

			defer wg.Done() // Đảm bảo wg.Done() được gọi khi goroutine hoàn thành
			defer func() {
				fmt.Println("prefix done")
				<-semaphore
				prefixPool <- prefix
			}() // Giải phóng semaphore khi xong việc

			ctx, ctxCancel := context.WithTimeout(context.Background(), 100*time.Minute)
			defer ctxCancel()

			// Truyền prefix vào Handle
			err := s.app.Commands.AggregateRegionContentHandler.Handle(ctx, cmdReq)
			if err != nil {
				log.Printf("Error processing property for Index %v with prefix %d", indexContent, prefix)
				// Gửi phản hồi lỗi cho stream
				if errSend := stream.SendMsg(&pb.AggregateHotelContentRes{
					IndexContent: indexContent,
					IsSuccess:    false,
					Content:      content,
				}); errSend != nil {
					errChan <- fmt.Errorf("error sending error response: %w", errSend)
					return
				}
				errChan <- err
			}
		}(cmdReq, req.IndexContent, req.Content, prefix) // Truyền i (prefix) vào đây
	}

	// Chờ tất cả goroutine hoàn thành
	wg.Wait()
	close(errChan)

	// Kiểm tra và trả về lỗi nếu có
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}
