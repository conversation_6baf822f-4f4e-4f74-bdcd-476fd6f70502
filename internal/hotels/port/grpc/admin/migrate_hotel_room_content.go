package admin

import (
	"context"
	"log"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

// AggregateHotelContent nhận và xử lý dữ liệu JSON từ client
func (s *AdminServer) MigrateHotelNameContent(ctx context.Context, input *pb.MigrateHotelNameContentReq) (*base.BasicRes, error) {
	cmdReq := &command.MigrateData{}

	err := s.app.Commands.AggregateHotelContentHandler.Handle(ctx, &command.AggregateHotelContentReq{
		Migrate:  cmdReq,
		Provider: commonEnum.HotelProvider(input.Provider),
	})
	if err != nil {
		log.Println("Error processing ", err)
		return nil, err
	}

	return nil, nil
}
