package admin

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"sync"
	"time"

	"gitlab.deepgate.io/apps/api/gen/go/base"
	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

// AggregateHotelContentReview nhận và xử lý dữ liệu JSON từ client.
func (s *AdminServer) AggregateHotelReview(stream pb.HotelAdminService_AggregateHotelReviewServer) error {
	var wg sync.WaitGroup
	errChan := make(chan error, 1)
	concurrency := 30 // Số lượng goroutine tối đa

	// Semaphore giới hạn số lượng goroutine đồng thời
	semaphore := make(chan struct{}, concurrency)

	prefixPool := make(chan int, concurrency)
	for i := 0; i < concurrency; i++ {
		prefixPool <- i // Đưa các giá trị prefix vào pool
	}

	for { // i là prefix đại diện cho mỗi goroutine
		req, err := stream.Recv()
		if errors.Is(err, io.EOF) {
			// Kết thúc stream
			break
		}

		if err != nil {
			return fmt.Errorf("error receiving data: %w", err)
		}

		var cmdReq *command.AggregateHotelContentReviewReq

		switch req.Provider {
		case int32(commonEnum.HotelProviderRateHawk):
			// Giải mã JSON vào struct domain.RateHawkHotelContent
			var property *domain.RateHawkHotelReview
			if err := json.Unmarshal(req.Content, &property); err != nil {
				log.Printf("Error unmarshalling JSON %s: %v", string(req.Content), err)
				// Gửi thông báo thất bại và kết thúc stream
				return stream.SendAndClose(&base.BasicRes{})
			}

			fmt.Println("receive hotel review of", property.PropertyID)
			cmdReq = &command.AggregateHotelContentReviewReq{
				ContentVersion:  "1",
				Provider:        commonEnum.HotelProviderRateHawk,
				RateHawkContent: property,
			}
		}

		if cmdReq == nil {
			log.Printf("error receiving non support provider: %d", req.Provider)
			return stream.SendAndClose(&base.BasicRes{})
		}

		wg.Add(1)
		semaphore <- struct{}{} // Chặn khi số lượng goroutine đạt tối đa
		prefix := <-prefixPool
		// Truyền giá trị prefix (i) vào hàm Handle
		go func(cmdReq *command.AggregateHotelContentReviewReq, content []byte, prefix int) {
			cmdReq.PrefixNumber = prefix

			defer wg.Done() // Đảm bảo wg.Done() được gọi khi goroutine hoàn thành
			defer func() {
				fmt.Println("prefix done")
				<-semaphore
				prefixPool <- prefix
			}() // Giải phóng semaphore khi xong việc

			ctx, ctxCancel := context.WithTimeout(context.Background(), 100*time.Minute)
			defer ctxCancel()

			// Truyền prefix vào Handle
			err := s.app.Commands.AggregateHotelContentReviewHandler.Handle(ctx, cmdReq)
			if err != nil {
				log.Printf("Error processing property with prefix %d", prefix)
				// Gửi phản hồi lỗi cho stream
				if errSend := stream.SendMsg(&pb.AggregateHotelContentRes{
					IsSuccess: false,
					Content:   content,
				}); errSend != nil {
					errChan <- fmt.Errorf("error sending error response: %w", errSend)
					return
				}
				errChan <- err
			}
		}(cmdReq, req.Content, prefix) // Truyền i (prefix) vào đây
	}

	// Chờ tất cả goroutine hoàn thành
	wg.Wait()
	close(errChan)

	// Kiểm tra và trả về lỗi nếu có
	for err := range errChan {
		if err != nil {
			return err
		}
	}

	return nil
}
