package admin

import (
	"context"

	pb "gitlab.deepgate.io/apps/api/gen/go/skyhub/admin"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
)

// AggregateHotelContent nhận và xử lý dữ liệu JSON từ client.
func (s *AdminServer) AggregateTourmindContent(ctx context.Context, req *pb.AggregateTourmindContentReq) (*pb.AggregateTourmindContentRes, error) {
	cmdReq := &command.AggregateTourmindContentReq{
		CountryCode: req.CountryCode,
		ForceInit:   req.ForceInit,
		BatchSize:   int(req.BatchSize),
	}

	res, err := s.app.Commands.AggregateTourmindContentHandler.Handle(ctx, cmdReq)
	if err != nil {
		return &pb.AggregateTourmindContentRes{
			Success:   false,
			ErrorCode: err.<PERSON><PERSON><PERSON>(),
		}, nil
	}

	return &pb.AggregateTourmindContentRes{
		Success:      true,
		MappedHotels: int32(res.MappedHotels),
		TotalHotels:  int32(res.TotalHotels),
	}, nil
}
