package http

import (
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"

	commonEnum "gitlab.deepgate.io/apps/common/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func ValidateRoomInfoArray(occupancies []*domain.HubSearchOccupancy) error {
	seenIndexes := make(map[uint]bool)

	for _, occupancy := range occupancies {
		if occupancy.Rooms <= 0 {
			return domain.ErrInvalidOccupancy
		}

		if occupancy.OccupancyIndex == 0 {
			return errors.WithMsg(domain.ErrInvalidOccupancy, "INDEX_NONZERO")
		}

		if seenIndexes[occupancy.OccupancyIndex] {
			return errors.WithMsg(domain.ErrInvalidOccupancy, "DUPLICATED_OCCUPANCY_INDEX")
		}

		for i := uint(0); i < occupancy.Rooms; i++ {
			seenIndexes[occupancy.OccupancyIndex+i] = true
		}
	}

	return nil
}

func ValidateRateDataGroup(rateDataGroups []enum.RateDataGroup) error {
	if len(rateDataGroups) == 0 {
		return nil
	}

	for _, rateDataGroup := range rateDataGroups {
		if _, ok := enum.RateDataGroupOrder[rateDataGroup]; !ok {
			return errors.WithMsg(errors.ErrInvalidInput, "INVALID_RATE_DATA_GROUP_NAME")
		}
	}

	return nil
}

// TODO validate language + currency.
func validateSearchRequest(req *domain.HubCheckAvailabilityReq) error {
	if req == nil {
		return errors.ErrInvalidInput
	}

	if len(req.ListHotels) > 1 {
		return errors.WithMsg(errors.ErrInvalidInput, "not support multi hotel request")
	}

	if req.Language != "" && !constants.IsSupportedLanguage(req.Language) {
		return domain.ErrUnSupportLanguage
	}

	err := ValidateRoomInfoArray(req.Occupancies)
	if err != nil {
		return err
	}

	err = validateOccupancy(req.Occupancies)
	if err != nil {
		return err
	}

	err = validateStay(req.Stay)
	if err != nil {
		return err
	}

	err = ValidateRateDataGroup(req.GroupBy)
	if err != nil {
		return err
	}

	return nil
}

func (s *Server) CheckAvailability(c echo.Context) error {
	req := &domain.HubCheckAvailabilityReq{
		OfficeID: s.getOfficeID(c),
		// EndUserIPAddress:    c.Request().Header.Get(constants.HeaderKeyClientIP),
		// EndUserBrowserAgent: c.Request().Header.Get(constants.HeaderKeyClientBrowserAgent),
	}

	authUser, ok := auth.GetContextUserFromEcho(c)
	if !ok {
		return s.handleErr(errors.ErrPermissionDenied, c)
	}

	if val, ok := c.Get(constants.ContextEnabledProvidersKey).([]commonEnum.HotelProvider); ok {
		req.EnableProviders = val
	}

	if err := c.Bind(req); err != nil {               
		fmt.Println("bind", err)
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		fmt.Println("validate", err)
		return s.validateStructError(err, c)
	}

	err = validateSearchRequest(req)
	if err != nil {
		fmt.Println("validateSearchRequest", err)
		return s.handleErr(err, c)
	}
	req.PartnershipID = authUser.PartnershipId

	res, err := s.app.Commands.CheckAvailabilityHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)
}
