package http

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func (s *Server) PriceCheckHotels(c echo.Context) error {
	req := &domain.HubPriceCheckReq{
		OfficeID: s.getOfficeID(c),
	}

	if val, ok := c.Get(constants.ContextEnabledProvidersKey).([]commonEnum.HotelProvider); ok {
		req.EnableProviders = val
	}

	authUser, ok := auth.GetContextUserFromEcho(c)
	if !ok {
		return s.handleErr(errors.ErrPermissionDenied, c)
	}

	if err := c.Bind(req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		return s.validateStructError(err, c)
	}

	req.PartnershipID = authUser.PartnershipId

	res, err := s.app.Commands.PriceCheckHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)
}
