package http

import (
	"net/http"

	"github.com/labstack/echo/v4"
	commonError "gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/logger"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) AggregateSearchPlace(c echo.Context) error {
	req := &domain.AggregateReq{}

	if err := c.Bind(req); err != nil {
		logger.Error("Bind err", logger.Any("err", err))
		return s.handleErr(commonError.WithMsg(commonError.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		logger.Error("validate.Struct err", logger.Any("err", err))
		return s.validateStructError(err, c)
	}

	err = s.app.Commands.AggregatePlaceHandler.Handle(c.Request().Context(), req.CountryCode, false)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, nil)
}
