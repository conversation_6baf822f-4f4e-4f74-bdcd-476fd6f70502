package http

import (
	"context"
	"net/http"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

// SubHubProxyMiddleware xử lý SUB Hub mapping và validation.
func SubHubProxyMiddleware(cfg *config.Schema, partnerClient partner.PartnerClient) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Tạo correlation ID cho tracking
			correlationID := generateCorrelationID()
			c.Set(constants.CorrelationIDKey, correlationID)
			c.Response().Header().Set(constants.HeaderKeyCorrelationID, correlationID)

			// Check for SUB Hub key
			hubKey := c.Request().Header.Get(constants.HeaderHubKey)
			if hubKey == "" {
				// NORMAL FLOW - ZERO CHANGES
				return next(c)
			}
			// Handle SUB Hub mapping and validation only
			return handleSubHubMapping(c, hubKey, cfg, correlationID, next)
		}
	}
}

// handleSubHubMapping xử lý SUB Hub mapping và validation, sau đó chuyển cho service layer.
func handleSubHubMapping(c echo.Context, hubKey string, cfg *config.Schema, correlationID string, next echo.HandlerFunc) error {
	// 1. Validate SUB Hub mapping
	subHubMapping, exists := cfg.SubHubMappings[hubKey]
	if !exists {
		log.Warn("Invalid SUB Hub key",
			log.String("hub_key", hubKey),
			log.String("correlation_id", correlationID))

		return echo.NewHTTPError(http.StatusUnauthorized, "Invalid hub key")
	}

	// 2. Validate Hub credentials exist
	if err := subHubMapping.Validate(); err != nil {
		log.Error("Invalid SUB Hub mapping configuration",
			log.Any("error", err),
			log.String("hub_key", hubKey),
			log.String("correlation_id", correlationID))

		return echo.NewHTTPError(http.StatusUnauthorized, "Invalid hub key")
	}

	// 3. Extract client headers
	clientHeaders := &config.ClientHeaders{
		UserAgent: c.Request().Header.Get(constants.HeaderKeyClientBrowserAgent),
		IP:        c.Request().Header.Get(constants.HeaderKeyClientIP),
		Country:   c.Request().Header.Get(constants.HeaderKeyClientCountryCode),
	}

	// 4. Create SUB Hub context
	subHubContext := &config.SubHubContext{
		HubKey:        hubKey,
		SubHubMapping: &subHubMapping,
		CorrelationID: correlationID,
		ClientHeaders: clientHeaders,
	}

	// 5. Set context cho service layer sử dụng
	c.Set(constants.SubHubContextKey, subHubContext)
	c.Set(constants.IsSubHubRequestKey, true)

	// 6. Add context to request context (BOTH keys needed for service layer)
	ctx := context.WithValue(c.Request().Context(), constants.SubHubContextKey, subHubContext)
	ctx = context.WithValue(ctx, constants.IsSubHubRequestKey, true)
	c.SetRequest(c.Request().WithContext(ctx))

	c.Request().Header.Set(constants.HeaderKeySubHubPartnershipID, subHubMapping.SubHubPartnershipID)

	return next(c)
}

// Helper functions.
func generateCorrelationID() string {
	return uuid.New().String()
}
