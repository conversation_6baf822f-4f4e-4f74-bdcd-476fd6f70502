package http

import (
	"time"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/http_echo/routeutil"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s Server) Register(e *echo.Echo, cfg *config.Schema, partnerClient partner.PartnerClient, redis redis.IRedis, repo repositories.RequestRepository) {
	// Apply SUB Hub middleware first (before authentication)
	e.Use(SubHubProxyMiddleware(cfg, partnerClient))
	e.Use(LoggingMiddleware(repo))

	headerAuth := Auth(partnerClient)

	authHotelGroup := e.Group("", headerAuth)

	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathSearchDestinations, s.SearchDestinations, domain.SearchDestinationReq{}, domain.SearchDestinationRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathSearchHotels, s.SearchHotels, domain.HubSearchHotelRequest{}, domain.HubSearchHotelResponse{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathSearchHotelList, s.SearchHotelList, domain.HubSearchHotelRequest{}, domain.HubSearchHotelResponse{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathHotelReviews, s.GetHotelReviews, domain.HubHotelReviewReq{}, domain.HubHotelReviewRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathCheckAvailability, s.CheckAvailability, domain.HubCheckAvailabilityReq{}, domain.HubCheckAvailabilityRes{}, "", nil)

	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathTimeoutTest, s.TimeoutTest, domain.TimeoutTestRequest{}, domain.TimeoutTestResponse{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathPriceCheck, s.PriceCheckHotels, domain.HubPriceCheckReq{}, domain.HubPriceCheckRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathBookHotel, s.BookHotels, domain.HubBookReq{}, domain.HubBookRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "GET", constants.PathRetrieveBooking, s.RetrieveBooking, domain.HubRetrieveBookingReq{}, domain.HubRetrieveBookingRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathRetrieveBooking, s.RetrieveBooking, domain.HubRetrieveBookingReq{}, domain.HubRetrieveBookingRes{}, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathCancelBooking, s.CancelBooking, domain.HubCancelBookingReq{}, nil, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathCheckBookingCancelStatus, s.CheckBookingCancelStatus, domain.HubCancelBookingReq{}, nil, "", nil)
	routeutil.AddEndpoint(authHotelGroup, "POST", constants.PathGetHotelDetail, s.GetHotelDetail, domain.GetHotelDetailReq{}, domain.GetHotelDetailRes{}, "", nil, RateLimitMiddleware(redis, cfg.RateLimitMaxRequests, time.Duration(cfg.RateLimitExpire)*time.Second))

	internalGroup := e.Group("/internal")
	routeutil.AddEndpoint(internalGroup, "POST", "/aggregate-search-place", s.AggregateSearchPlace, nil, nil, "", nil)

	webhookGroup := e.Group("/webhook")
	routeutil.AddEndpoint(webhookGroup, "POST", "/platform/notifications", s.ExpediaWebhookNotification, domain.ExpediaWebhookNotificationRequest{}, domain.ErrorRes{}, "", nil)
	routeutil.AddEndpoint(webhookGroup, "POST", "/ta-booking", s.HandleTAWebhook, domain.TAWebhookMsg{}, domain.ErrorRes{}, "", nil)
}
