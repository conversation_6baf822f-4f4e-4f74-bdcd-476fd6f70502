package http

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/samber/lo"
	"gitlab.deepgate.io/apps/common/adapter/redis"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func Auth(partnerClient partner.PartnerClient) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			var err error

			// Check if this is a SUB Hub request
			isSubHubRequest := c.Get(constants.IsSubHubRequestKey)
			if isSubHubRequest != nil && isSubHubRequest.(bool) {
				// SUB Hub: Use client credentials + SUB Hub partnership ID
				clientOfficeID, clientAPIKey := c.Request().Header.Get(auth.OfficeIDHeaderKey), c.Request().Header.Get(auth.APIKeyHeader)
				subHubPartnershipID := c.Request().Header.Get(constants.HeaderKeySubHubPartnershipID)

				if clientOfficeID != "" && clientAPIKey != "" && subHubPartnershipID != "" {
					err = authSubHub(partnerClient, c, clientOfficeID, clientAPIKey, subHubPartnershipID)
				} else {
					return c.JSON(echo.ErrBadRequest.Code, domain.ErrorRes{
						IsSuccess: false,
						ErrorCode: errors.ErrPermissionDenied.Error(),
						ErrorMsg:  "Missing SUB Hub credentials.",
					})
				}
			} else {
				// Normal flow: Use existing logic
				officeID, apiKey, partnershipID := c.Request().Header.Get(auth.HubOfficeIDHeaderKey), c.Request().Header.Get(auth.HubAPIKeyHeader), c.Request().Header.Get(auth.PartnershipContextKey)
				if officeID != "" && apiKey != "" && partnershipID != "" {
					err = authMiddlewareV2(partnerClient, c)
				} else {
					err = authMiddlewareV1(partnerClient, c)
				}
			}

			if c.Response().Committed {
				return nil
			}

			if err != nil {
				return err
			}

			return next(c)
		}
	}
}

func authMiddlewareV1(partnerClient partner.PartnerClient, c echo.Context) error {
	officeID, apiKey := c.Request().Header.Get(auth.OfficeIDHeaderKey), c.Request().Header.Get(auth.APIKeyHeader)
	if officeID == "" || apiKey == "" {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
			ErrorMsg:  "Missing header authorization.",
		})
	}

	user, err := partnerClient.AuthByOffice(c.Request().Context(), &domain.LoginReq{
		OfficeID: officeID,
		APIKey:   apiKey,
	})
	if err != nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	if user == nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	partnerShop, err := partnerClient.GetOfficeInfo(c.Request().Context(), officeID, "")
	if err != nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	if partnerShop == nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	c = auth.SetContextUserFromEcho(c, auth.User{
		Id:            user.ID,
		Name:          user.Name,
		Email:         user.Email,
		PartnershipId: user.PartnershipID,
		PartnerShopId: user.PartnerShopID,
	})

	c.Set(constants.ContextWebhookKey, &domain.WebhookCfg{
		WebhookKey: user.WebhookCfg.WebhookKey,
		WebhookURLCfg: domain.WebhookURLCfg{
			Transaction:    user.WebhookCfg.WebhookURLCfg.Transaction,
			ConfirmationID: user.WebhookCfg.WebhookURLCfg.ConfirmationID,
		},
	})

	if partnerShop.Hotel != nil {
		enableProviders := lo.Filter(partnerShop.Hotel.ProviderConfigs, func(item *domain.ProviderConfig, _ int) bool { return item.Enable })

		c.Set(constants.ContextEnabledProvidersKey, lo.Map(enableProviders, func(item *domain.ProviderConfig, _ int) commonEnum.HotelProvider { return item.Provider }))
		c.Set(constants.ContextDefaultLanguage, partnerShop.Hotel.DefaultLanguage)

		if partnerShop.Hotel.PriceConfig == nil {
			return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: "MISSING_PARTNER_SHOP_CONFIG",
			})
		}

		c.Set(constants.ContextPriceConditionConfig, partnerShop.Hotel.PriceConfig)
	}

	return nil
}

// authSubHub handles authentication for SUB Hub requests using client credentials + SUB Hub partnership ID.
func authSubHub(partnerClient partner.PartnerClient, c echo.Context, clientOfficeID, clientAPIKey, subHubPartnershipID string) error {
	// Authenticate using client credentials (similar to authMiddlewareV1)
	user, err := partnerClient.AuthByOffice(c.Request().Context(), &domain.LoginReq{
		OfficeID:      clientOfficeID,
		APIKey:        clientAPIKey,
		PartnershipID: subHubPartnershipID,
	})
	if err != nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	if user == nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	// Get partner shop info using client office ID
	partnerShop, err := partnerClient.GetOfficeInfo(c.Request().Context(), clientOfficeID, subHubPartnershipID)
	if err != nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	if partnerShop == nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	// Set auth user with SUB Hub partnership ID
	c = auth.SetContextUserFromEcho(c, auth.User{
		Id:            user.ID,
		Name:          user.Name,
		Email:         user.Email,
		PartnershipId: subHubPartnershipID, // Use SUB Hub partnership ID
		PartnerShopId: user.PartnerShopID,
	})

	// Set webhook config
	c.Set(constants.ContextWebhookKey, &domain.WebhookCfg{
		WebhookKey: user.WebhookCfg.WebhookKey,
		WebhookURLCfg: domain.WebhookURLCfg{
			Transaction: user.WebhookCfg.WebhookURLCfg.Transaction,
		},
	})

	// Set provider configs and other settings
	if partnerShop.Hotel != nil {
		enableProviders := lo.Filter(partnerShop.Hotel.ProviderConfigs, func(item *domain.ProviderConfig, _ int) bool { return item.Enable })

		c.Set(constants.ContextEnabledProvidersKey, lo.Map(enableProviders, func(item *domain.ProviderConfig, _ int) commonEnum.HotelProvider { return item.Provider }))
		c.Set(constants.ContextDefaultLanguage, partnerShop.Hotel.DefaultLanguage)

		if partnerShop.Hotel.PriceConfig == nil {
			return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: "MISSING_PARTNER_SHOP_CONFIG",
			})
		}

		c.Set(constants.ContextPriceConditionConfig, partnerShop.Hotel.PriceConfig)
	}

	return nil
}

func authMiddlewareV2(partnerClient partner.PartnerClient, c echo.Context) error {
	// AUTH Agent
	officeID, apiKey, partnershipID := c.Request().Header.Get(auth.OfficeIDHeaderKey), c.Request().Header.Get(auth.APIKeyHeader), c.Request().Header.Get(auth.PartnershipContextKey)
	if officeID == "" || apiKey == "" {
		return c.JSON(echo.ErrBadRequest.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
			ErrorMsg:  "Missing header authorization.",
		})
	}

	user, err := partnerClient.AuthByOffice(c.Request().Context(), &domain.LoginReq{
		OfficeID:      officeID,
		APIKey:        apiKey,
		PartnershipID: partnershipID,
	})
	if err != nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	if user == nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	subPartnerShop, err := partnerClient.GetOfficeInfo(c.Request().Context(), officeID, partnershipID)
	if err != nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	if subPartnerShop == nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	// c = auth.SetContextUserFromEcho(c, auth.User{
	// 	Id:            user.ID,
	// 	Name:          user.Name,
	// 	Email:         user.Email,
	// 	PartnershipId: user.PartnershipID,
	// 	PartnerShopId: user.PartnerShopID,
	// })

	// c.Set(constants.ContextWebhookKey, &domain.WebhookCfg{
	// 	WebhookKey: user.WebhookCfg.WebhookKey,
	// 	WebhookURLCfg: domain.WebhookURLCfg{
	// 		Transaction: user.WebhookCfg.WebhookURLCfg.Transaction,
	// 	},
	// })

	if subPartnerShop.Hotel != nil {
		// c.Set(constants.AgentEnabledProvidersKey, partnerShop.Hotel.ProviderConfigs)
		c.Set(constants.ContextDefaultLanguage, subPartnerShop.Hotel.DefaultLanguage)
	}

	// Auth For HUB
	hubOfficeID, hubApiKey := c.Request().Header.Get(auth.HubOfficeIDHeaderKey), c.Request().Header.Get(auth.HubAPIKeyHeader)

	if officeID == "" || apiKey == "" {
		return c.JSON(echo.ErrBadRequest.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
			ErrorMsg:  "Missing header authorization.",
		})
	}

	user, err = partnerClient.AuthByOffice(c.Request().Context(), &domain.LoginReq{
		OfficeID: hubOfficeID,
		APIKey:   hubApiKey,
	})
	if err != nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	if user == nil {
		return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrPermissionDenied.Error(),
		})
	}

	hubPartnerShop, err := partnerClient.GetOfficeInfo(c.Request().Context(), hubOfficeID, "")
	if err != nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	if hubPartnerShop == nil {
		return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
			IsSuccess: false,
			ErrorCode: errors.ErrNotFound.Error(),
		})
	}

	c = auth.SetContextUserFromEcho(c, auth.User{
		Id:            user.ID,
		Name:          user.Name,
		Email:         user.Email,
		PartnershipId: user.PartnershipID,
		PartnerShopId: user.PartnerShopID,
	})

	c.Set(constants.ContextWebhookKey, &domain.WebhookCfg{
		WebhookKey: user.WebhookCfg.WebhookKey,
		WebhookURLCfg: domain.WebhookURLCfg{
			Transaction:    user.WebhookCfg.WebhookURLCfg.Transaction,
			ConfirmationID: user.WebhookCfg.WebhookURLCfg.ConfirmationID,
		},
	})

	if subPartnerShop.Hotel != nil {
		enableProviders := lo.Filter(subPartnerShop.Hotel.ProviderConfigs, func(item *domain.ProviderConfig, _ int) bool { return item.Enable })

		if hubPartnerShop.Hotel != nil {
			hubEnableProviders := lo.Filter(hubPartnerShop.Hotel.ProviderConfigs, func(item *domain.ProviderConfig, _ int) bool { return item.Enable })
			enableProviders = mergeProviderConfigs(hubEnableProviders, enableProviders)
		}

		c.Set(constants.ContextEnabledProvidersKey, lo.Map(enableProviders, func(item *domain.ProviderConfig, _ int) commonEnum.HotelProvider { return item.Provider }))
		c.Set(constants.ContextDefaultLanguage, subPartnerShop.Hotel.DefaultLanguage)

		if subPartnerShop.Hotel.PriceConfig == nil {
			return c.JSON(echo.ErrNotFound.Code, domain.ErrorRes{
				IsSuccess: false,
				ErrorCode: "MISSING_PARTNER_SHOP_CONFIG",
			})
		}

		c.Set(constants.ContextPriceConditionConfig, subPartnerShop.Hotel.PriceConfig)
	}

	return nil
}

func mergeProviderConfigs(primary, secondary []*domain.ProviderConfig) []*domain.ProviderConfig {
	providerMap := make(map[commonEnum.HotelProvider]*domain.ProviderConfig)

	for _, p := range secondary {
		providerMap[p.Provider] = p
	}

	result := make([]*domain.ProviderConfig, 0, len(providerMap))

	for _, p := range primary {
		if val, exists := providerMap[p.Provider]; exists {
			result = append(result, val)
		}
	}

	return result
}

func RateLimitMiddleware(redis redis.IRedis, maxReqs int64, timeExpire time.Duration) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			officeID := c.Request().Header.Get(auth.OfficeIDHeaderKey)
			if officeID == "" {
				return c.JSON(echo.ErrForbidden.Code, domain.ErrorRes{
					IsSuccess: false,
					ErrorCode: errors.ErrPermissionDenied.Error(),
					ErrorMsg:  "Missing header authorization.",
				})
			}

			var (
				key   = fmt.Sprintf("rate_limit:%s:%s", c.Path(), officeID)
				count = maxReqs - 1
			)

			ttl, err := redis.CMD().TTL(c.Request().Context(), key).Result()
			if err != nil {
				return c.JSON(echo.ErrInternalServerError.Code, domain.ErrorRes{
					IsSuccess: false,
					ErrorCode: errors.ErrInternalServer.Error(),
				})
			}

			if ttl <= 0 {
				err = redis.SetWithExpiration(key, maxReqs-1, timeExpire)
				if err != nil {
					return c.JSON(echo.ErrInternalServerError.Code, domain.ErrorRes{
						IsSuccess: false,
						ErrorCode: errors.ErrInternalServer.Error(),
					})
				}
				ttl = timeExpire
			} else {
				count, err = redis.CMD().Decr(c.Request().Context(), key).Result()
				if err != nil {
					return c.JSON(echo.ErrInternalServerError.Code, domain.ErrorRes{
						IsSuccess: false,
						ErrorCode: errors.ErrInternalServer.Error(),
					})
				}

				if count < 0 {
					resetTime := time.Now().Add(ttl).Unix() // Tính thời gian reset

					c.Response().Header().Set(constants.HeaderKeyRateLimitLimit, strconv.Itoa(int(maxReqs)))
					c.Response().Header().Set(constants.HeaderKeyRateLimitRemaining, strconv.Itoa(0))
					c.Response().Header().Set(constants.HeaderKeyRateLimitReset, strconv.FormatInt(resetTime, 10))

					return c.JSON(echo.ErrTooManyRequests.Code, domain.ErrorRes{
						IsSuccess: false,
						ErrorCode: domain.ErrTooManyRequests.Error(),
					})
				}
			}

			resetTime := time.Now().Add(ttl).Unix()

			c.Response().Header().Set(constants.HeaderKeyRateLimitLimit, strconv.Itoa(int(maxReqs)))
			c.Response().Header().Set(constants.HeaderKeyRateLimitRemaining, strconv.Itoa(int(count)))
			c.Response().Header().Set(constants.HeaderKeyRateLimitReset, strconv.FormatInt(resetTime, 10))

			return next(c)
		}
	}
}

func LoggingMiddleware(repo repositories.RequestRepository) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			start := time.Now()

			// Log request
			reqBody := new(bytes.Buffer)
			if c.Request().Body != nil {
				_, _ = io.Copy(reqBody, c.Request().Body)
				c.Request().Body = io.NopCloser(bytes.NewBuffer(reqBody.Bytes()))
			}

			requestLog := &repositories.Request{
				RequestID: c.Request().Header.Get("X-Request-ID"),
				Path:      c.Request().URL.Path,
				Method:    c.Request().Method,
				Body:      reqBody.String(),
				Headers:   convertHeaders(c.Request().Header),
				TracingID: c.Request().Header.Get("X-Office-Id"),
				Action:    "API Request",
				IsJson:    true,
			}

			// Remove API key
			requestLog.Headers["X-Api-Key"] = ""
			requestLog.Headers["X-Hub-Api-Key"] = ""

			// Process request
			res := new(bytes.Buffer)
			c.Response().Writer = &responseWriter{Writer: res, ResponseWriter: c.Response().Writer}
			err := next(c)

			// Log response
			requestLog.Response = res.Bytes()
			requestLog.StatusCode = c.Response().Status
			requestLog.Duration = time.Since(start).Milliseconds()

			// Save log to database using the separate context
			go func() {
				if requestLog.Path == "/healthz" {
					return
				}
				logCtx, cc := context.WithTimeout(context.Background(), constants.GoroutineContextTimeout)
				defer cc()
				_ = repo.Create(logCtx, requestLog)
			}()

			return err
		}
	}
}

type responseWriter struct {
	Writer         io.Writer
	ResponseWriter http.ResponseWriter
}

func (rw *responseWriter) Header() http.Header {
	return rw.ResponseWriter.Header()
}

func (rw *responseWriter) Write(data []byte) (int, error) {
	rw.Writer.Write(data)
	return rw.ResponseWriter.Write(data)
}

func (rw *responseWriter) WriteHeader(statusCode int) {
	rw.ResponseWriter.WriteHeader(statusCode)
}

func convertHeaders(headers http.Header) map[string]string {
	converted := make(map[string]string)
	for key, values := range headers {
		converted[key] = values[0] // Take the first value for each header key
	}
	return converted
}
