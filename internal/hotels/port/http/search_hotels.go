package http

import (
	"fmt"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/apps/common/log"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func validateOccupancy(occupancies []*domain.HubSearchOccupancy) error {
	roomCount := 0

	for _, room := range occupancies {
		if room.Rooms != 1 {
			return domain.ErrInvalidRequestRoomAmount
		}

		roomCount += int(room.Rooms)

		if room.Adults == 0 {
			return domain.ErrInvalidOccupancy
		}

		if room.Children != nil {
			if int(room.Children.Number) != len(room.Children.Age) {
				return domain.ErrInvalidChildAge
			}

			for _, age := range room.Children.Age {
				if age > constants.MaxChildrenAge {
					return domain.ErrInvalidChildAge
				}
			}
		}
	}

	if roomCount > constants.MaxRoomSearchAllowed {
		return domain.ErrInvalidRequestRoomAmount
	}

	return nil
}

func validateStay(stay domain.HubSearchStay) error {
	checkIn, err := time.Parse(constants.HubSearchDateFormat, stay.CheckIn)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-in", stay.CheckIn))
		return domain.ErrCheckInDateInvalid
	}

	checkOut, err := time.Parse(constants.HubSearchDateFormat, stay.CheckOut)
	if err != nil {
		log.Error("parse time err", log.Any("err", err), log.String("check-out", stay.CheckOut))
		return domain.ErrCheckOutDateInvalid
	}

	limitDate := time.Now().Add(constants.HotelLimitDay) // 1 year

	if checkIn.Before(time.Now().Add(-24*time.Hour)) || checkIn.After(limitDate) {
		return domain.ErrCheckInDateInvalid
	}

	if checkOut.Before(checkIn) || checkOut.Before(time.Now()) || checkOut.After(limitDate) {
		return domain.ErrCheckOutDateInvalid
	}

	if checkOut.Sub(checkIn) > constants.HotelStayDayRange {
		return domain.ErrCheckOutDateInvalid
	}

	return nil
}

func validatePlace(place *domain.Place) error {
	if place == nil {
		return domain.ErrPlaceRequired
	}

	return nil
}

// TODO validate language + currency.
func validateSearchHotelRequest(req *domain.HubSearchHotelRequest) error {
	if req == nil {
		return errors.ErrInvalidInput
	}

	if req.Language != "" && !constants.IsSupportedLanguage(req.Language) {
		return domain.ErrUnSupportLanguage
	}

	err := validateOccupancy(req.Occupancies)
	if err != nil {
		return err
	}

	err = validateStay(req.Stay)
	if err != nil {
		return err
	}

	err = validatePlace(req.Place)
	if err != nil {
		return err
	}

	return nil
}

func (s *Server) SearchHotels(c echo.Context) error {
	req := &domain.HubSearchHotelRequest{
		OfficeID: s.getOfficeID(c),
		// EndUserIPAddress:    c.Request().Header.Get(constants.HeaderKeyClientIP),
		// EndUserBrowserAgent: c.Request().Header.Get(constants.HeaderKeyClientBrowserAgent),
	}

	if val, ok := c.Get(constants.ContextEnabledProvidersKey).([]commonEnum.HotelProvider); ok {
		req.EnableProviders = val
	}

	if val, ok := c.Get(constants.ContextDefaultLanguage).(string); ok {
		req.DefaultLanguage = val
	}

	if val, ok := c.Get(constants.ContextPriceConditionConfig).(*domain.HotelPriceConditionConfig); ok {
		req.PriceConditionConfig = val
	}

	authUser, ok := auth.GetContextUserFromEcho(c)
	if !ok {
		return s.handleErr(errors.ErrPermissionDenied, c)
	}
	req.PartnershipID = authUser.PartnershipId

	if err := c.Bind(req); err != nil {
		fmt.Println("bind", err)
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		fmt.Println("validate", err)
		return s.validateStructError(err, c)
	}

	err = validateSearchHotelRequest(req)
	if err != nil {
		fmt.Println("validateSearchHotelRequest", err)
		return s.handleErr(err, c)
	}

	res, err := s.app.Commands.SearchHotelsHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)
}
