package http

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) RetrieveBooking(c echo.Context) error {
	req := &domain.HubRetrieveBookingReq{}

	if err := c.Bind(req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}
	req.OfficeID = s.getOfficeID(c)

	// Set PartnershipID từ auth user
	authUser, ok := auth.GetContextUserFromEcho(c)
	if ok {
		req.PartnershipID = authUser.PartnershipId
	}

	err := validate.Struct(req)
	if err != nil {
		return s.validateStructError(err, c)
	}

	res, err := s.app.Queries.RetrieveBookingHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)
}
