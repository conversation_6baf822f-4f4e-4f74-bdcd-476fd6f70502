package http

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
)

func (s *Server) ExpediaWebhookNotification(c echo.Context) error {
	req := &domain.ExpediaWebhookNotificationRequest{}

	if err := c.Bind(req); err != nil {
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}
	headerAuth := c.Request().Header.Get("Authorization")

	apiKey, signature, timestamp, err := ParseAuthorizationHeader(headerAuth)
	if err != nil {
		return s.handleErr(err, c)
	}

	err = validate.Struct(req)
	if err != nil {
		return s.validateStructError(err, c)
	}

	req.ExpediaWebhookHeader = &domain.ExpediaWebhookHeader{
		APIKey:    apiKey,
		Signature: signature,
		Timestamp: timestamp,
	}

	err = s.app.Commands.ExpediaWebhookNotificationHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, &domain.ErrorRes{
		IsSuccess: true,
		ErrorCode: "",
		ErrorMsg:  "",
	})
}

func ParseAuthorizationHeader(authHeader string) (apiKey, signature string, timestamp int64, err error) {
	const prefix = "EAN "
	if !strings.HasPrefix(authHeader, prefix) {
		return "", "", 0, errors.New(errors.BadRequest, "invalid header format: missing EAN prefix")
	}

	// Remove the "EAN " prefix
	authHeader = strings.TrimPrefix(authHeader, prefix)

	// Split the header into key-value pairs using commas
	parts := strings.Split(authHeader, ",")
	if len(parts) != 3 {
		return "", "", 0, errors.New(errors.BadRequest, "invalid header format: expected 3 parts")
	}

	// Parse key-value pairs into a map
	values := make(map[string]string)

	for _, part := range parts {
		// Split each key-value pair using '='
		keyValue := strings.SplitN(strings.TrimSpace(part), "=", 2)
		if len(keyValue) != 2 {
			return "", "", 0, errors.New(errors.BadRequest, "invalid header format: malformed key-value pair")
		}
		key := keyValue[0]
		value := keyValue[1]
		values[key] = value
	}

	// Extract required fields
	apiKey, ok1 := values["apikey"]
	signature, ok2 := values["signature"]
	timestampStr, ok3 := values["timestamp"]

	if !ok1 || !ok2 || !ok3 {
		return "", "", 0, errors.New(errors.BadRequest, "missing required fields: APIKey, Signature, or timestamp")
	}

	// Convert timestamp to int64
	timestamp, err = strconv.ParseInt(timestampStr, 10, 64)
	if err != nil {
		return "", "", 0, errors.New(errors.BadRequest, "invalid timestamp: must be an integer")
	}

	return apiKey, signature, timestamp, nil
}
