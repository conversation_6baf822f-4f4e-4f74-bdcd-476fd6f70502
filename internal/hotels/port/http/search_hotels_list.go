package http

import (
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"gitlab.deepgate.io/apps/common/auth"
	"gitlab.deepgate.io/apps/common/errors"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/constants"
	commonEnum "gitlab.deepgate.io/apps/common/enum"
)

func validateHotelIds(hotelIds []string) error {
	if len(hotelIds) == 0 {
		return domain.ErrHotelIdsRequired
	}

	return nil
}

func validateHotelListRequest(req *domain.HubSearchHotelRequest) error {
	if req == nil {
		return errors.ErrInvalidInput
	}

	if req.Language != "" && !constants.IsSupportedLanguage(req.Language) {
		return domain.ErrUnSupportLanguage
	}

	err := validateGeneralOccupancy(req.GeneralOccupancy)
	if err != nil {
		return err
	}

	isGenOccupancy := req.CheckGeneralOccupancy()

	if !isGenOccupancy {
		err := validateOccupancy(req.Occupancies)
		if err != nil {
			return err
		}
	}

	err = validateStay(req.Stay)
	if err != nil {
		return err
	}

	err = validateHotelIds(req.HotelIds)
	if err != nil {
		return err
	}

	return nil
}

// Validate general occupancy.
func validateGeneralOccupancy(generalOccupancy *domain.HubSearchGeneralOccupancy) error {
	if generalOccupancy == nil {
		return nil
	}

	if generalOccupancy.Rooms > constants.MaxRoomSearchAllowed {
		return domain.ErrInvalidRequestRoomAmount
	}

	if generalOccupancy.Rooms > generalOccupancy.Adults {
		return domain.ErrRoomsExceedAdultsAmount
	}

	if generalOccupancy.Children != nil {
		childAgeLen := uint(len(generalOccupancy.Children.Age))
		if generalOccupancy.Children.Number != childAgeLen {
			return domain.ErrInvalidChildAge
		}
	}

	return nil
}

func (s *Server) SearchHotelList(c echo.Context) error {
	req := &domain.HubSearchHotelRequest{
		OfficeID: s.getOfficeID(c),
		// EndUserIPAddress:    c.Request().Header.Get(constants.HeaderKeyClientIP),
		// EndUserBrowserAgent: c.Request().Header.Get(constants.HeaderKeyClientBrowserAgent),
	}

	if val, ok := c.Get(constants.ContextEnabledProvidersKey).([]commonEnum.HotelProvider); ok {
		req.EnableProviders = val
	}

	if val, ok := c.Get(constants.ContextDefaultLanguage).(string); ok {
		req.DefaultLanguage = val
	}

	if val, ok := c.Get(constants.ContextPriceConditionConfig).(*domain.HotelPriceConditionConfig); ok {
		req.PriceConditionConfig = val
	}

	authUser, ok := auth.GetContextUserFromEcho(c)
	if !ok {
		return s.handleErr(errors.ErrPermissionDenied, c)
	}
	req.PartnershipID = authUser.PartnershipId

	if err := c.Bind(req); err != nil {
		fmt.Println("bind", err)
		return s.handleErr(errors.WithMsg(errors.ErrInvalidInput, constants.ErrMsgInvalidPayload), c)
	}

	err := validate.Struct(req)
	if err != nil {
		fmt.Println("validate", err)
		return s.validateStructError(err, c)
	}

	err = validateHotelListRequest(req)
	if err != nil {
		fmt.Println("validateHotelListRequest", err)
		return s.handleErr(err, c)
	}

	res, err := s.app.Commands.SearchHotelListHandler.Handle(c.Request().Context(), req)
	if err != nil {
		return s.handleErr(err, c)
	}

	return c.JSON(http.StatusOK, res)
}
