package background

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.deepgate.io/apps/common/log"
)

func (c *cronjob) registerProcessBookingConfirmationID(s *gocron.Scheduler, scheduled string) {
	const jobTimeout = time.Hour

	if scheduled == "" {
		scheduled = "0"
	}

	for _, hour := range strings.Split(scheduled, ",") {
		_, err := s.Cron(fmt.Sprintf("0 %s * * *", hour)).Do(func() {
			ctx, cancel := context.WithTimeout(context.Background(), jobTimeout)
			defer cancel()

			c.ProcessBookingConfirmationID(ctx)
		})
		if err != nil {
			log.Fatal("cronjob do error", log.Any("error", err))
			return
		}
	}
}

func (c *cronjob) ProcessBookingConfirmationID(ctx context.Context) {
	log.Info("[cronjob] start ProcessBookingConfirmationID...")

	err := c.app.Commands.ProcessBookingConfirmationIDHandler.Handle(ctx)
	if err != nil {
		return
	}
}
