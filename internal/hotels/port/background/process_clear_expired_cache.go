package background

import (
	"context"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.deepgate.io/apps/common/log"
)

func (c *cronjob) registerProcessClearExpiredCache(s *gocron.Scheduler) {
	const jobTimeout = time.Hour

	_, err := s.<PERSON><PERSON>("0 * * * *").Do(func() {
		ctx, cancel := context.WithTimeout(context.Background(), jobTimeout)
		defer cancel()

		c.ProcessClearExpiredCache(ctx)
	})
	if err != nil {
		log.Fatal("cronjob do error", log.Any("error", err))
		return
	}
}

func (c *cronjob) ProcessClearExpiredCache(ctx context.Context) {
	log.Info("[cronjob] start ProcessClearExpiredCache...")

	err := c.app.Commands.ClearExpiredCacheHandler.Handle(ctx)
	if err != nil {
		return
	}
}
