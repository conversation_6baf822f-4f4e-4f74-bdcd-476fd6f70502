package background

import (
	"context"
	"time"

	"github.com/go-co-op/gocron"
	commonDomain "gitlab.deepgate.io/apps/common/domain"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/domain"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/enum"
)

func (c *cronjob) registerProcessTicketPendingBooking(s *gocron.Scheduler, scheduled string) {
	const jobTimeout = time.Second * 100

	_, err := s.Cron(scheduled).Do(func() {
		ctx, cancel := context.WithTimeout(context.Background(), jobTimeout)
		defer cancel()

		c.processTicketPendingBooking(ctx)
	})
	if err != nil {
		log.Fatal("cronjob do error", log.Any("error", err))
		return
	}
}

func (c *cronjob) processTicketPendingBooking(ctx context.Context) {
	log.Info("[cronjob] start processTicketPendingBooking...")

	listReq := &domain.ListBookingRequest{
		Pagination: &commonDomain.Pagination{
			PageLimit:   100,
			PageCurrent: 1,
		},
		BookingStatus:    enum.BookingStatusPending,
		OrderKey:         "created_at",
		OrderStatus:      enum.HubOrderStatusConfirmed,
		OrderVal:         1,
		PendingStartAtLt: time.Now().UnixMilli(),
	}

	res, err := c.app.Queries.ListBookingHandler.Handle(ctx, listReq)
	if err != nil {
		return
	}

	processReq := &command.ProcessPendingBookingRequest{
		Bookings: res,
	}

	err = c.app.Commands.ProcessPendingBookingHandler.Handle(ctx, processReq)
	if err != nil {
		return
	}
}
