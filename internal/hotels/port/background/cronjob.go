package background

import (
	"strings"
	"time"

	"github.com/go-co-op/gocron"
	redislock "github.com/go-co-op/gocron-redis-lock"
	"github.com/redis/go-redis/v9"
	"gitlab.deepgate.io/apps/common/log"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
)

type cronjob struct {
	app app.Application
	cfg *config.Schema
}

type Cronjob interface {
	Run()
}

func NewBackgroundJob(cfg *config.Schema, application app.Application) Cronjob {
	return &cronjob{
		cfg: cfg,
		app: application,
	}
}

func (c *cronjob) Run() {
	var redisClientLock *redis.Client
	if c.cfg.RedisSentinel {
		redisClientLock = redis.NewFailoverClient(&redis.FailoverOptions{
			MasterName:       c.cfg.RedisSentinelMasterName,
			SentinelAddrs:    strings.Split(c.cfg.RedisAddress, ","),
			SentinelPassword: c.cfg.RedisPassword,
			Password:         c.cfg.RedisPassword,
			DB:               c.cfg.RedisDatabase,
		})
	} else {
		redisClientLock = redis.NewClient(&redis.Options{
			Addr:     c.cfg.RedisAddress,
			Password: c.cfg.RedisPassword,
			DB:       c.cfg.RedisDatabase,
		})
	}

	locker, err := redislock.NewRedisLocker(redisClientLock, redislock.WithTries(1))
	if err != nil {
		log.Fatal("cronjob NewRedisLocker error", log.Any("error", err))
	}

	s := gocron.NewScheduler(time.UTC)
	s.WithDistributedLocker(locker)

	if c.cfg.Env != "local" {
		c.registerProcessTicketPendingBooking(s, c.cfg.CronJobPendingBooking)
		c.registerProcessCancelingBooking(s, c.cfg.CronJobPendingBooking)
		c.registerProcessBookingConfirmationID(s, c.cfg.JobScanConfirmationIDSchedule)
		c.registerProcessClearExpiredCache(s)
	}

	s.StartAsync()
}
