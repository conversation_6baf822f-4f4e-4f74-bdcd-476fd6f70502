package hotels

import (
	"fmt"

	"gitlab.deepgate.io/apps/common/adapter/elasticsearch"
	commonMongoDB "gitlab.deepgate.io/apps/common/adapter/mongodb"
	"gitlab.deepgate.io/apps/common/adapter/rabbitmq"
	"gitlab.deepgate.io/apps/common/adapter/redis"

	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/agoda_client"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/hub_provider"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/mongodb/repositories"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partner"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/partnership"
	redisRepo "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/redis"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/adapter/webhook"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/command"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/background"
	grpcServer "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/grpc"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/http"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/port/mq"
	btm_client_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/btm_client"
	dida_client_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/dida"
	elastic_search_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/elastic_search"
	expedia_client_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/expedia_client"
	hub_provider_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/hub_provider"
	repositories_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/mongodb/repositories"
	mq_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/mq"
	notification_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/notification"
	order_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/order"
	payment_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/payment"
	price_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/price"
	rate_hawk_client_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/rate_hawk_client"
	ta_client_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/ta_client"
	tourmind_client_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/tourmind_client"
	travel_agent_client_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/travel_agent_client"
	wallet_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/adapter/wallet"
	command_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/app/command"
	query_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/app/query"
	service_tracing "gitlab.deepgate.io/skyhub/skyhub-hotels/internal/hotels/tracing/app/service"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/config"
	"gitlab.deepgate.io/skyhub/skyhub-hotels/pkg/helpers"
)

func New(cfg *config.Schema, db commonMongoDB.DB, commonDB commonMongoDB.DB, redisClient redis.IRedis, partnerClient partner.PartnerClient, es elasticsearch.ES, mqClient rabbitmq.MQ) (*http.Server, background.Cronjob, *grpcServer.Server, mq.Consumer) {
	// Mongo repo
	requestRepo := repositories_tracing.NewRequestRepository(db, cfg)
	currencyExRepo := repositories_tracing.NewCurrencyExchangeRepository(db)
	searchHotelRepo := repositories_tracing.NewSearchHotelCachesRepository(db)
	provideSearchHotelRepo := repositories_tracing.NewProviderSearchHotelsRepository(db)
	sessionRepo := repositories_tracing.NewSessionRepository(db)
	orderRepo := repositories_tracing.NewOrderRepository(db)
	areaHotelRepo := repositories_tracing.NewHotelAreaCacheRepository(db)
	expediaWebhookRepo := repositories_tracing.NewExpediaWebhookNotificationRepository(db)
	placeRepo := repositories_tracing.NewRequestPlaceRepository(db)

	// Common DB
	tourmindJobsQueueRepo := repositories_tracing.NewTourmindJobsQueueRepository(commonDB, cfg)
	tourmindRegionsRepo := repositories_tracing.NewTourmindRegionsRepository(commonDB, cfg)
	regionContentRepo := repositories_tracing.NewRegionRepository(commonDB, cfg)
	polygonContentRepo := repositories_tracing.NewPolygonRepository(commonDB, cfg)
	hotelContentRepo := repositories_tracing.NewHotelRepository(commonDB, cfg)
	roomContentRepo := repositories_tracing.NewRoomRepository(commonDB, cfg)
	hiddenFeeRepo := repositories_tracing.NewHiddenServiceFeeRepository(db)
	taMappingRepo := repositories_tracing.NewTAMappingsRepository(commonDB)
	contentAmenityRepo := repositories_tracing.NewAmenityRepository(commonDB)
	contentBedOptionRepo := repositories_tracing.NewBedOptionRepository(commonDB)
	promoHotelRepo := repositories_tracing.NewHotelPrioritiesRepository(db)
	displayImageRepo := repositories_tracing.NewDisplayImageRepository(commonDB)

	// Redis repo
	currencyExRedis := redisRepo.NewCurrencyExchangeRepository(redisClient)
	searchHotelRedis := redisRepo.NewSearchHotelsRepository(redisClient)
	bookingRedis := redisRepo.NewBookingRedisRepository(redisClient)
	checkAvailabilityRedis := redisRepo.NewCheckAvailabilityRepository(redisClient)
	hiddenFeeRedis := redisRepo.NewHiddenServiceFeeRepository(redisClient)
	cancelRedis := redisRepo.NewCancelRedisRepository(redisClient)
	priceCheckRedis := redisRepo.NewPriceCheckRepository(redisClient)

	// Grpc clients
	orderClient := order_tracing.NewOrderServiceClient(cfg)
	walletClient := wallet_tracing.NewWalletClient(cfg)
	paymentClient := payment_tracing.NewPaymentClient(cfg)
	priceClient := price_tracing.NewPriceClient(cfg)
	notificationClient := notification_tracing.NewNotificationServiceClient(cfg)

	webhookClient := webhook.NewWebhookAdapter(cfg, requestRepo)

	// 3rd client
	expediaAdapter := expedia_client_tracing.NewExpediaAdapter(cfg, redisClient, requestRepo)
	tourmindAdapter := tourmind_client_tracing.NewTourmindAdapter(cfg, requestRepo)

	rateHawkAdapter := rate_hawk_client_tracing.NewRateHawkAdapter(cfg, requestRepo)
	btmAdapter := btm_client_tracing.NewBTMAdapter(cfg, requestRepo)
	didaAdapter := dida_client_tracing.NewAdapter(cfg, requestRepo)
	// Adapter, client & other stuff
	currencyExchangeService := service_tracing.NewCurrencyExchangeService(currencyExRepo, currencyExRedis)
	taAdapter := ta_client_tracing.NewTAAdapter(cfg, requestRepo, redisClient)
	mqAdapter := mq_tracing.NewAdapter(mqClient)
	travelAgentAdapter := travel_agent_client_tracing.NewTravelAgentAdapter(cfg, redisClient, requestRepo, contentAmenityRepo, contentBedOptionRepo)

	hiddenFeeService := service_tracing.NewHiddenFeeService(priceClient, hiddenFeeRedis)

	hubProviderClient := hub_provider.NewClient(cfg, requestRepo)

	agodaAdapter := agoda_client.NewAgodaAdapter(cfg, redisClient, requestRepo)
	hubAdapter := hub_provider_tracing.NewHubAdapter(hubProviderClient, cfg)
	inActiveHotelsRepo := repositories_tracing.NewInActiveHotelsRepository(db)
	clientBookingRepo := repositories_tracing.NewClientBookingRepository(db)

	sessionService := service_tracing.NewSessionService(sessionRepo)
	providerSearchHandler := service.NewProviderSearchHandler(expediaAdapter, tourmindAdapter, rateHawkAdapter, didaAdapter, travelAgentAdapter, agodaAdapter)
	searchHotelService := service_tracing.NewSearchHotelService(cfg, searchHotelRepo, searchHotelRedis, hotelContentRepo, roomContentRepo, regionContentRepo, expediaAdapter, rateHawkAdapter, didaAdapter, areaHotelRepo, currencyExchangeService, travelAgentAdapter, hiddenFeeService, hubAdapter, inActiveHotelsRepo, agodaAdapter, promoHotelRepo, placeRepo, displayImageRepo)
	checkAvailabilityService := service_tracing.NewCheckAvailabilityService(cfg, provideSearchHotelRepo, providerSearchHandler, checkAvailabilityRedis, hotelContentRepo, roomContentRepo, hubAdapter, hiddenFeeService)
	priceCheckService := service_tracing.NewPriceCheckService(cfg, provideSearchHotelRepo, sessionService, orderRepo, currencyExchangeService, hiddenFeeService, expediaAdapter, tourmindAdapter, rateHawkAdapter, taMappingRepo, taAdapter, didaAdapter, priceCheckRedis, travelAgentAdapter, hubAdapter, agodaAdapter)
	cancelService := service_tracing.NewCancelBookingService(cfg, expediaAdapter, rateHawkAdapter, cancelRedis, orderRepo, walletClient, paymentClient, partnerClient, webhookClient, didaAdapter, travelAgentAdapter, hubAdapter, agodaAdapter)
	bookingService := service_tracing.NewBookingService(cfg, bookingRedis, orderRepo, expediaAdapter, tourmindAdapter, rateHawkAdapter, taAdapter, didaAdapter, notificationClient, partnerClient, mqAdapter, cancelService, travelAgentAdapter, webhookClient, hubAdapter, checkAvailabilityRedis, provideSearchHotelRepo, searchHotelRepo, agodaAdapter)
	webhookService := service_tracing.NewWebhookService(cfg, orderRepo, btmAdapter)
	esRepo := elastic_search_tracing.NewPlaceElasticRepository(es)

	getReviewService := service_tracing.NewGetReviewService(cfg, expediaAdapter)
	syncContentRepo := repositories.NewSyncContentRepository(cfg)

	partnershipClient := partnership.NewPartnershipClient(cfg)

	// Commands & queries
	application := app.Application{
		Commands: app.Commands{
			AggregateHotelContentHandler:        command.NewAggregateHotelContentHandler(hotelContentRepo, roomContentRepo, syncContentRepo, displayImageRepo),
			AggregateRegionsContentHandler:      command_tracing.NewAggregateRegionsContentHandler(regionContentRepo, polygonContentRepo, expediaAdapter),
			AggregateHotelTransactionHandler:    command_tracing.NewAggregateHotelTransactionHandler(cfg, orderRepo, walletClient, partnerClient),
			PriceCheckHandler:                   command_tracing.NewPriceCheckHandler(priceCheckService, sessionService, checkAvailabilityService, searchHotelRedis),
			BookHotelHandler:                    command_tracing.NewBookHotelHandler(cfg, orderRepo, sessionRepo, bookingService, bookingRedis, orderClient, paymentClient, walletClient, partnerClient, webhookClient),
			ProcessPendingBookingHandler:        command_tracing.NewProcessPendingBookingHandler(orderRepo, sessionRepo, bookingService),
			AggregateTourmindContentHandler:     command_tracing.NewAggregateTourmindContentHandler(tourmindJobsQueueRepo, tourmindAdapter, hotelContentRepo, tourmindRegionsRepo),
			CheckAvailabilityHandler:            command_tracing.NewCheckAvailabilityHandler(cfg, checkAvailabilityService, currencyExchangeService, hiddenFeeService, inActiveHotelsRepo),
			SearchHotelsHandler:                 command_tracing.NewSearchHotelsHandler(cfg, searchHotelService, currencyExchangeService, promoHotelRepo),
			SearchHotelListHandler:              command_tracing.NewSearchHotelListHandler(cfg, searchHotelService, currencyExchangeService),
			UpdateOrderStatusHandler:            command_tracing.NewUpdateOrderStatusHandler(orderRepo, bookingService),
			SearchDestinationHandler:            command_tracing.NewSearchDestinationHandler(cfg, esRepo),
			AggregatePlaceHandler:               command.NewAggregatePlaceHandler(regionContentRepo, hotelContentRepo, inActiveHotelsRepo, esRepo),
			AggregateRegionContentHandler:       command_tracing.NewAggregateRegionContentHandler(regionContentRepo),
			AggregateHotelContentReviewHandler:  command.NewAggregateHotelContentReviewHandler(hotelContentRepo),
			ExpediaWebhookNotificationHandler:   command_tracing.NewExpediaWebhookNotificationHandler(cfg, orderRepo, expediaWebhookRepo, webhookService, notificationClient),
			GetHotelReview:                      command_tracing.NewGetReviewHandler(cfg, getReviewService),
			DeleteConfigHiddenFeeHandler:        command_tracing.NewDeleteConfigHiddenFeeHandler(hiddenFeeRepo),
			CreateConfigHiddenFeeHandler:        command_tracing.NewCreateConfigHiddenFeeHandler(hiddenFeeRepo),
			UpdateConfigHiddenFeeHandler:        command_tracing.NewUpdateConfigHiddenFeeHandler(hiddenFeeRepo),
			CancelBookingHandler:                command_tracing.NewCancelBookingHandler(cancelService),
			CheckBookingCancelStatusHandler:     command_tracing.NewCheckBookingCancelStatusHandler(cancelService, orderRepo),
			ProcessCancelingBookingHandler:      command_tracing.NewProcessCancelingBookingHandler(cfg, cancelService, orderRepo, notificationClient),
			TAWebHookHandler:                    command_tracing.NewTAWebhookHandler(orderRepo, taAdapter, mqAdapter),
			BookOldProviderHandler:              command_tracing.NewBookOldProviderHandler(priceCheckService, bookingService, orderRepo),
			ProcessBookingConfirmationIDHandler: command_tracing.NewProcessBookingConfirmationIDHandler(orderRepo, sessionRepo, bookingService),
			ClearExpiredCacheHandler:            command_tracing.NewClearExpiredCacheHandler(searchHotelRepo),
			UpdateClientBookingHandler:          command_tracing.NewUpdateClientBookingHandler(clientBookingRepo),
			CreateCurrencyExchangeHandler:       command_tracing.NewCreateCurrencyExchangeHandler(currencyExRepo),
			UpdateCurrencyExchangeHandler:       command_tracing.NewUpdateCurrencyExchangeHandler(currencyExRepo),
			ForceFailOrderHandler:               command_tracing.NewForceFailOrderHandler(orderRepo, bookingService),
		},
		Queries: app.Queries{
			GetHotelDetailHandler:        query_tracing.NewGetHotelDetailHandler(cfg, hotelContentRepo, roomContentRepo),
			RetrieveBookingHandler:       query_tracing.NewRetrieveBookingHandler(cfg, orderRepo),
			ListBookingHandler:           query_tracing.NewListBookingHandler(orderRepo),
			ListConfigHiddenFeeHandler:   query_tracing.NewListConfigHiddenFeeHandler(hiddenFeeRepo),
			DetailConfigHiddenFeeHandler: query_tracing.NewDetailConfigHiddenFeeHandler(hiddenFeeRepo),
			ListOrderByFilterHandler:     query_tracing.NewListOrderByFilterHandler(cfg, orderRepo, partnerClient, partnershipClient, clientBookingRepo),
			GetDetailOrderByIDHandler:    query_tracing.NewGetDetailOrderByIDHandler(cfg, orderRepo, hotelContentRepo, clientBookingRepo),
			ListCurrencyExchangeHandler:  query_tracing.NewListCurrencyExchangeHandler(currencyExRepo),
			GetCurrencyExchangeHandler:   query_tracing.NewGetCurrencyExchangeDetailHandler(currencyExRepo),
		},
	}

	undefineDMethodCommand := helpers.CheckFieldsInitialized(application.Commands)
	if len(undefineDMethodCommand) > 0 {
		fmt.Println("undefineDMethodCommand", undefineDMethodCommand)
		panic(undefineDMethodCommand)
	}

	undefineDMethodQuery := helpers.CheckFieldsInitialized(application.Queries)
	if len(undefineDMethodQuery) > 0 {
		fmt.Println("undefineDMethodQuery", undefineDMethodQuery)
		panic(undefineDMethodQuery)
	}

	bgPort := background.NewBackgroundJob(cfg, application)

	return http.New(cfg, application), bgPort, grpcServer.NewServer(application, cfg), mq.NewConsumer(application)
}
